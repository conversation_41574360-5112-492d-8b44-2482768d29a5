package com.footway.inventory.mapping.handler;

import com.footway.fop.shared.client.merchant.MerchantClient;
import com.footway.inventory.model.dao.*;
import com.footway.inventory.model.dto.MerchantVariantSet;
import com.footway.inventory.model.dto.VariantSkuMap;
import com.footway.inventory.service.HandlerService;
import com.footway.inventory.service.InternalVariantService;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Slf4j
@Component
public class ProductMappingHandler {
    private final HandlerService handlerService;
    private final InternalVariantService internalVariantService;
    private final VariantSkuMap variantSkuMap;
    private final MerchantVariantSet mappedVariants;
    private final MerchantVariantSet mappedMscVariants;
    private final MerchantClient merchantClient;
    private final Map<String, List<String>> merchantOrgMerchantsMap;

    public ProductMappingHandler(HandlerService handlerService, InternalVariantService internalVariantService, MerchantClient merchantClient) {
        this.handlerService = handlerService;
        this.internalVariantService = internalVariantService;
        this.variantSkuMap = internalVariantService.getVariantSkuMap();
        this.mappedVariants = internalVariantService.getMappedVariants();
        this.mappedMscVariants = internalVariantService.getMappedMscVariants();
        this.merchantClient = merchantClient;
        this.merchantOrgMerchantsMap = merchantClient.getConsignmentMappingAll();
    }

    private static @NotNull MultiSalesChannelVariantMappingSuggested createMultiSalesChannelVariantMappingSuggested(String integrationVariantId, String merchantId, String footwayVariantId) {
        MultiSalesChannelVariantMappingSuggested multiSalesChannelVariantMappingSuggested = new MultiSalesChannelVariantMappingSuggested();
        multiSalesChannelVariantMappingSuggested.setMerchantId(merchantId);
        multiSalesChannelVariantMappingSuggested.setFootwayVariantId(footwayVariantId);
        multiSalesChannelVariantMappingSuggested.setIntegrationVariantId(integrationVariantId);
        multiSalesChannelVariantMappingSuggested.setStatus("suggested");
        return multiSalesChannelVariantMappingSuggested;
    }

    private static @NotNull VariantMappingSuggested createVariantMappingSuggested(String integrationVariantId, String merchantId, String footwayVariantId) {
        VariantMappingSuggested variantMappingSuggested = new VariantMappingSuggested();
        variantMappingSuggested.setMerchantId(merchantId);
        variantMappingSuggested.setFootwayVariantId(footwayVariantId);
        variantMappingSuggested.setIntegrationVariantId(integrationVariantId);
        variantMappingSuggested.setStatus("suggested");
        return variantMappingSuggested;
    }

    /**
     * Get a batch of external variants for processing
     * @param page The page number (0-based)
     * @param size The size of the batch
     * @return A list of external variants for the requested page
     */
    public List<ExternalVariant> getExternalVariantsBatch(int page, int size) {
        return handlerService.getExternalVariantsBatch(page, size);
    }

    public void processProductMappingSuggested(List<ExternalVariant> externalVariants) {
        try {
            // Parallel processing using parallelStream
            externalVariants.parallelStream()
                .filter(externalVariant -> {
                    String sku = externalVariant.getSku();
                    return sku != null && !sku.isEmpty();
                })
                .forEach(externalVariant -> {
                    try {
                        String sku = externalVariant.getSku();
                        String variantId = variantSkuMap.getBySku(sku);
                        if (variantId != null) {
                            processMscVariantMappingSuggested(externalVariant, variantId);
                            processVariantMappingSuggested(externalVariant, variantId);
                        }
                    } catch (Exception e) {
                        log.error("Error processing variant {}: {}", externalVariant.getId(), e.getMessage(), e);
                    }
                });
        } catch (Exception e) {
            log.error("Error processing msc or regular mapping suggested: {}", e.getMessage(), e);
        }
    }

    private void processMscVariantMappingSuggested(ExternalVariant externalVariant, String footwayVariantId) {
        try {
            String integrationVariantId = externalVariant.getId().getVariantId();
            String merchantId = externalVariant.getId().getMerchantId();

            if (!mappedMscVariants.has(merchantId, footwayVariantId)) {
                List<String> orgMerchants = merchantOrgMerchantsMap.get(merchantId);
                List<MscItemView> multiSalesChannelItem = handlerService.getMscItemViewByVariantIdAndMerchantIdNotContaining(footwayVariantId, orgMerchants);
                if (!multiSalesChannelItem.isEmpty()) {
                    MultiSalesChannelVariantMappingSuggested multiSalesChannelVariantMappingSuggested = createMultiSalesChannelVariantMappingSuggested(integrationVariantId, merchantId, footwayVariantId);
                    MultiSalesChannelVariantMappingSuggested existingMultiSalesChannelVariantMappingSuggested = handlerService.findMscVariantMappingSuggestedByMerchantIdAndFootwayVariantIdAndIntegrationVariantId(merchantId, footwayVariantId, integrationVariantId);
                    if (existingMultiSalesChannelVariantMappingSuggested == null) {
                        log.info("Create multi-sales channel variant mapping suggested between {} and {}", multiSalesChannelVariantMappingSuggested.getIntegrationVariantId(), multiSalesChannelVariantMappingSuggested.getFootwayVariantId());
                        handlerService.saveMscVariantMappingSuggested(multiSalesChannelVariantMappingSuggested);
                    }
                }
            }
        } catch (Exception e) {
            log.error("Error processing msc mapping suggested: {}", e.getMessage(), e);
        }
    }

    private void processVariantMappingSuggested(ExternalVariant externalVariant, String footwayVariantId) {
        try {
            String integrationVariantId = externalVariant.getId().getVariantId();
            String merchantId = externalVariant.getId().getMerchantId();
            if (!mappedVariants.has(merchantId, footwayVariantId)) {
                Optional<InventoryView> inventoryItem = handlerService.getInventoryViewByMerchantIdAndVariantId(merchantId, footwayVariantId);
                if (inventoryItem.isPresent()) {
                    VariantMappingSuggested variantMappingSuggested = createVariantMappingSuggested(integrationVariantId, merchantId, footwayVariantId);
                    VariantMappingSuggested existingVariantMappingSuggested = handlerService.findVariantMappingSuggestedByMerchantIdAndFootwayVariantIdAndIntegrationVariantId(merchantId, footwayVariantId, integrationVariantId);
                    if (existingVariantMappingSuggested == null) {
                        log.info("Create regular variant mapping suggested between {} and {}", variantMappingSuggested.getIntegrationVariantId(), variantMappingSuggested.getFootwayVariantId());
                        handlerService.saveVariantMappingSuggested(variantMappingSuggested);
                    }
                }
            }
        } catch (Exception e) {
            log.error("Error processing regular mapping suggested: {}", e.getMessage(), e);
        }
    }
}
