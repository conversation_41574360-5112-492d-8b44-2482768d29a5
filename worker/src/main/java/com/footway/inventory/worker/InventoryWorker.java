package com.footway.inventory.worker;

import com.footway.fop.shared.logging.LogContext;
import com.footway.fop.shared.webhook.configuration.CallbackConfig;
import com.footway.fop.shared.webhook.service.CallbackService;
import com.footway.inventory.component.CacheFiller;
import com.footway.inventory.service.CategoryService;
import com.footway.inventory.service.InternalVariantService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.WebApplicationType;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.web.reactive.ReactiveWebServerFactoryAutoConfiguration;
import org.springframework.boot.autoconfigure.web.servlet.ServletWebServerFactoryAutoConfiguration;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.FilterType;
import org.springframework.context.annotation.FullyQualifiedAnnotationBeanNameGenerator;
import java.time.Duration;
import java.time.Instant;
import java.util.List;

@Slf4j
@SpringBootApplication(
        nameGenerator = FullyQualifiedAnnotationBeanNameGenerator.class
)
@ComponentScan(
        basePackages = {"org.openapitools", "com.footway"},
        nameGenerator = FullyQualifiedAnnotationBeanNameGenerator.class,
        excludeFilters = @ComponentScan.Filter(type = FilterType.ASSIGNABLE_TYPE, classes = {
                ServletWebServerFactoryAutoConfiguration.class,
                ReactiveWebServerFactoryAutoConfiguration.class,
                CallbackConfig.class, CallbackService.class
        })
)
public class InventoryWorker implements CommandLineRunner {
    private final String workerSubscriptionId;
    private final String projectId = "footway-plus";
    private final InventoryPubSubMessageController inventoryPubSubMessageController;
    private final CacheFiller cacheFiller;
    private final CategoryService categoryService;
    private final InternalVariantService internalVariantService;

    public InventoryWorker(
            @Value("${fop-api.inventory-worker-subscription}") String workerSubscriptionId
            , InventoryPubSubMessageController inventoryPubSubMessageController
            , CacheFiller cacheFiller,
            @Value("${fop-api.inventory-service-url}") String inventoryServiceUrl,
            CategoryService categoryService,
            InternalVariantService internalVariantService) {
        this.workerSubscriptionId = workerSubscriptionId;
        this.inventoryPubSubMessageController = inventoryPubSubMessageController;
        this.cacheFiller = cacheFiller;
        this.categoryService = categoryService;
        this.internalVariantService = internalVariantService;
        log.info("project:{} subscriptionId:{}", projectId, workerSubscriptionId);
    }

    public static void main(String[] args) {
        SpringApplication app = new SpringApplication(InventoryWorker.class);
        app.setWebApplicationType(WebApplicationType.NONE); // Disables the web server
        app.run(args);
    }

    private static void delay(int seconds) {
        try {
            Thread.sleep(1000 * seconds);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public void run(String... args) throws Exception {
        Instant start = Instant.now();
        LogContext.setEnableMDC(false);

        cacheFiller.prefillCaches();
        log.info("Worker started with enums {} {} {}", categoryService.status("DEPARTMENT"), categoryService.status("TYPE"), categoryService.status("GROUP"));
        log.info("Refreshed batch of {}", refreshBatch());

        PubSubMessageConsumer receiver = new PubSubMessageConsumer(projectId, workerSubscriptionId, inventoryPubSubMessageController);

        try (receiver) {
            receiver.start();
            while (!receiver.isDone()) {
                Instant now = Instant.now();
                Duration duration = Duration.between(start, now);
                long minutes = duration.toMinutes();
                if (minutes > 7) {
                    log.warn("Starting shut down because duration is {}", minutes);
                    break;
                } else {
                    log.info("Continuing, because duration is {}", minutes);
                }
                delay(30);
            }
            receiver.stop(5);
        } catch (Exception e) {
            log.error(e.getMessage());
        }

        // TODO: on failure, manually send to DLQ! - today stuff just remain, but at least is not processed multiple times (so maybe no need for DLQ?)
        log.info("Worker done after processing {} messages with {} failed", receiver.getReceivedCount(), receiver.getFailedCount());
        System.exit(0);
    }


    public int refreshBatch() {
        List<String> variantIds = internalVariantService.getVariantIdOlderThan(72, 1024);
        for(String variantId:variantIds) {
            internalVariantService.getOrCreateVariant(variantId);
        }
        return variantIds.size();
    }
}
