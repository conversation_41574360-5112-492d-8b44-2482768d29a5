openapi: 3.0.0
info:
  title: Inventory API
  version: 1.0.0
  description: Inventory Service API
paths:

  /v1/variant/byId/{variantId}:
    get:
      summary: Retrieve variant by Variant ID
      description: Retrieve information for a specific variant.
      operationId: getVariantByVariantId
      parameters:
        - name: variantId
          description: The variant ID.
          in: path
          required: true
          schema:
            type: string  # You can adjust the type based on your specific requirements.
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Variant'
        '404':
          description: Variant not found
          content:
            application/json:
              example:
                message: "Variant with id {variantId} not found"
      tags:
        - inventory
      x-accepts: application/json
      x-tags:
        - inventory

  /v1/inventory/refresh:
    post:
      summary: Request prat to resend stale inventory
      description: Trigger prat to send inventory.
      operationId: syncInventoryOlderThan
      parameters:
        - name: hours
          description: Minimum age to consider resending
          in: query
          required: false
          schema:
            type: integer
            default: 12
        - name: limit
          description: Limits the request to process only specified quantity of variants.
          in: query
          required: false
          schema:
            type: integer
            default: 999999
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                type: integer
        '404':
          description: Merchant not found
          content:
            application/json:
              example:
                message: "Merchant not found"
      tags:
        - inventory
      x-accepts: application/json
      x-tags:
        - inventory
  /v1/inventory/{merchant_code}:
    get:
      summary: Retrieve minimal inventory & variant information for merchant
      description: Retrieve information for the inventory.
      operationId: getInventoryForMerchant
      parameters:
        - name: merchant_code
          description: ID of the merchant
          in: path
          required: true
          schema:
            type: string
        - in: query
          name: page
          description: page number. starts a 0
          required: true
          schema:
            type: integer
        - in: query
          name: size
          description: Size of each requested page
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InventoryImageResponse'
        '404':
          description: Merchant not found
          content:
            application/json:
              example:
                message: "Merchant not found"
      tags:
        - inventory
      x-accepts: application/json
      x-tags:
        - inventory
  /v1/inventory/v2/{merchant_code}:
    get:
      summary: Retrieve inventory & variant information for merchant
      description: Retrieve information for the inventory.
      operationId: getInventoryForMerchantPortalMerchant
      parameters:
        - name: merchant_code
          description: ID of the merchant
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/MerchantPortalVariantInventory'
        '404':
          description: Merchant not found
          content:
            application/json:
              example:
                message: "Merchant not found"
      tags:
        - inventory
      x-accepts: application/json
      x-tags:
        - inventory
    post:
      summary: Request prat to resend entire inventory for merchant
      description: Trigger prat to send inventory.
      operationId: syncInventoryForMerchantPortalMerchant
      parameters:
        - name: merchant_code
          description: ID of the merchant
          in: path
          required: true
          schema:
            type: string
        - name: limit
          description: Limits the request to process only specified quantity of variants.
          in: query
          required: false
          schema:
            type: integer
            default: 999999
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                type: integer
        '404':
          description: Merchant not found
          content:
            application/json:
              example:
                message: "Merchant not found"
      tags:
        - inventory
      x-accepts: application/json
      x-tags:
        - inventory
  /v1/inventory/v2/{merchant_code}/paginated:
    get:
      summary: Retrieve inventory & variant information for merchant
      description: Retrieve information for the inventory.
      operationId: getPaginatedInventoryForMerchantPortalMerchant
      parameters:
        - name: merchant_code
          description: ID of the merchant
          in: path
          required: true
          schema:
            type: string
        - in: query
          name: page
          description: Page number (1-based)
          required: false
          schema:
            type: integer
            minimum: 1
            default: 1
        - in: query
          name: pageSize
          description: Number of items per page
          required: false
          schema:
            type: integer
            minimum: 1
            maximum: 10000
            default: 20
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedMerchantPortalVariantInventoryResponse'
        '404':
          description: Merchant not found
          content:
            application/json:
              example:
                message: "Merchant not found"
      tags:
        - inventory
      x-accepts: application/json
      x-tags:
        - inventory
  /v1/inventory/v2/{merchant_code}/unidentified:
    get:
      summary: Retrieve unidentified inventory information for merchant
      description: Retrieve information for the inventory.
      operationId: getUnIdentifiedInventoryForMerchantPortalMerchant
      parameters:
        - name: merchant_code
          description: ID of the merchant
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MerchantPortalVariantUnidentifiedInventory'
        '404':
          description: Merchant not found
          content:
            application/json:
              example:
                message: "Merchant not found"
      tags:
        - inventory
      x-accepts: application/json
      x-tags:
        - inventory
  /v1/inventory/mapping/{merchant_code}:
    get:
      summary: Retrieve inventory and shopify mapping status information for merchant
      description: Retrieve information for the inventory shopify mapping status.
      operationId: getInventoryShopifyMappingStatusForMerchant
      parameters:
        - name: merchant_code
          description: ID of the merchant
          in: path
          required: true
          schema:
            type: string
        - name: page
          description: Page number (1-based)
          in: query
          required: false
          schema:
            type: integer
            minimum: 1
            default: 1
        - name: pageSize
          description: Number of items per page
          in: query
          required: false
          schema:
            type: integer
            minimum: 1
            maximum: 10000
            default: 20
        - name: filters
          in: query
          required: false
          schema:
            $ref: '#/components/schemas/Filters'
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/InventoryShopifyMappingItem'
        '404':
          description: Merchant not found
          content:
            application/json:
              example:
                message: "Merchant not found"
      security:
        - ApiKeyAuth: [ ]
      tags:
        - inventory
      x-accepts: application/json
      x-tags:
        - inventory
  /v1/inventory/storage-order-lines:
    get:
      summary: Retrieve storage order lines with inventory information
      description: Get a paginated list of storage order lines joined with their inventory information including product details and images.
      operationId: getStorageOrderLinesWithInventory
      parameters:
        - name: merchantId
          in: query
          required: true
          description: The ID of the merchant
          schema:
            type: string
        - name: page
          in: query
          description: Page number (1-based)
          required: false
          schema:
            type: integer
            minimum: 1
            default: 1
        - name: pageSize
          in: query
          description: Number of items per page
          required: false
          schema:
            type: integer
            minimum: 1
            maximum: 10000
            default: 20
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedStorageOrderLineInventoryResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              example:
                message: "Error fetching storage order lines with inventory"
      tags:
        - inventory
      x-accepts: application/json
      x-tags:
        - inventory
  /v1/purchaseOrderHead/{orderNumber}:
    get:
      summary: Retrieve purchase order by Order Number
      description: Retrieve purchase order based on the order number
      operationId: getPurchaseOrderHeadByOrderNumber
      parameters:
        - name: orderNumber
          description: The order number to filter results
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DynamicJsonObject'
      tags:
        - inventory
      x-accepts: application/json
      x-tags:
        - inventory
        - internal
  /v1/inventory/search:
    get:
      summary: Search inventory items
      description: Search inventory items with various filters. Multiple values can be provided for each filter type.
      operationId: searchInventory
      parameters:
        - name: merchantId
          in: query
          description: Filter by merchant ID (can specify multiple)
          required: false
          schema:
            type: array
            items:
              type: string
              minLength: 3
              maxLength: 3
          style: form
          explode: false
        - name: productName
          in: query
          description: Search by product name (uses trigram similarity)
          required: false
          schema:
            type: string
        - name: vendor
          in: query
          description: Filter by vendor (can specify multiple)
          required: false
          schema:
            type: array
            items:
              type: string
          style: form
          explode: false
        - name: department
          in: query
          description: Filter by department (can specify multiple)
          required: false
          schema:
            type: array
            items:
              type: string
          style: form
          explode: false
        - name: productGroup
          in: query
          description: Filter by product group (can specify multiple)
          required: false
          schema:
            type: array
            items:
              type: string
          style: form
          explode: false
        - name: productType
          in: query
          description: Filter by product type (can specify multiple)
          required: false
          schema:
            type: array
            items:
              type: string
          style: form
          explode: false
        - name: variantIds
          in: query
          description: Filter by variantIds (can specify multiple)
          required: false
          schema:
            type: array
            items:
              type: string
          style: form
          explode: false
        - name: searchText
          in: query
          description: General search text
          required: false
          schema:
            type: string
        - name: page
          in: query
          description: Page number (1-based)
          required: false
          schema:
            type: integer
            minimum: 1
            default: 1
        - name: pageSize
          in: query
          description: Number of items per page
          required: false
          schema:
            type: integer
            minimum: 1
            maximum: 10000
            default: 20
        - name: sortBy
          in: query
          description: Field to sort by
          required: false
          schema:
            type: string
        - name: sortDirection
          in: query
          description: Sort direction (asc or desc)
          required: false
          schema:
            type: string
            enum: [ asc, desc ]
            default: asc
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedInventoryResponse'
        '400':
          description: Invalid request parameters
        '500':
          description: Internal server error
      tags:
        - inventory
  /v1/inventory/search/internal:
    get:
      summary: Internal search inventory items
      description: Search inventory items with various filters. Multiple values can be provided for each filter type. Include sensitive fields like poprice
      operationId: searchInventoryInternal
      parameters:
        - name: merchantId
          in: query
          description: Filter by merchant ID (can specify multiple)
          required: false
          schema:
            type: array
            items:
              type: string
              minLength: 3
              maxLength: 3
          style: form
          explode: false
        - name: productName
          in: query
          description: Search by product name (uses trigram similarity)
          required: false
          schema:
            type: string
        - name: vendor
          in: query
          description: Filter by vendor (can specify multiple)
          required: false
          schema:
            type: array
            items:
              type: string
          style: form
          explode: false
        - name: department
          in: query
          description: Filter by department (can specify multiple)
          required: false
          schema:
            type: array
            items:
              type: string
          style: form
          explode: false
        - name: productGroup
          in: query
          description: Filter by product group (can specify multiple)
          required: false
          schema:
            type: array
            items:
              type: string
          style: form
          explode: false
        - name: productType
          in: query
          description: Filter by product type (can specify multiple)
          required: false
          schema:
            type: array
            items:
              type: string
          style: form
          explode: false
        - name: variantIds
          in: query
          description: Filter by variantIds (can specify multiple)
          required: false
          schema:
            type: array
            items:
              type: string
          style: form
          explode: false
        - name: searchText
          in: query
          description: General search text
          required: false
          schema:
            type: string
        - name: page
          in: query
          description: Page number (1-based)
          required: false
          schema:
            type: integer
            minimum: 1
            default: 1
        - name: pageSize
          in: query
          description: Number of items per page
          required: false
          schema:
            type: integer
            minimum: 1
            maximum: 10000
            default: 20
        - name: sortBy
          in: query
          description: Field to sort by
          required: false
          schema:
            type: string
        - name: sortDirection
          in: query
          description: Sort direction (asc or desc)
          required: false
          schema:
            type: string
            enum: [ asc, desc ]
            default: asc
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedInventoryResponse'
        '400':
          description: Invalid request parameters
        '500':
          description: Internal server error
      tags:
        - inventory
  /v1/inventory/availableFilters:
    get:
      summary: Get available filter options for inventory search
      description: Returns possible filter values based on current filter selection
      operationId: getAvailableFilters
      parameters:
        - name: merchantId
          in: query
          description: Filter by merchant ID (can specify multiple)
          required: false
          schema:
            type: array
            items:
              type: string
              minLength: 3
              maxLength: 3
          style: form
          explode: false
        - name: vendor
          in: query
          description: Filter by vendor (can specify multiple)
          required: false
          schema:
            type: array
            items:
              type: string
          style: form
          explode: false
        - name: department
          in: query
          description: Filter by department (can specify multiple)
          required: false
          schema:
            type: array
            items:
              type: string
          style: form
          explode: false
        - name: productGroup
          in: query
          description: Filter by product group (can specify multiple)
          required: false
          schema:
            type: array
            items:
              type: string
          style: form
          explode: false
        - name: productType
          in: query
          description: Filter by product type (can specify multiple)
          required: false
          schema:
            type: array
            items:
              type: string
          style: form
          explode: false
      responses:
        '200':
          description: Available filter options
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AvailableFilters'
        '400':
          description: Invalid request parameters
        '500':
          description: Internal server error
      tags:
        - inventory
  /v1/inventory/export:
    post:
      summary: Search inventory items
      description: Export the entire inventory for multiple merchants
      operationId: exportInventory
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                merchantIds:
                  type: array
                  items:
                    type: string
              required:
                - merchantIds
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/InventoryItem'
        '400':
          description: Invalid request parameters
        '500':
          description: Internal server error
      tags:
        - inventory
  /v1/inventory/{merchantId}/{variantId}:
    get:
      summary: Get inventory item by merchant ID and variant ID
      description: Retrieve a specific inventory item
      operationId: getInventoryItem
      parameters:
        - name: merchantId
          in: path
          required: true
          description: The merchant ID
          schema:
            type: string
            minLength: 3
            maxLength: 3
        - name: variantId
          in: path
          required: true
          description: The variant ID
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InventoryItem'
        '404':
          description: Inventory item not found
        '500':
          description: Internal server error
      tags:
        - inventory

  /v1/purchaseOrderLines/{orderNumber}:
    get:
      summary: Retrieve order lines by Order Number
      description: Retrieve order lines based on the order number.
      operationId: getPurchaseOrderLinesByOrderNumber
      parameters:
        - name: orderNumber
          description: The order number
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/DynamicJsonObject'
      tags:
        - inventory
      x-accepts: application/json
      x-tags:
        - inventory
        - internal


  /v1/multiSalesChannelItems:
    post:
      summary: Create a new MultiSalesChannel item
      operationId: createMultiSalesChannelItem
      tags:
        - multiSalesChannel
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MultiSalesChannelItemRequest'
      responses:
        '201':
          description: Created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MultiSalesChannelItemResponse'
    get:
      summary: Get all consignment items
      operationId: getAllMultiSalesChannelItems
      tags:
        - multiSalesChannel
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/MultiSalesChannelItemResponse'

  /v1/multiSalesChannelItems/{multiSalesChannelItemId}:
    get:
      summary: Get a MultiSalesChannel item by ID
      operationId: getConsignmentItemById
      tags:
        - multiSalesChannel
      parameters:
        - name: multiSalesChannelItemId
          in: path
          required: true
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MultiSalesChannelItemResponse'
    put:
      summary: Update a MultiSalesChannel item
      operationId: updateMultiSalesChannelItem
      tags:
        - multiSalesChannel
      parameters:
        - name: multiSalesChannelItemId
          in: path
          required: true
          schema:
            type: integer
            format: int64
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MultiSalesChannelItemRequest'
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MultiSalesChannelItemResponse'
    post:
      summary: Delete a MultiSalesChannel item
      operationId: deleteMultiSalesChannelItem
      tags:
        - multiSalesChannel
      parameters:
        - name: multiSalesChannelItemId
          in: path
          required: true
          schema:
            type: integer
            format: int64
      responses:
        '204':
          description: Successful operation
  /v1/multiSalesChannelItems/availableFilters:
    get:
      summary: Get available filter options for inventory search
      description: Returns possible filter values based on current filter selection
      operationId: getAvailableMscFilters
      parameters:
        - name: merchantId
          in: query
          description: Filter by merchant ID (can specify multiple)
          required: false
          schema:
            type: array
            items:
              type: string
              minLength: 3
              maxLength: 3
          style: form
          explode: false
        - name: vendor
          in: query
          description: Filter by vendor (can specify multiple)
          required: false
          schema:
            type: array
            items:
              type: string
          style: form
          explode: false
        - name: department
          in: query
          description: Filter by department (can specify multiple)
          required: false
          schema:
            type: array
            items:
              type: string
          style: form
          explode: false
        - name: productGroup
          in: query
          description: Filter by product group (can specify multiple)
          required: false
          schema:
            type: array
            items:
              type: string
          style: form
          explode: false
        - name: productType
          in: query
          description: Filter by product type (can specify multiple)
          required: false
          schema:
            type: array
            items:
              type: string
          style: form
          explode: false
      responses:
        '200':
          description: Available filter options
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AvailableFilters'
        '400':
          description: Invalid request parameters
        '500':
          description: Internal server error
      tags:
        - multiSalesChannel

  /v1/multiSalesChannelItems/search:
    get:
      summary: Search consignment items by merchant code and prat variant ID
      operationId: searchMultiSalesChannelItems
      tags:
        - multiSalesChannel
      parameters:
        - name: merchantCode
          in: query
          required: true
          schema:
            type: string
        - name: pratVariantId
          in: query
          schema:
            type: string
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/MultiSalesChannelItemResponse'

  /v1/multiSalesChannelItems/byPratVariantId/{pratVariantId}:
    get:
      summary: Find MultiSalesChannel items by Prat Variant ID
      operationId: findByPratVariantId
      tags:
        - multiSalesChannel
      parameters:
        - name: pratVariantId
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/MultiSalesChannelItemResponse'

  /v1/multiSalesChannelItems/withInventorySnapshot/{merchantCode}:
    get:
      summary: Query MultiSalesChannel items with inventory snapshot by merchant
      operationId: queryMultiSalesChannelItemsWithInventorySnapshotByMerchant
      tags:
        - multiSalesChannel
      parameters:
        - name: merchantCode
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/MultiSalesChannelItemSnapshotResponse'
  /v1/multiSalesChannelItems/withInventorySnapshot:
    get:
      summary: Get all MultiSalesChannel items with inventory snapshot paginated
      operationId: getMultiSalesChannelItemsWithInventorySnapshot
      tags:
        - multiSalesChannel
      parameters:
        - in: query
          name: page
          description: Page number (1-based)
          required: false
          schema:
            type: integer
            minimum: 1
            default: 1
        - in: query
          name: pageSize
          description: Number of items per page
          required: false
          schema:
            type: integer
            minimum: 1
            maximum: 10000
            default: 20
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedMultiSalesChannelItemSnapshotResponse'
  /v1/multiSalesChannelItems/upsert:
    post:
      summary: Create or update a MultiSalesChannel item
      operationId: upsertMultiSalesChannelItem
      tags:
        - multiSalesChannel
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MultiSalesChannelItemRequest'
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MultiSalesChannelItemResponse'
  /v1/multiSalesChannelItemVariants/search:
    get:
      summary: Internal search inventory items
      description: Search inventory items with various filters. Multiple values can be provided for each filter type. Include sensitive fields like poprice
      operationId: searchMscItems
      parameters:
        - name: merchantId
          in: query
          description: Filter by merchant ID (can specify multiple)
          required: false
          schema:
            type: array
            items:
              type: string
              minLength: 3
              maxLength: 3
          style: form
          explode: false
        - name: productName
          in: query
          description: Search by product name (uses trigram similarity)
          required: false
          schema:
            type: string
        - name: vendor
          in: query
          description: Filter by vendor (can specify multiple)
          required: false
          schema:
            type: array
            items:
              type: string
          style: form
          explode: false
        - name: department
          in: query
          description: Filter by department (can specify multiple)
          required: false
          schema:
            type: array
            items:
              type: string
          style: form
          explode: false
        - name: productGroup
          in: query
          description: Filter by product group (can specify multiple)
          required: false
          schema:
            type: array
            items:
              type: string
          style: form
          explode: false
        - name: productType
          in: query
          description: Filter by product type (can specify multiple)
          required: false
          schema:
            type: array
            items:
              type: string
          style: form
          explode: false
        - name: isInStock
          in: query
          description: Filter by in stock items
          required: false
          schema:
            type: boolean
        - name: page
          in: query
          description: Page number (1-based)
          required: false
          schema:
            type: integer
            minimum: 1
            default: 1
        - name: pageSize
          in: query
          description: Number of items per page
          required: false
          schema:
            type: integer
            minimum: 1
            maximum: 10000
            default: 20
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedInventoryResponse'
        '400':
          description: Invalid request parameters
        '500':
          description: Internal server error
      tags:
        - multiSalesChannel
  
  /v1/MultiSalesChannelVariantMapping:
    post:
      summary: Create a variant mapping
      operationId: createMultiSalesChannelVariantMapping
      x-google-quota:
        metricCosts:
          "request-count": 1
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateVariantMappingRequest'
      responses:
        '200':
          description: Variant mapping created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/VariantMapping'
        '400':
          description: Bad request
        '500':
          description: Internal server error
      security:
        - ApiKeyAuth: [ ]
      tags:
        - multiSalesChannel
      x-accepts: application/json
      x-content-type: application/json
  /v1/MultiSalesChannelVariantMapping/byMappingId/{mappingId}:
    get:
      summary: Retrieve a variant mapping by its ID
      operationId: getMultiSalesChannelVariantMapping
      x-google-quota:
        metricCosts:
          "request-count": 1
      parameters:
        - in: path
          name: mappingId
          description: The ID of the variant mapping
          required: true
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/VariantMapping'
        '404':
          description: Variant mapping not found
        '500':
          description: Internal server error
      security:
        - ApiKeyAuth: [ ]
      tags:
        - multiSalesChannel
    delete:
      summary: Remove a variant mapping by its ID
      operationId: removeMultiSalesChannelVariantMapping
      x-google-quota:
        metricCosts:
          "request-count": 1
      parameters:
        - in: path
          name: mappingId
          description: The ID of the variant mapping
          required: true
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: string
                example: "Variant mapping removed successfully"
        '404':
          description: Not found
          content:
            application/json:
              schema:
                type: string
                example: "Variant mapping not found"
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                type: string
                example: "Internal server error"
      security:
        - ApiKeyAuth: [ ]
      tags:
        - multiSalesChannel
      x-accepts: application/json
      x-content-type: application/json
  /v1/MultiSalesChannelVariantMappings/byMerchantCode/{merchantCode}:
    get:
      summary: Retrieve variant mappings by merchant code
      operationId: getMultiSalesChannelVariantMappingListByMerchantCode
      x-google-quota:
        metricCosts:
          "request-count": 1
      parameters:
        - in: path
          name: merchantCode
          description: The merchant code
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/VariantMapping'
        '404':
          description: Variant mappings not found for the specified merchant code
        '500':
          description: Internal server error
      security:
        - ApiKeyAuth: [ ]
      tags:
        - multiSalesChannel
  /v1/MultiSalesChannelVariantMapping/provisionForMerchant/{merchantCode}:
    put:
      summary: Provision variant mapping by merchant code (automatically suggested by integration)
      operationId: provisionMultiSalesChannelVariantMappingsForMerchant
      x-google-quota:
        metricCosts:
          "request-count": 1
      parameters:
        - in: path
          name: merchantCode
          description: The merchant code
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/VariantMapping'
        '404':
          description: specified merchant code not found
        '500':
          description: Internal server error
      security:
        - ApiKeyAuth: [ ]
      tags:
        - multiSalesChannel
      x-accepts: application/json
      x-content-type: application/json
  /v1/MultiSalesChannelVariantMapping/{merchantCode}:
    get:
      summary: Retrieve a variant mapping by merchant code and variant id
      operationId: getMultiSalesChannelVariantMappingByMerchantCodeAndVariantId
      x-google-quota:
        metricCosts:
          "request-count": 1
      parameters:
        - in: path
          name: merchantCode
          description: The merchant code
          required: true
          schema:
            type: string
        - in: query
          name: integrationVariantId
          description: The ID of the integration variant
          required: false
          schema:
            type: string
        - in: query
          name: footwayVariantId
          description: The ID of the Footway variant
          required: false
          schema:
            type: string
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/VariantMapping'
        '404':
          description: Variant mappings not found for the specified merchant code
        '500':
          description: Internal server error
      security:
        - ApiKeyAuth: [ ]
      tags:
        - multiSalesChannel
      x-accepts: application/json
      x-content-type: application/json
    put:
      summary: Update variant mappings for a merchant
      operationId: upsertMultiSalesChannelVariantMappings
      x-google-quota:
        metricCosts:
          "request-count": 1
      parameters:
        - in: path
          name: merchantCode
          description: The merchant code
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: array
              items:
                $ref: '#/components/schemas/VariantMappingUpdateModel'
      responses:
        '200':
          description: Variant mappings updated successfully
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/VariantMapping'
      security:
        - ApiKeyAuth: [ ]
      x-accepts: application/json
      x-content-type: application/json
      tags:
        - multiSalesChannel
  /v1/MultiSalesChannelVariantMapping/viewMappings:
    get:
      summary: View Multi Sales Channel Variant Mappings by merchantId
      description: Get a paginated list of multi sales channel variant mappings of products information both footway and integration, including image urls.
      operationId: getPaginatedMultiSalesChannelVariantMappingsByMerchantId
      parameters:
        - name: merchantId
          in: query
          required: true
          description: The ID of the merchant
          schema:
            type: string
        - name: page
          in: query
          description: Page number (1-based)
          required: false
          schema:
            type: integer
            minimum: 1
            default: 1
        - name: pageSize
          in: query
          description: Number of items per page
          required: false
          schema:
            type: integer
            minimum: 1
            maximum: 10000
            default: 20
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedMultiSalesChannelMappingsResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              example:
                message: "Error fetching multi sales channel variant mappings"
      tags:
        - multiSalesChannel
      x-accepts: application/json
      x-tags:
        - multiSalesChannel
  /v1/MultiSalesChannelVariantMapping/viewSuggested:
    get:
      summary: View Multi Sales Channel Variant Mapping Suggested by merchantId
      description: Get a paginated list of multi sales channel variant mapping suggested of products information both footway and integration, including image urls.
      operationId: getPaginatedMultiSalesChannelVariantMappingSuggestedByMerchantId
      parameters:
        - name: merchantId
          in: query
          required: true
          description: The ID of the merchant
          schema:
            type: string
        - name: page
          in: query
          description: Page number (1-based)
          required: false
          schema:
            type: integer
            minimum: 1
            default: 1
        - name: pageSize
          in: query
          description: Number of items per page
          required: false
          schema:
            type: integer
            minimum: 1
            maximum: 10000
            default: 20
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedMultiSalesChannelMappingSuggestedResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              example:
                message: "Error fetching multi sales channel variant mapping suggested"
      tags:
        - multiSalesChannel
      x-accepts: application/json
      x-tags:
        - multiSalesChannel
  /v1/MultiSalesChannelVariantMapping/createAllMappingsByMerchant:
    post:
      summary: Create all mappings for merchant id by suggested
      description: Convert all the suggesting mappings to msc Mappings
      operationId: createAllMappingsByMerchant
      parameters:
        - name: merchantId
          description: The ID of the merchant
          in: query
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                type: integer
        '400':
          description: Bad request
        '404':
          description: Merchant not found
          content:
            application/json:
              example:
                message: "Merchant not found"
        '500':
          description: Internal server error
      tags:
        - multiSalesChannel
      x-accepts: application/json
      x-tags:
        - multiSalesChannel
  /v1/variantMapping:
    post:
      summary: Create a variant mapping
      operationId: createVariantMapping
      x-google-quota:
        metricCosts:
          "request-count": 1
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateVariantMappingRequest'
      responses:
        '200':
          description: Variant mapping created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/VariantMapping'
        '400':
          description: Bad request
        '500':
          description: Internal server error
      security:
        - ApiKeyAuth: [ ]
      tags:
        - variantMapping
      x-accepts: application/json
      x-content-type: application/json

  /v1/variantMapping/missing:
    post:
      summary: Create a variant mapping missing entry
      operationId: createVariantMappingMissing
      x-google-quota:
        metricCosts:
          "request-count": 1
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateVariantMappingMissingRequest'
      responses:
        '200':
          description: Variant mapping missing created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/VariantMappingMissing'
      security:
        - ApiKeyAuth: [ ]
      tags:
        - variantMappingMissing
      x-accepts: application/json
      x-content-type: application/json
    get:
      summary: Retrieve a variant mapping missing by its ID
      operationId: getVariantMappingsMissing
      x-google-quota:
        metricCosts:
          "request-count": 1
      parameters:
        - in: query
          name: merchant_code
          description: The code of the merchant
          required: false
          schema:
            type: string
        - in: query
          name: cause
          description: Partial match on the underlying cause
          required: false
          schema:
            type: string
        - in: query
          name: before
          description: limit to missing order before specific date
          required: false
          schema:
            type: string
            format: date
        - in: query
          name: after
          description: limit to missing order after specific date
          required: false
          schema:
            type: string
            format: date
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/VariantMappingMissing'
      security:
        - ApiKeyAuth: [ ]
      tags:
        - variantMappingMissing
  /v1/variantMapping/missing/byMappingId/{mappingId}:
    delete:
      summary: Remove a variant mapping missing by its ID
      operationId: removeVariantMappingMissing
      x-google-quota:
        metricCosts:
          "request-count": 1
      parameters:
        - in: path
          name: mappingId
          description: The ID of the variant mapping missing to remove
          required: true
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: OK (with deleted missing mapping supplied)
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/VariantMappingMissing'
      security:
        - ApiKeyAuth: [ ]
      tags:
        - variantMappingMissing
      x-accepts: application/json
      x-content-type: application/json

  /v1/variantMapping/byMappingId/{mappingId}:
    get:
      summary: Retrieve a variant mapping by its ID
      operationId: getVariantMapping
      x-google-quota:
        metricCosts:
          "request-count": 1
      parameters:
        - in: path
          name: mappingId
          description: The ID of the variant mapping
          required: true
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/VariantMapping'
        '404':
          description: Variant mapping not found
        '500':
          description: Internal server error
      security:
        - ApiKeyAuth: [ ]
      tags:
        - variantMapping
    delete:
      summary: Remove a variant mapping by its ID
      operationId: removeVariantMapping
      x-google-quota:
        metricCosts:
          "request-count": 1
      parameters:
        - in: path
          name: mappingId
          description: The ID of the variant mapping
          required: true
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: string
                example: "Variant mapping removed successfully"
        '404':
          description: Not found
          content:
            application/json:
              schema:
                type: string
                example: "Variant mapping not found"
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                type: string
                example: "Internal server error"
      security:
        - ApiKeyAuth: [ ]
      tags:
        - variantMapping
      x-accepts: application/json
      x-content-type: application/json
  /v1/variantMapping/{variant_id}/to-sku:
    get:
      summary: Retrieve sku by its variant mapping id
      operationId: getSkuFromVariant
      parameters:
        - in: path
          name: variant_id
          description: The ID of the variant
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success - sku
          content:
            application/json:
              schema:
                type: string
        '404':
          description: Variant not found
        '500':
          description: Internal server error
      security:
        - ApiKeyAuth: [ ]
      tags:
        - variantMapping
  /v1/variantMappings/byMerchantCode/{merchantCode}:
    get:
      summary: Retrieve variant mappings by merchant code
      operationId: getVariantMappingListByMerchantCode
      x-google-quota:
        metricCosts:
          "request-count": 1
      parameters:
        - in: path
          name: merchantCode
          description: The merchant code
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/VariantMapping'
        '404':
          description: Variant mappings not found for the specified merchant code
        '500':
          description: Internal server error
      security:
        - ApiKeyAuth: [ ]
      tags:
        - variantMapping
  /v1/variantMapping/provisionForMerchant/{merchantCode}:
    put:
      summary: Provision variant mapping by merchant code (automatically suggested by integration)
      operationId: provisionVariantMappingsForMerchant
      x-google-quota:
        metricCosts:
          "request-count": 1
      parameters:
        - in: path
          name: merchantCode
          description: The merchant code
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/VariantMapping'
        '404':
          description: specified merchant code not found
        '500':
          description: Internal server error
      security:
        - ApiKeyAuth: [ ]
      tags:
        - variantMapping
      x-accepts: application/json
      x-content-type: application/json
  /v1/variantMapping/{merchantCode}:
    get:
      summary: Retrieve a variant mapping by merchant code and variant id
      operationId: getVariantMappingByMerchantCodeAndVariantId
      x-google-quota:
        metricCosts:
          "request-count": 1
      parameters:
        - in: path
          name: merchantCode
          description: The merchant code
          required: true
          schema:
            type: string
        - in: query
          name: integrationVariantId
          description: The ID of the integration variant
          required: false
          schema:
            type: string
        - in: query
          name: footwayVariantId
          description: The ID of the Footway variant
          required: false
          schema:
            type: string
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/VariantMapping'
        '404':
          description: Variant mappings not found for the specified merchant code
        '500':
          description: Internal server error
      security:
        - ApiKeyAuth: [ ]
      tags:
        - variantMapping
      x-accepts: application/json
      x-content-type: application/json
    put:
      summary: Update variant mappings for a merchant
      operationId: upsertVariantMappings
      x-google-quota:
        metricCosts:
          "request-count": 1
      parameters:
        - in: path
          name: merchantCode
          description: The merchant code
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: array
              items:
                $ref: '#/components/schemas/VariantMappingUpdateModel'
      responses:
        '200':
          description: Variant mappings updated successfully
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/VariantMapping'
      security:
        - ApiKeyAuth: [ ]
      x-accepts: application/json
      x-content-type: application/json
      tags:
        - variantMapping
  /v1/variantMapping/{merchantCode}/confirmConflictedMappings:
    put:
      summary: confirm conflicted variant mappings for a merchant
      operationId: confirmConflictedVariantMappings
      x-google-quota:
        metricCosts:
          "request-count": 1
      parameters:
        - in: path
          name: merchantCode
          description: The merchant code
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: array
              items:
                $ref: '#/components/schemas/VariantMappingUpdateModel'
      responses:
        '200':
          description: Conflicted Variant mappings updated successfully
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/VariantMapping'
      security:
        - ApiKeyAuth: [ ]
      x-accepts: application/json
      x-content-type: application/json
      tags:
        - variantMapping
  /v1/variantMapping/viewSuggested:
    get:
      summary: View Variant Mapping Suggested by merchantId
      description: Get a paginated list of variant mapping suggested of products information both footway and integration, including image urls.
      operationId: getPaginatedVariantMappingSuggestedByMerchantId
      parameters:
        - name: merchantId
          in: query
          required: true
          description: The ID of the merchant
          schema:
            type: string
        - name: page
          in: query
          description: Page number (1-based)
          required: false
          schema:
            type: integer
            minimum: 1
            default: 1
        - name: pageSize
          in: query
          description: Number of items per page
          required: false
          schema:
            type: integer
            minimum: 1
            maximum: 10000
            default: 20
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedVariantMappingSuggestedResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              example:
                message: "Error fetching variant mapping suggested"
      tags:
        - variantMapping
      x-accepts: application/json
      x-tags:
        - variantMapping
  /v1/storageOrderLines:
    post:
      summary: Add a new storage order line
      operationId: addStorageOrderLine
      tags:
        - storageOrder
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/StorageOrderLineRequest'
      responses:
        '201':
          description: Created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/StorageOrderLine'
        '400':
          description: Bad request
        '500':
          description: Internal server error
  /v1/storageOrderLines/suggest:
    get:
      summary: Get suggested storage order line
      operationId: getSuggestedStorageOrderLine
      tags:
        - storageOrder
      parameters:
        - name: merchantId
          in: query
          required: true
          description: The merchant ID
          schema:
            type: string
        - name: ean
          in: query
          required: true
          description: The EAN/GTIN code
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/StorageOrderLine'
        '400':
          description: Bad request
        '500':
          description: Internal server error
  /v1/storageOrderLines/getVariantInfoByEan:
    get:
      summary: Get variant information by ean for storage order line
      operationId: getVariantInfoByEanForStorageOrderLine
      tags:
        - storageOrder
      parameters:
        - name: merchantId
          in: query
          required: true
          description: The merchant ID
          schema:
            type: string
        - name: ean
          in: query
          required: true
          description: The EAN/GTIN code
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/StorageOrderLine'
        '400':
          description: Bad request
        '500':
          description: Internal server error
  /v1/storageOrderLines/{storage_order_id}:
    get:
      summary: Get storage order lines for a given storage order
      operationId: getStorageOrderLines
      tags:
        - storageOrder
      parameters:
        - name: storage_order_id
          in: path
          required: true
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/StorageOrderLine'
        '404':
          description: Storage order line not found
        '500':
          description: Internal server error
    put:
      summary: Update storage order lines for a given storage order
      operationId: updateStorageOrderLines
      tags:
        - storageOrder
      parameters:
        - name: storage_order_id
          in: path
          required: true
          schema:
            type: integer
            format: int64
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: array
              items:
                $ref: '#/components/schemas/StorageOrderLineRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/StorageOrderLine'
        '404':
          description: Storage order line not found
        '500':
          description: Internal server error
    delete:
      summary: Remove storage order lines for a given storage order
      operationId: removeStorageOrderLines
      tags:
        - storageOrder
      parameters:
        - name: storage_order_id
          in: path
          required: true
          schema:
            type: integer
            format: int64
      responses:
        '204':
          description: No content
        '404':
          description: Storage order lines not found
        '500':
          description: Internal server error

  /v1/storageOrderLine/{storage_order_line_id}:
    get:
      summary: Get a storage order line
      operationId: getStorageOrderLine
      tags:
        - storageOrder
      parameters:
        - name: storage_order_line_id
          in: path
          required: true
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/StorageOrderLine'
        '404':
          description: Storage order line not found
        '500':
          description: Internal server error
    put:
      summary: Update a storage order line
      operationId: updateStorageOrderLine
      tags:
        - storageOrder
      parameters:
        - name: storage_order_line_id
          in: path
          required: true
          schema:
            type: integer
            format: int64
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/StorageOrderLineRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/StorageOrderLine'
        '404':
          description: Storage order line not found
        '500':
          description: Internal server error

    delete:
      summary: Remove a storage order line
      operationId: removeStorageOrderLine
      tags:
        - storageOrder
      parameters:
        - name: storage_order_line_id
          in: path
          required: true
          schema:
            type: integer
            format: int64
      responses:
        '204':
          description: No content
        '404':
          description: Storage order line not found
        '500':
          description: Internal server error
  /v1/ean/count:
    get:
      summary: Get variant count per EAN
      description: count number of matching variants per EAN
      operationId: countEANs
      parameters:
        - name: ean
          in: query
          description: one or more EANs
          required: true
          schema:
            type: array
            items:
              type: string
          style: form
          explode: false
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/countPerEan'
        '400':
          description: Invalid request parameters
        '500':
          description: Internal server error
      tags:
        - inventory
  /v1/multiSalesChannel/price:
    get:
      summary: Search consignment items by merchant code and prat variant ID
      operationId: multiSalesVariantPrice
      tags:
        - multiSalesChannel
      parameters:
        - name: owner
          description: three letter merchant code (e.g. FWG)
          in: query
          required: true
          schema:
            type: string
        - name: variantId
          description: PRAT variant id
          in: query
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MultiSalesChannelPriceResponse'
        '404':
          description: Variant not found for owner
          content:
            application/json:
              example:
                message: "Variant with id {pratVariantId} not found for owner {owner}"
  
  /v1/MultiSalesChannelVariantMapping/viewMappingsKeySet:
    post:
      summary: View Multi Sales Channel Variant Mappings using keySet pagination
      description: Get a list of multi sales channel variant mappings using keySet pagination for efficient scrolling through large datasets.
      operationId: getMultiSalesChannelVariantMappingsWithKeySetPagination
      tags:
        - multiSalesChannel
      requestBody:
        description: Pagination parameters
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/KeySetPaginationRequest'
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/KeySetPaginatedMappingsResponse'
        '500':
          description: Internal server error
  
  /v1/externalIntegration/search:
    get:
      summary: Search external integration items by fuzzy query terms for displayName and merchant code
      operationId: searchIntegrationItems
      tags:
        - externalIntegration
      parameters:
        - name: query
          in: query
          required: true
          schema:
            type: string
        - name: merchantId
          in: query
          schema:
            type: string
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ExternalVariantResponse'
  /v1/externalIntegration/{merchantId}/paginated:
    get:
      summary: Retrieve paginated external variants for a merchant
      operationId: getPaginatedExternalVariantsByMerchantId
      tags:
        - externalIntegration
      parameters:
        - in: path
          name: merchantId
          description: The ID of the merchant
          required: true
          schema:
            type: string
        - in: query
          name: page
          description: Page number (1-based)
          required: false
          schema:
            type: integer
            minimum: 1
            default: 1
        - in: query
          name: pageSize
          description: Number of items per page
          required: false
          schema:
            type: integer
            minimum: 1
            maximum: 10000
            default: 20
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedExternalVariantResponse'
        '400':
          description: Bad request
        '404':
          description: Merchant not found
        '500':
          description: Internal server error
  /v1/product-categories/{categoryType}/strict-names:
    get:
      summary: Get all strict names for a category type
      description: Retrieve all strict names for the specified category type
      operationId: getAllStrictNames
      parameters:
        - name: categoryType
          in: path
          required: true
          description: The category type (TYPE, GROUP, or DEPARTMENT)
          schema:
            type: string
      responses:
        '200':
          description: Successful retrieval of strict names
          content:
            application/json:
              schema:
                type: array
                items:
                  type: string
      tags:
        - productCategories

  /v1/product-categories/{categoryType}/status:
    get:
      summary: Get status information for a category type
      description: Retrieve status information for the specified category type
      operationId: getStatus
      parameters:
        - name: categoryType
          in: path
          required: true
          description: The category type (TYPE, GROUP, or DEPARTMENT)
          schema:
            type: string
      responses:
        '200':
          description: Successful retrieval of status information
          content:
            application/json:
              schema:
                type: string
      tags:
        - productCategories

  /v1/product-categories/{categoryType}:
    post:
      summary: Add a new source key for a strict name
      description: Add a new source key for an existing strict name within the specified category type
      operationId: addSourceKey
      parameters:
        - name: categoryType
          in: path
          required: true
          description: The category type (TYPE, GROUP, or DEPARTMENT)
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AddSourceKeyRequest'
      responses:
        '201':
          description: Source key added successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProductCategoryResponse'
        '400':
          description: Bad request - strictName or sourceKey missing
      tags:
        - productCategories

  /v1/product-categories/{categoryType}/lookup:
    get:
      summary: Look up strict names from source keys
      description: Convert a list of source keys to their corresponding strict names
      operationId: lookupFromSourceKeys
      parameters:
        - name: categoryType
          in: path
          required: true
          description: The category type (TYPE, GROUP, or DEPARTMENT)
          schema:
            type: string
        - name: sourceKeys
          in: query
          required: true
          description: List of source keys to look up
          schema:
            type: array
            items:
              type: string
          style: form
          explode: false
      responses:
        '200':
          description: Successful lookup of strict names
          content:
            application/json:
              schema:
                type: array
                items:
                  type: string
      tags:
        - productCategories

  /v1/product-categories/{categoryType}/{sourceKey}:
    delete:
      summary: Delete a source key
      description: Delete a source key from the specified category type
      operationId: deleteSourceKey
      parameters:
        - name: categoryType
          in: path
          required: true
          description: The category type (TYPE, GROUP, or DEPARTMENT)
          schema:
            type: string
        - name: sourceKey
          in: path
          required: true
          description: The source key to delete
          schema:
            type: string
      responses:
        '204':
          description: Source key deleted successfully
        '404':
          description: Source key not found
      tags:
        - productCategories

  /v1/product-categories:
    get:
      summary: Get all product categories
      description: Retrieve all product categories with pagination
      operationId: getAllProductCategories
      parameters:
        - name: page
          in: query
          description: Page number (0-based)
          required: false
          schema:
            type: integer
            default: 0
        - name: size
          in: query
          description: Page size
          required: false
          schema:
            type: integer
            default: 20
      responses:
        '200':
          description: Successful retrieval of product categories
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedProductCategoryResponse'
      tags:
        - productCategories

  /v1/product-categories/{id}:
    get:
      summary: Get a product category by ID
      description: Retrieve a specific product category by its unique identifier
      operationId: getProductCategoryById
      parameters:
        - name: id
          in: path
          required: true
          description: The unique identifier of the product category
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: Successful retrieval of product category
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProductCategoryResponse'
        '404':
          description: Product category not found
      tags:
        - productCategories
    put:
      summary: Update a product category
      description: Update an existing product category
      operationId: updateProductCategory
      parameters:
        - name: id
          in: path
          required: true
          description: The unique identifier of the product category to update
          schema:
            type: integer
            format: int64
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateProductCategoryRequest'
      responses:
        '200':
          description: Product category updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProductCategoryResponse'
        '404':
          description: Product category not found
      tags:
        - productCategories

  /v1/product-categories/byType/{categoryType}:
    get:
      summary: Get all categories of a specific type
      description: Retrieve all product categories for a given category type
      operationId: getAllProductCategoriesByType
      parameters:
        - name: categoryType
          in: path
          required: true
          description: The category type (TYPE, GROUP, or DEPARTMENT)
          schema:
            type: string
        - name: page
          in: query
          description: Page number (0-based)
          required: false
          schema:
            type: integer
            default: 0
        - name: size
          in: query
          description: Page size
          required: false
          schema:
            type: integer
            default: 20
      responses:
        '200':
          description: Successful retrieval of product categories
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedProductCategoryResponse'
      tags:
        - productCategories
  /v1/variants/{variantId}/recommended-retail-price:
    get:
      summary: Get recommended retail price for a variant
      description: Retrieves the recommended retail price for a specific variant
      operationId: getRecommendedRetailPrice
      parameters:
        - name: variantId
          in: path
          required: true
          description: The ID of the variant
          schema:
            type: string
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RecommendedRetailPricesResponse'
        '404':
          description: Recommended retail price not found
        '500':
          description: Internal server error
      tags:
        - inventory

components:
  schemas:
    DynamicJsonObject:
      description: A dynamic JSON object
      type: object
      additionalProperties: true

    InventoryImageResponse:
      type: object
      properties:
        variants:
          type: array
          items:
            $ref: '#/components/schemas/MinimalVariant'
        pagination:
          $ref: '#/components/schemas/Pagination'

    MinimalVariant:
      type: object
      properties:
        id:
          type: integer
          description: The unique identifier of the variant.
          example: 657740
        variantNumber:
          type: string
          description: The unique number of the variant.
          example: "203968004107"
        ean:
          type: string
          description: The EAN (European Article Number) of the variant.
          example: "0800090796391"
        dead:
          type: boolean
          description: Indicates whether the variant is marked as dead or not.
          example: false
        onHandKjula:
          type: integer
          description: The quantity of the variant on hand in Kjula.
          example: 0
        existsKjula:
          type: boolean
          description: Indicates whether the variant exists in Kjula.
          example: false
        product:
          $ref: '#/components/schemas/MinimalProduct'
    MinimalProduct:
      type: object
      properties:
        productNumber:
          type: string
          description: The unique number of the product.
          example: "03968-00"
        id:
          type: integer
          description: The unique identifier of the product.
          example: 131302
        images:
          type: array
          items:
            $ref: '#/components/schemas/MinimalImage'
    MinimalImage:
      type: object
      properties:
        listImage:
          type: boolean
        url:
          type: string
    Pagination:
      type: object
      properties:
        offset:
          type: integer
        pageSize:
          type: integer
        pageNumber:
          type: integer
        totalPages:
          type: integer
        totalElements:
          type: integer
        last:
          type: boolean
    Variant:
      type: object
      properties:
        variantNumber:
          type: string
          description: The unique number of the variant.
          example: "203968004107"
        attributes:
          type: object
          description: Additional attributes of the variant.
          example: null
        ean:
          type: string
          description: The EAN (European Article Number) of the variant.
          example: "0800090796391"
        size:
          type: string
          description: The size of the variant.
          example: "EU 41"
        supplierSize:
          type: string
          description: The size of the variant provided by the supplier.
          example: ""
        sizeEuLabel:
          type: string
          description: The size label in EU format.
          example: null
        sizeUkLabel:
          type: string
          description: The size label in UK format.
          example: "UK 7"
        sizeUsLabel:
          type: string
          description: The size label in US format.
          example: null
        weightGram:
          type: string
          description: The weight of the variant in grams.
          example: null
        fwmsId:
          type: string
          description: The ID of the variant in the FWMS (Footway Warehouse Management System).
          example: null
        volumeMilliMeterCubed:
          type: string
          description: The volume of the variant in cubic millimeters.
          example: null
        longestDimensionMilliMeter:
          type: string
          description: The longest dimension of the variant in millimeters.
          example: null
        minInventoryLevel:
          type: integer
          description: The minimum inventory level of the variant.
          example: 0
        maxInventoryLevel:
          type: integer
          description: The maximum inventory level of the variant.
          example: 0
        fixedLotMultiplier:
          type: integer
          description: The fixed lot multiplier of the variant.
          example: 1
        dead:
          type: boolean
          description: Indicates whether the variant is marked as dead or not.
          example: false
        snsSyncEnabled:
          type: boolean
          description: Indicates whether the variant is synchronized with SNS (Stock Notification System).
          example: true
        allowBackorder:
          type: boolean
          description: Indicates whether backorders are allowed for the variant.
          example: false
        sourceRef:
          type: string
          description: The reference ID of the source of the variant.
          example: "footway:40486"
        onHandHbg:
          type: integer
          description: The quantity of the variant on hand in Hbg (Helsingborg).
          example: 0
        onHandKjula:
          type: integer
          description: The quantity of the variant on hand in Kjula.
          example: 0
        existsHbg:
          type: boolean
          description: Indicates whether the variant exists in Hbg (Helsingborg).
          example: true
        existsKjula:
          type: boolean
          description: Indicates whether the variant exists in Kjula.
          example: false
        product:
          $ref: '#/components/schemas/Product'
        barcodes:
          type: array
          items:
            type: string
          description: The array of barcodes associated with the variant.
          example: [ ]
        id:
          type: integer
          description: The unique identifier of the variant.
          example: 657740
        version:
          type: integer
          description: The version number of the variant.
          example: 369
        createdDate:
          type: string
          description: The date and time when the variant was created.
          format: date-time
          example: "2020-09-17T13:04:36.16"
        updatedDate:
          type: string
          description: The date and time when the variant was last updated.
          format: date-time
          example: "2022-10-17T09:17:46.772"
        createdBy:
          type: string
          description: The user who created the variant.
          example: null
        updatedBy:
          type: string
          description: The user who last updated the variant.
          example: null
    Product:
      type: object
      properties:
        productNumber:
          type: string
          description: The unique number of the product.
          example: "03968-00"
        attributes:
          type: object
          description: Additional attributes of the variant.
          example: null
        sourceRef:
          type: string
          description: The reference to the source of the product.
          example: "footway:40425"
        id:
          type: integer
          description: The unique identifier of the product.
          example: 131302
        version:
          type: integer
          description: The version of the product.
          example: 1817
        createdDate:
          type: string
          format: date-time
          description: The date and time when the product was created.
          example: "2020-09-15T09:46:40.572"
        updatedDate:
          type: string
          format: date-time
          description: The date and time when the product was last updated.
          example: "2023-06-26T14:22:43.097"
        createdBy:
          type: string
          description: The user who created the product.
          example: null
        updatedBy:
          type: string
          description: The user who last updated the product.
          example: null
    ProductInventory:
      type: object
      properties:
        min_sku_qty:
          type: integer
          description: The minimum quantity of stock-keeping units (SKUs) available for the product.
          example: 0
        sum_sku_qty:
          type: integer
          description: The total quantity of stock-keeping units (SKUs) available for the product.
          example: 663
        max_sku_qty:
          type: integer
          description: The maximum quantity of stock-keeping units (SKUs) available for the product.
          example: 268
        sku_size_count:
          type: integer
          description: The count of different sizes of the product's SKUs.
          example: 17
    MerchantPortalVariantInventory:
      type: object
      properties:
        merchantId:
          type: string
        variantId:
          type: integer
          format: int64
        displayName:
          type: string
        sku:
          type: string
        ean:
          type: string
        metadata:
          type: object
        snapshotDate:
          type: string
          format: date
    PaginatedMerchantPortalVariantInventoryResponse:
      type: object
      properties:
        items:
          type: array
          items:
            $ref: '#/components/schemas/MerchantPortalVariantInventory'
        totalItems:
          type: integer
        currentPage:
          type: integer
        totalPages:
          type: integer
    MerchantPortalVariantUnidentifiedInventory:
      type: object
      required:
        - merchant_code
        - data
      properties:
        merchant_code:
          type: string
          example: "CAL"
        data:
          type: array
          items:
            $ref: '#/components/schemas/UnidentifiedInventoryItem'
    UnidentifiedInventoryItem:
      type: object
      required:
        - ean
        - qty
      properties:
        ean:
          type: string
          example: "78251945702"
        qty:
          type: integer
          minimum: 1
          example: 4

    VariantInventory:
      type: object
      properties:
        onHandKjula:
          type: integer
          description: The quantity of the variant available at a specific location named "Kjula".
          example: 10
        variantNumber:
          type: string
          description: The unique number of the variant.
          example: "203968004107"
    ProductResponse:
      type: object
      properties:
        productInventory:
          $ref: '#/components/schemas/ProductInventory'
        variantInventory:
          type: array
          items:
            $ref: '#/components/schemas/VariantInventory'

    MultiSalesChannelItemRequest:
      type: object
      properties:
        merchantCode:
          type: string
        pratVariantId:
          type: string
        metadata:
          type: object
          additionalProperties: true
      required:
        - merchantCode
        - pratVariantId

    MultiSalesChannelItemSnapshotResponse:
      type: object
      properties:
        multiSalesChannelItemId:
          type: integer
          format: int64
        created:
          type: string
          format: date-time
        updated:
          type: string
          format: date-time
        multiSalesChannelMetadata:
          type: object
          additionalProperties: true
        merchantCode:
          type: string
        variantId:
          type: integer
          format: int64
        displayName:
          type: string
        sku:
          type: string
        ean:
          type: string
        snapshotMetadata:
          type: object
          additionalProperties: true
        snapshotDate:
          type: string
          format: date
    
    PaginatedMultiSalesChannelItemSnapshotResponse:
      type: object
      properties:
        Items:
          type: array
          items:
            $ref: '#/components/schemas/MultiSalesChannelItemSnapshotResponse'
        totalItems:
          type: integer
        currentPage:
          type: integer
        totalPages:
          type: integer

    MultiSalesChannelPriceResponse:
      type: object
      properties:
        multiSalesChannelItemId:
          type: integer
          format: int64
        variantId:
          type: string
        owner:
          type: string
        globalProductValue:
          type: number
        mscPrice:
          type: number
      required:
        - multiSalesChannelItemId
        - pratVariantId
        - price
        - priceDefault
    MultiSalesChannelItemResponse:
      type: object
      properties:
        multiSalesChannelItemId:
          type: integer
          format: int64
        created:
          type: string
        updated:
          type: string
        merchantCode:
          type: string
        pratVariantId:
          type: integer
        metadata:
          type: object
          additionalProperties: true
      required:
        - multiSalesChannelItemId
        - created
        - updated
        - merchantCode
        - pratVariantId
    VariantMapping:
      type: object
      properties:
        mappingId:
          type: integer
          format: int64
          description: The Mapping ID
        integrationVariantId:
          type: string
          description: The ID of a Integration variant
        footwayVariantId:
          type: string
          description: The ID of a Footway variant
        merchantCode:
          type: string
          description: The merchant code associated with the mapping
    VariantMappingUpdateModel:
      type: object
      properties:
        footwayVariantId:
          type: string
          description: The Footway variant ID
        integrationVariantId:
          type: string
          description: The Integration variant ID
    CreateVariantMappingRequest:
      type: object
      properties:
        integrationVariantId:
          type: string
        footwayVariantId:
          type: string
        merchantCode:
          type: string
      required:
        - integrationVariantId
        - footwayVariantId
        - merchantCode
    VariantMappingMissing:
      type: object
      properties:
        mappingId:
          type: integer
          format: int64
          description: The Mapping ID
        integrationVariantId:
          type: string
          description: The ID of a Integration variant
        footwayVariantId:
          type: string
          description: The ID of a Footway variant
        merchantCode:
          type: string
          description: The merchant code associated with the mapping
        createdAt:
          type: string
          description: The time when the mapping was found missing
        cause:
          type: string
          description: The reason why the mapping is missing
        ean:
          type: string
          description: Relevant EANs
        display_name:
          type: string
          description: Name of the product
        sku:
          type: string
          description: Relevant EANs

    CreateVariantMappingMissingRequest:
      type: object
      properties:
        integrationVariantId:
          type: string
        footwayVariantId:
          type: string
        merchantCode:
          type: string
        cause:
          type: string
      required:
        - cause
        - merchantCode
    StorageOrderLineInventoryResponse:
      type: object
      properties:
        merchantId:
          type: string
          description: The ID of the merchant
          example: "MER"
        productName:
          type: string
          description: The name of the product
          example: "Classic Running Shoe"
        size:
          type: string
          description: The size of the product
          example: "EU 41"
        productBrand:
          type: string
          description: The brand of the product
          example: "Nike"
        productNumber:
          type: string
          description: The product number/SKU
          example: "NK-123-456"
        ean:
          type: string
          description: The EAN/barcode of the product
          example: "1234567890123"
        url:
          type: string
          description: The URL of the first product image
          example: "https://example.com/images/product.jpg"
    PaginatedStorageOrderLineInventoryResponse:
      type: object
      properties:
        items:
          type: array
          items:
            $ref: '#/components/schemas/StorageOrderLineInventoryResponse'
        totalItems:
          type: integer
          description: Total number of items across all pages
          example: 100
        currentPage:
          type: integer
          description: Current page number
          example: 1
        totalPages:
          type: integer
          description: Total number of pages
          example: 5
    StorageOrderLineRequest:
      type: object
      properties:
        storage_order_id:
          type: string
        storage_order_line_id:
          type: integer
          format: int64
        expected_delivery_date:
          type: string
          format: date
        merchant_id:
          type: string
        product_name:
          type: string
        product_number:
          type: string
        product_brand:
          type: string
        gtin_ean:
          type: string
        shopify_variant_id:
          type: string
        country_of_origin:
          type: string
        size:
          type: string
        quantity:
          type: string
        height:
          type: string
        length:
          type: string
        width:
          type: string
        weight:
          type: string
        value:
          type: string
        price:
          type: string
        status:
          type: string
        variant_type:
          type: string
        product_group:
          type: string
        product_type:
          type: string
      required:
        - storage_order_id
        - merchant_id
        - gtin_ean
        - quantity

    StorageOrderLine:
      type: object
      properties:
        storage_order_line_id:
          type: integer
          format: int64
        created_at:
          type: string
          format: date-time
        storage_order_id:
          type: string
        expected_delivery_date:
          type: string
          format: date
        merchant_id:
          type: string
        product_name:
          type: string
        product_number:
          type: string
        product_brand:
          type: string
        gtin_ean:
          type: string
        shopify_variant_id:
          type: string
        country_of_origin:
          type: string
        size:
          type: string
        quantity:
          type: string
        height:
          type: string
        length:
          type: string
        width:
          type: string
        weight:
          type: string
        value:
          type: string
        price:
          type: string
        status:
          type: string
        variant_type:
          type: string
        product_group:
          type: string
        product_type:
          type: string

    InventoryItem:
      type: object
      properties:
        merchantId:
          type: string
          minLength: 3
          maxLength: 3
        variantId:
          type: string
        productName:
          type: string
        supplierModelNumber:
          type: string
        ean:
          type: array
          items:
            type: string
        size:
          type: string
        price:
          type: string
        product_description:
          type: string
        vendor:
          type: string
        quantity:
          type: integer
        productType:
          type: array
          items:
            type: string
        productGroup:
          type: array
          items:
            type: string
        department:
          type: array
          items:
            type: string
        image_url:
          type: string
        created:
          type: string
          format: date-time
        updated:
          type: string
          format: date-time
      required:
        - merchantId
        - variantId
        - productName
        - quantity

    InventoryItemRequest:
      type: object
      properties:
        productName:
          type: string
        supplierModelNumber:
          type: string
        ean:
          type: string
        size:
          type: string
        vendor:
          type: string
        quantity:
          type: integer
        productType:
          type: string
        productGroup:
          type: string
        department:
          type: string
      required:
        - productName
        - quantity

    PaginatedInventoryResponse:
      type: object
      properties:
        items:
          type: array
          items:
            $ref: '#/components/schemas/InventoryItem'
        totalItems:
          type: integer
        currentPage:
          type: integer
        totalPages:
          type: integer

    AvailableFilters:
      type: object
      properties:
        totalItems:
          type: integer
          description: Total number of items matching the current filter selection
        merchants:
          type: object
          properties:
            total:
              type: integer
              description: Total number of unique merchants in the filtered results
            values:
              type: array
              items:
                type: object
                properties:
                  id:
                    type: string
                  name:
                    type: string
                  count:
                    type: integer
                    description: Number of items matching this merchant
        vendors:
          type: object
          properties:
            total:
              type: integer
              description: Total number of unique vendors in the filtered results
            values:
              type: array
              items:
                type: object
                properties:
                  name:
                    type: string
                  count:
                    type: integer
                    description: Number of items matching this vendor
        departments:
          type: object
          properties:
            total:
              type: integer
              description: Total number of unique departments in the filtered results
            values:
              type: array
              items:
                type: object
                properties:
                  name:
                    type: string
                  count:
                    type: integer
                    description: Number of items matching this department
        productGroups:
          type: object
          properties:
            total:
              type: integer
              description: Total number of unique product groups in the filtered results
            values:
              type: array
              items:
                type: object
                properties:
                  name:
                    type: string
                  count:
                    type: integer
                    description: Number of items matching this product group
        productTypes:
          type: object
          properties:
            total:
              type: integer
              description: Total number of unique product types in the filtered results
            values:
              type: array
              items:
                type: object
                properties:
                  name:
                    type: string
                  count:
                    type: integer
                    description: Number of items matching this product type
    countPerEan:
      type: array
      items:
        type: object
        properties:
          ean:
            type: string
          count:
            type: integer
    InventoryShopifyMappingItem:
      type: object
      properties:
        merchantId:
          type: string
          minLength: 3
          maxLength: 3
        footwayVariantId:
          type: string
        productName:
          type: string
        ean:
          type: string
        footway_image_url:
          type: string
        status:
          type: string
        shopify_variant_id:
          type: string
        display_name:
          type: string
        shopify_image_url:
          type: string
      required:
        - merchantId
        - variantId
        - status
    ShopifyVariantInventory:
      type: object
      properties:
        merchantId:
          type: string
        variantId:
          type: integer
          format: int64
        displayName:
          type: string
        sku:
          type: string
        ean:
          type: string
        metadata:
          type: object
        snapshotDate:
          type: string
          format: date
    PaginatedVariantInventoryResponse:
      type: object
      properties:
        items:
          type: array
          items:
            $ref: '#/components/schemas/ShopifyVariantInventory'
        totalItems:
          type: integer
        currentPage:
          type: integer
        totalPages:
          type: integer
    PaginatedMultiSalesChannelMappingsResponse:
      type: object
      properties:
        items:
          type: array
          items:
            $ref: '#/components/schemas/MultiSalesChannelMappingsResponse'
        totalItems:
          type: integer
          description: Total number of items across all pages
          example: 100
        currentPage:
          type: integer
          description: Current page number
          example: 1
        totalPages:
          type: integer
          description: Total number of pages
          example: 5
    MultiSalesChannelMappingsResponse:
      type: object
      properties:
        merchantId:
          type: string
        footwayVariantId:
          type: string
        integrationVariantId:
          type: string
        mscMappingId:
          type: integer
          format: int64
        footwayProduct:
          type: string
        mscEan:
          type: array
          items:
            type: string
        footwayImageUrl:
          type: string
        integrationProduct:
          type: string
        productId:
          type: string
        ean:
          type: string
        integrationImageUrl:
          type: string
    PaginatedMultiSalesChannelMappingSuggestedResponse:
      type: object
      properties:
        items:
          type: array
          items:
            $ref: '#/components/schemas/MultiSalesChannelMappingSuggestedResponse'
        totalItems:
          type: integer
          description: Total number of items across all pages
          example: 100
        currentPage:
          type: integer
          description: Current page number
          example: 1
        totalPages:
          type: integer
          description: Total number of pages
          example: 5
    MultiSalesChannelMappingSuggestedResponse:
      type: object
      properties:
        merchantId:
          type: string
        footwayVariantId:
          type: string
        integrationVariantId:
          type: string
        footwayProductName:
          type: string
        footwayEan:
          type: array
          items:
            type: string
        footwayImageUrl:
          type: string
        integrationProductName:
          type: string
        productId:
          type: string
        ean:
          type: string
        integrationImageUrl:
          type: string
    PaginatedExternalVariantResponse:
      type: object
      properties:
        items:
          type: array
          items:
            $ref: '#/components/schemas/ExternalVariantResponse'
        totalItems:
          type: integer
          description: Total number of items across all pages
          example: 100
        currentPage:
          type: integer
          description: Current page number
          example: 1
        totalPages:
          type: integer
          description: Total number of pages
          example: 5
    ExternalVariantResponse:
      type: object
      properties:
        merchantId:
          type: string
        variantId:
          type: string
        displayName:
          type: string
        sku:
          type: string
        ean:
          type: string
        imageUrl:
          type: string
        metadata:
          type: object
    AddSourceKeyRequest:
      type: object
      properties:
        strictName:
          type: string
          description: The strict name to associate with the source key
        sourceKey:
          type: string
          description: The source key to add
      required:
        - strictName
        - sourceKey
    ProductCategoryResponse:
      type: object
      properties:
        id:
          type: integer
          format: int64
          description: The unique identifier for the product category
        categoryType:
          type: string
          description: The type of category (TYPE, GROUP, or DEPARTMENT)
        strictName:
          type: string
          description: The strict name of the category
        sourceKey:
          type: string
          description: The source key associated with the strict name
        createdAt:
          type: string
          format: date-time
          description: The date and time when the category was created
          example: "2020-09-15T09:46:40.572"
        updatedAt:
          type: string
          format: date-time
          description: The date and time when the category was last updated
          example: "2020-09-15T09:46:40.572"
      required:
        - id
        - categoryType
        - strictName
        - sourceKey
        - createdAt
        - updatedAt
    UpdateProductCategoryRequest:
      type: object
      properties:
        strictName:
          type: string
          description: The strict name of the category
        sourceKey:
          type: string
          description: The source key associated with the strict name
      required:
        - strictName
        - sourceKey
    PaginatedProductCategoryResponse:
      type: object
      properties:
        items:
          type: array
          items:
            $ref: '#/components/schemas/ProductCategoryResponse'
        totalItems:
          type: integer
          description: Total number of items across all pages
          example: 100
        currentPage:
          type: integer
          description: Current page number
          example: 1
        totalPages:
          type: integer
          description: Total number of pages
          example: 5
    Filters:
      type: object
      properties:
        merchantId:
          type: array
          items:
            type: string
            minLength: 3
            maxLength: 3
        vendor:
          type: array
          items:
            type: string
        department:
          type: array
          items:
            type: string
        productGroup:
          type: array
          items:
            type: string
        productType:
          type: array
          items:
            type: string
        variantIds:
          type: array
          items:
            type: string
        searchText:
          type: string
    PaginatedVariantMappingSuggestedResponse:
      type: object
      properties:
        items:
          type: array
          items:
            $ref: '#/components/schemas/VariantMappingSuggestedResponse'
        totalItems:
          type: integer
          description: Total number of items across all pages
          example: 100
        currentPage:
          type: integer
          description: Current page number
          example: 1
        totalPages:
          type: integer
          description: Total number of pages
          example: 5
    VariantMappingSuggestedResponse:
      type: object
      properties:
        merchantId:
          type: string
        footwayVariantId:
          type: string
        integrationVariantId:
          type: string
        footwayProductName:
          type: string
        footwayEan:
          type: array
          items:
            type: string
        footwayImageUrl:
          type: string
        integrationProductName:
          type: string
        productId:
          type: string
        ean:
          type: string
        integrationImageUrl:
          type: string
    RecommendedRetailPricesResponse:
      type: object
      properties:
        recommended_retail_price:
          type: object
          additionalProperties:
            type: integer
          description: Map of country codes to recommended retail prices in the smallest currency unit
          example:
            se: 239
            si: 21
            sk: 21
            us: 20

    KeySetCursor:
      type: object
      properties:
        productName:
          type: string
          description: Product name for cursor position
        variantId:
          type: string
          description: Variant ID for cursor position

    KeySetPaginationRequest:
      type: object
      properties:
        merchantId:
          type: string
          description: The ID of the merchant
        limit:
          type: integer
          minimum: 1
          maximum: 1000
          default: 50
          description: Maximum number of records to return
        afterCursor:
          allOf:
            - $ref: '#/components/schemas/KeySetCursor'
          description: Cursor for forward pagination (next page)
        beforeCursor:
          allOf:
            - $ref: '#/components/schemas/KeySetCursor'
          description: Cursor for backward pagination (previous page)
        descending:
          type: boolean
          default: false
          description: Whether to order results in descending order
        searchTerm:
          type: string
          description: Optional search term for product name search
        eanFilter:
          type: string
          description: Optional EAN value to filter by exact match
      required:
        - merchantId

    KeySetPaginatedMappingsResponse:
      type: object
      properties:
        items:
          type: array
          items:
            $ref: '#/components/schemas/MultiSalesChannelMappingsResponse'
        hasMore:
          type: boolean
          description: Indicates if there are more results available
        nextCursor:
          allOf:
            - $ref: '#/components/schemas/KeySetCursor'
          description: Cursor for fetching the next page
        prevCursor:
          allOf:
            - $ref: '#/components/schemas/KeySetCursor'
          description: Cursor for fetching the previous page
