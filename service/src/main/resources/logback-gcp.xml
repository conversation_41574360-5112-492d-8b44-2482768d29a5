<configuration>
    <appender name="CLOUD" class="com.google.cloud.logging.logback.LoggingAppender">
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>DEBUG</level>
        </filter>
        <log>application.log</log> <!-- Optional : default java.log -->
        <flushLevel>WARN</flushLevel> <!-- Optional : default ERROR -->
    </appender>
    <logger name="com.footway" level="trace" additivity="false">
        <appender-ref ref="CLOUD" />
    </logger>
    <root level="info">
        <appender-ref ref="CLOUD" />
    </root>
    <statusListener class="ch.qos.logback.core.status.NopStatusListener"/>
</configuration>