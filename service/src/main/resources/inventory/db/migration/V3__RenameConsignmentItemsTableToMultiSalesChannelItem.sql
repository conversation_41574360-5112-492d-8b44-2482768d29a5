-- Rename the table
ALTER TABLE consignment_items RENAME TO multi_sales_channel_items;

-- Rename the primary key column
ALTER TABLE multi_sales_channel_items RENAME COLUMN consignment_item_id TO multi_sales_channel_item_id;

-- Rename the primary key constraint
ALTER INDEX consignment_items_pkey RENAME TO multi_sales_channel_items_pkey;

-- Rename indexes
ALTER INDEX idx_consignment_items_created RENAME TO idx_multi_sales_channel_items_created;
ALTER INDEX idx_consignment_items_updated RENAME TO idx_multi_sales_channel_items_updated;
ALTER INDEX idx_consignment_items_merchant_code RENAME TO idx_multi_sales_channel_items_merchant_code;
ALTER INDEX idx_consignment_items_prat_variant_id RENAME TO idx_multi_sales_channel_items_prat_variant_id;
ALTER INDEX idx_consignment_items_metadata RENAME TO idx_multi_sales_channel_items_metadata;

-- If there are any sequences associated with the table, rename them
ALTER SEQUENCE consignment_items_consignment_item_id_seq
    RENAME TO multi_sales_channel_items_multi_sales_channel_item_id_seq;

-- Update the sequence ownership
ALTER SEQUENCE multi_sales_channel_items_multi_sales_channel_item_id_seq
    OWNED BY multi_sales_channel_items.multi_sales_channel_item_id;
