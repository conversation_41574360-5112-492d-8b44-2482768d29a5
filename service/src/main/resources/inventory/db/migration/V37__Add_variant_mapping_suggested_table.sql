CREATE TABLE variant_mapping_suggested (
                                            variant_mapping_suggested_id BIGSERIAL PRIMARY KEY,
                                            created TIMES<PERSON>MP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
                                            updated TIM<PERSON><PERSON><PERSON> WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
                                            merchant_id CHAR(3) NOT NULL,
                                            footway_variant_id TEXT NOT NULL,
                                            integration_variant_id TEXT NOT NULL,
                                            status TEXT NOT NULL,
                                            CONSTRAINT unique_merchant_footway_integration UNIQUE (merchant_id, footway_variant_id, integration_variant_id)
);