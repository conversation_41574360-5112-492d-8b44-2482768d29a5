CREATE TABLE IF NOT EXISTS prat_variant_inventories_daily_snapshots (
    merchant_id CHAR(3) NOT NULL,
    variant_id BIGINT NOT NULL,
    display_name TEXT NOT NULL,
    sku TEXT,
    ean TEXT,
    metadata JSONB,
    snapshot_date DATE NOT NULL DEFAULT CURRENT_DATE,
    PRIMARY KEY (merchant_id, variant_id, snapshot_date)
);

CREATE EXTENSION IF NOT EXISTS pg_trgm;
-- Create a simple trigram index for text search instead of full-text search
CREATE INDEX IF NOT EXISTS idx_prat_variant_inventories_trigram ON prat_variant_inventories_daily_snapshots USING GIN (display_name gin_trgm_ops);
CREATE INDEX IF NOT EXISTS idx_prat_variant_inventories_merchant_id ON prat_variant_inventories_daily_snapshots(merchant_id);
CREATE INDEX IF NOT EXISTS idx_prat_variant_inventories_sku ON prat_variant_inventories_daily_snapshots(sku);
CREATE INDEX IF NOT EXISTS idx_prat_variant_inventories_ean ON prat_variant_inventories_daily_snapshots(ean);
CREATE INDEX IF NOT EXISTS idx_prat_variant_inventories_snapshot_date ON prat_variant_inventories_daily_snapshots(snapshot_date);
