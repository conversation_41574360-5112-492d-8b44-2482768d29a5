-- Rename columns to snake case
ALTER TABLE inventory
    RENAME COLUMN productType TO product_type;
ALTER TABLE inventory
    RENAME COLUMN productGroup TO product_group;

-- Drop existing indices
DROP INDEX IF EXISTS idx_inventory_productgroup;
DROP INDEX IF EXISTS idx_inventory_producttype;

-- Recreate indices with snake case names
CREATE INDEX IF NOT EXISTS idx_inventory_product_group ON inventory(product_group);
CREATE INDEX IF NOT EXISTS idx_inventory_product_type ON inventory(product_type);