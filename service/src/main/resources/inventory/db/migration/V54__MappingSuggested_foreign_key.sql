ALTER TABLE variant_mapping_suggested
DROP
CONSTRAINT foreign_key_integration_variant,
ADD CONSTRAINT foreign_key_integration_variant
FOREIGN KEY (merchant_id, integration_variant_id)
REFERENCES external_variants (merchant_id, variant_id)
ON DELETE
CASCADE;

ALTER TABLE multi_sales_channel_variant_mapping_suggested
DROP
CONSTRAINT fk_integration_variant,
ADD CONSTRAINT fk_integration_variant
FOREIGN KEY (merchant_id, integration_variant_id)
REFERENCES external_variants (merchant_id, variant_id)
ON DELETE
CASCADE;