CREATE TABLE product_categories (
                                    id SERIAL PRIMARY KEY,
                                    category_type TEXT NOT NULL,  -- 'TYPE', 'GROUP', or 'DEPARTMENT'
                                    strict_name TEXT NOT NULL,
                                    source_key TEXT NOT NULL,
                                    created TIM<PERSON><PERSON><PERSON> WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
                                    updated TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
                                    CONSTRAINT unique_product_categories_type_source_key UNIQUE (category_type, source_key)
);

-- Add index for faster lookups by strict name
CREATE INDEX idx_product_categories_type_strict_name
    ON product_categories(category_type, strict_name);