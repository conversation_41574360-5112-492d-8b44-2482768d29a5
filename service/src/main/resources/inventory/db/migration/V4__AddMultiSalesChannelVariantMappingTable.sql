CREATE TABLE multi_sales_channel_variant_mapping (
                                   multi_sales_channel_variant_mapping_id BIGSERIAL PRIMARY KEY,
                                   created TIM<PERSON><PERSON><PERSON> WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
                                   updated TIM<PERSON><PERSON>MP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
                                   merchant_id CHAR(3) NOT NULL,
                                   footway_variant_id TEXT NOT NULL,
                                   integration_variant_id TEXT NOT NULL
);

-- Create indexes on all columns (except consignment_item_id which is already indexed as PRIMARY KEY)
CREATE INDEX idx_shopify_merchant ON multi_sales_channel_variant_mapping (integration_variant_id, merchant_id);
CREATE INDEX idx_footway_merchant ON multi_sales_channel_variant_mapping (footway_variant_id, merchant_id);
