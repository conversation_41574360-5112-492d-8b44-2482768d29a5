CREATE TABLE variant_mapping_suggested (
                                           variant_mapping_suggested_id BIGSERIAL PRIMARY KEY,
                                           created TIM<PERSON><PERSON><PERSON> WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
                                           updated TIM<PERSON><PERSON><PERSON> WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
                                           merchant_id CHAR(3) NOT NULL,
                                           footway_variant_id TEXT NOT NULL,
                                           integration_variant_id BIGINT NOT NULL,
                                           status TEXT NOT NULL,
                                           CONSTRAINT uk_merchant_footway_integration UNIQUE (merchant_id, footway_variant_id, integration_variant_id)
);

-- Add foreign key constraint for footway_variant_id referencing variants.variant_id.
ALTER TABLE variant_mapping_suggested
    ADD CONSTRAINT foreign_key_footway_variant
        FOREIGN KEY (footway_variant_id)
            REFERENCES variants(variant_id);

-- Add composite foreign key constraint for merchant_id and integration_variant_id referencing external_variants.
ALTER TABLE variant_mapping_suggested
    ADD CONSTRAINT foreign_key_integration_variant
        FOREIGN KEY (merchant_id, integration_variant_id)
            REFERENCES external_variants(merchant_id, variant_id);