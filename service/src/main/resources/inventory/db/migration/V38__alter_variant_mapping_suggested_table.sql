-- Step 1: Change the type of integration_variant_id from text to bigint.
ALTER TABLE variant_mapping_suggested
ALTER COLUMN integration_variant_id TYPE bigint USING integration_variant_id::bigint;

-- Step 2: Add foreign key constraint for footway_variant_id referencing variants.variant_id.
ALTER TABLE variant_mapping_suggested
    ADD CONSTRAINT fk_footway_variant
        FOREIGN KEY (footway_variant_id)
            REFERENCES variants(variant_id);

-- Step 3: Add composite foreign key constraint for merchant_id and integration_variant_id referencing external_variants.
ALTER TABLE variant_mapping_suggested
    ADD CONSTRAINT fk_integration_variant
        FOREIGN KEY (merchant_id, integration_variant_id)
            REFERENCES external_variants(merchant_id, variant_id);