CREATE TABLE variant_mappings (
        variant_mapping_id BIGSERIAL PRIMARY KEY,
        created TIM<PERSON><PERSON><PERSON> WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
        merchant_id CHAR(3) NOT NULL,
        footway_variant_id TEXT NOT NULL,
        integration_variant_id TEXT NOT NULL
);

-- Create indexes on all columns (except variant_mapping_id which is already indexed as PRIMARY KEY)
CREATE INDEX idx_variant_mappings_integration_merchant ON variant_mappings (integration_variant_id, merchant_id);
CREATE INDEX idx_variant_mappings_footway_merchant ON variant_mappings (footway_variant_id, merchant_id);
