CREATE TABLE consignment_items (
                                   consignment_item_id BIGSERIAL PRIMARY KEY,
                                   created TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
                                   updated TIM<PERSON><PERSON>MP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
                                   merchant_code VARCHAR(255) NOT NULL,
                                   prat_variant_id INTEGER NOT NULL,
                                   metadata JSONB
);

-- Create indexes on all columns (except consignment_item_id which is already indexed as PRIMARY KEY)
CREATE INDEX idx_consignment_items_created ON consignment_items (created);
CREATE INDEX idx_consignment_items_updated ON consignment_items (updated);
CREATE INDEX idx_consignment_items_merchant_code ON consignment_items (merchant_code);
CREATE INDEX idx_consignment_items_prat_variant_id ON consignment_items (prat_variant_id);
CREATE INDEX idx_consignment_items_metadata ON consignment_items USING GIN (metadata);