-- Drop the view if it exists (we need to do this because otherwise we cant alter the table)
DROP VIEW IF EXISTS public.msc_variant_view;

-- Drop the existing index on prat_variant_id to allow type change
DROP INDEX IF EXISTS idx_multi_sales_channel_items_prat_variant_id;

-- Alter the column type from integer to text
ALTER TABLE multi_sales_channel_items
    ALTER COLUMN prat_variant_id TYPE TEXT USING prat_variant_id::TEXT;

-- Recreate the index on prat_variant_id
CREATE INDEX idx_multi_sales_channel_items_prat_variant_id
    ON multi_sales_channel_items (prat_variant_id);

-- Recreate the view
CREATE OR REPLACE VIEW public.msc_variant_view
            (id, merchant_id, variant_id, product_name, supplier_model_number, ean, size, vendor, quantity,
            product_type, product_group, department, variant_created, variant_updated, inventory_level_created,
            inventory_level_updated, image_url, price, product_description)
AS
SELECT msc.multi_sales_channel_item_id,
       msc.merchant_code as merchant_id,
       il.variant_id,
       v.product_name,
       v.supplier_model_number,
       v.ean,
       v.size,
       v.vendor,
       il.quantity,
       v.product_type,
       v.product_group,
       v.department,
       v.created  AS variant_created,
       v.updated  AS variant_updated,
       il.created AS inventory_level_created,
       il.updated AS inventory_level_updated,
       v.image_url,
       v.price,
       v.product_description
FROM multi_sales_channel_items msc
         LEFT JOIN inventory_levels il on msc.prat_variant_id = il.variant_id and msc.merchant_code = il.merchant_id
         LEFT JOIN variants v ON il.variant_id = v.variant_id;
