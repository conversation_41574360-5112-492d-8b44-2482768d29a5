-- Rename the table
ALTER TABLE shopify_variant_inventories RENAME TO external_variant_inventories;

-- Optionally, rename the indexes to match the new table name
ALTER INDEX IF EXISTS idx_shopify_variant_inventories_trigram
    RENAME TO idx_external_variant_inventories_trigram;

ALTER INDEX IF EXISTS idx_shopify_variant_inventories_merchant_id
    RENAME TO idx_external_variant_inventories_merchant_id;

ALTER INDEX IF EXISTS idx_shopify_variant_inventories_sku
    RENAME TO idx_external_variant_inventories_sku;

ALTER INDEX IF EXISTS idx_shopify_variant_inventories_ean
    RENAME TO idx_external_variant_inventories_ean;