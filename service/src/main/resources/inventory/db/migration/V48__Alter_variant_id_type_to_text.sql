-- Drop foreign keys
ALTER TABLE variant_mapping_suggested
DROP CONSTRAINT IF EXISTS foreign_key_integration_variant;

ALTER TABLE multi_sales_channel_variant_mapping_suggested
DROP CONSTRAINT IF EXISTS fk_integration_variant;

-- Alter column types
ALTER TABLE variant_mapping_suggested
ALTER COLUMN integration_variant_id TYPE text
    USING integration_variant_id::text;

ALTER TABLE multi_sales_channel_variant_mapping_suggested
ALTER COLUMN integration_variant_id TYPE text
    USING integration_variant_id::text;

ALTER TABLE external_variants
ALTER COLUMN variant_id TYPE text
    USING variant_id::text;

-- Re-add foreign keys
ALTER TABLE variant_mapping_suggested
    ADD CONSTRAINT foreign_key_integration_variant
        FOREIGN KEY (merchant_id, integration_variant_id)
            REFERENCES external_variants(merchant_id, variant_id);

ALTER TABLE multi_sales_channel_variant_mapping_suggested
    ADD CONSTRAINT fk_integration_variant
        FOREIGN KEY (merchant_id, integration_variant_id)
            REFERENCES external_variants(merchant_id, variant_id);