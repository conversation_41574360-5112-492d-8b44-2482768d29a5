CREATE OR REPLACE VIEW public.inventory_view
            (id, merchant_id, variant_id, product_name, supplier_model_number, ean, size, vendor, quantity,
             product_type, product_group, department, variant_created, variant_updated, inventory_level_created,
             inventory_level_updated, image_url, price, product_description)
AS
SELECT il.id,
       il.merchant_id,
       il.variant_id,
       v.product_name,
       v.supplier_model_number,
       v.ean,
       v.size,
       v.vendor,
       il.quantity,
       v.product_type,
       v.product_group,
       v.department,
       v.created  AS variant_created,
       v.updated  AS variant_updated,
       il.created AS inventory_level_created,
       il.updated AS inventory_level_updated,
       v.image_url,
       v.price,
       v.product_description
FROM inventory_levels il
         JOIN variants v ON il.variant_id = v.variant_id;
