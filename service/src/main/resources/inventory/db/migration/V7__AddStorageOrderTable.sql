CREATE TABLE storage_order_lines (
        storage_order_line_id BIGSERIAL PRIMARY KEY,
        created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
        storage_order_id VARCHAR,
        expected_delivery_date DATE,
        merchant_id CHAR(3),
        product_name VA<PERSON>HA<PERSON>,
        product_number VARCHAR,
        product_brand VARCHAR,
        gtin_ean VARCHAR,
        shopify_variant_id VARCHAR,
        country_of_origin VARCHAR,
        size VARCHAR,
        quantity VARCHAR,
        height VARCHAR,
        length VARCHAR,
        width VARCHAR,
        weight VARCHAR,
        value VARCHAR,
        price VARCHAR
);

-- Create indexes on all columns (except variant_mapping_id which is already indexed as PRIMARY KEY)
CREATE INDEX idx_merchant_created ON storage_order_lines (merchant_id, created_at);
