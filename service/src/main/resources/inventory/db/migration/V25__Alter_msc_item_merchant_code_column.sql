-- Step 1: Drop the existing view
DROP VIEW IF EXISTS public.msc_variant_view;

-- Step 2: Drop the existing index on merchant_code
DROP INDEX IF EXISTS public.idx_multi_sales_channel_items_merchant_code;

-- Step 3: Alter the column merchant_code to bpchar(3)
ALTER TABLE public.multi_sales_channel_items
    ALTER COLUMN merchant_code SET DATA TYPE bpchar(3);

-- Step 4: Add the index back on the updated merchant_code column
CREATE INDEX idx_multi_sales_channel_items_merchant_code
    ON public.multi_sales_channel_items USING btree (merchant_code);

-- Step 5: Recreate the view with the updated column type
CREATE OR REPLACE VIEW public.msc_variant_view AS
SELECT msc.multi_sales_channel_item_id AS id,
       msc.merchant_code AS merchant_id,
       il.variant_id,
       v.product_name,
       v.supplier_model_number,
       v.ean,
       v.size,
       v.vendor,
       il.quantity,
       v.product_type,
       v.product_group,
       v.department,
       v.created AS variant_created,
       v.updated AS variant_updated,
       il.created AS inventory_level_created,
       il.updated AS inventory_level_updated,
       v.image_url,
       v.price,
       v.product_description
FROM multi_sales_channel_items msc
         JOIN inventory_levels il ON msc.prat_variant_id = il.variant_id
    AND msc.merchant_code = il.merchant_id  -- No need for casting anymore
         JOIN variants v ON il.variant_id = v.variant_id;

-- Step 6: Analyze the table to update statistics
ANALYZE public.multi_sales_channel_items;
