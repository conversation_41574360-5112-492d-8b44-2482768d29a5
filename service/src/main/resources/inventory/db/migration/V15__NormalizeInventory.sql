CREATE TABLE public.variants (
                                 id bigserial NOT NULL,
                                 variant_id text NOT NULL,
                                 product_name text NOT NULL,
                                 supplier_model_number text NULL,
                                 ean _text NULL,
                                 "size" text NULL,
                                 vendor text NULL,
                                 product_type _text NULL,
                                 product_group _text NULL,
                                 department _text NULL,
                                 created timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
                                 updated timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
                                 image_url text NULL,
                                 price text NULL,
                                 CONSTRAINT pkey_variants_id PRIMARY KEY (id),
                                 CONSTRAINT unique_variants_variant_id UNIQUE (variant_id)


);


CREATE TABLE public.inventory_levels (
                                         id bigserial NOT NULL,
                                         merchant_id bpchar(3) NOT NULL,
                                         variant_id text NOT NULL,
                                         quantity int4 NULL,
                                         created timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
                                         updated timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
                                         CONSTRAINT pkey_inventory_levels_id PRIMARY KEY (id),
                                         CONSTRAINT unique_inventory_levels UNIQUE (merchant_id, variant_id),
                                         CONSTRAINT fk_inventory_levels_variant_id
                                             FOREIGN KEY (variant_id)
                                                 REFERENCES public.variants (variant_id)
                                                 ON DELETE RESTRICT  -- Prevent deletion of variants if there are inventory levels referencing them
);


CREATE VIEW public.inventory_view AS
SELECT
    il.id,
    il.merchant_id,
    il.variant_id,
    v.product_name,
    v.supplier_model_number,
    v.ean,
    v."size",
    v.vendor,
    il.quantity,
    v.product_type,
    v.product_group,
    v.department,
    v.created as variant_created,
    v.updated as variant_updated,
    il.created as inventory_level_created,
    il.updated as inventory_level_updated,
    v.image_url,
    v.price
FROM
    public.inventory_levels il
        JOIN
    public.variants v
    ON
        il.variant_id = v.variant_id;


INSERT INTO public.variants (
    variant_id, product_name, supplier_model_number, ean, "size", vendor,
    product_type, product_group, department, created, updated, image_url, price
)
SELECT DISTINCT ON(i.variant_id)
    i.variant_id,
    i.product_name,
    i.supplier_model_number,
    i.ean,
    i."size",
    i.vendor,
    i.product_type,
    i.product_group,
    i.department,
    i.created,
    i.updated,
    i.image_url,
    i.price
FROM public.inventory i;

CREATE INDEX idx_variants_variant_id ON public.variants USING btree (variant_id);
CREATE INDEX idx_variants_department ON public.variants USING btree (department);
CREATE INDEX idx_variants_product_group ON public.variants USING btree (product_group);
CREATE INDEX idx_variants_product_type ON public.variants USING btree (product_type);
CREATE INDEX idx_variants_trigram ON public.variants USING gin (product_name gin_trgm_ops);
CREATE INDEX idx_variants_vendor ON public.variants USING btree (vendor);


INSERT INTO public.inventory_levels (merchant_id, variant_id, quantity, created, updated)
SELECT merchant_id, variant_id, quantity, created , updated
FROM public.inventory;

CREATE INDEX idx_inventory_levels on public.inventory_levels using btree (merchant_id, variant_id);
