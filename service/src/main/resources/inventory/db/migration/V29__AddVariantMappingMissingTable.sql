CREATE TABLE variant_mapping_missing (
        variant_mapping_id BIGSERIAL PRIMARY KEY,
        created TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
        merchant_id CHAR(3) NOT NULL,
        footway_variant_id TEXT NULL,
        integration_variant_id TEXT NULL,
        cause TEXT NOT NULL,
        CONSTRAINT uk_mapping_missing UNIQUE (merchant_id, footway_variant_id, integration_variant_id)
);

CREATE INDEX idx_variant_mapping_missing_integration_merchant ON variant_mapping_missing (merchant_id, integration_variant_id);
CREATE INDEX idx_variant_mapping_missing_footway_merchant ON variant_mapping_missing (merchant_id,footway_variant_id);
CREATE INDEX idx_variant_mapping_missing_cause_merchant ON variant_mapping_missing (cause, merchant_id);
