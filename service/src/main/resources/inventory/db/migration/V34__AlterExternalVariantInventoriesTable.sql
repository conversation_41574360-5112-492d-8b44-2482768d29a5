-- Rename the table
ALTER TABLE external_variant_inventories
    RENAME TO external_variants;

-- Optionally, rename indexes (if you want the index names to match the new table name)
ALTER INDEX idx_external_variant_inventories_trigram
    RENAME TO idx_external_variants_trigram;
ALTER INDEX idx_external_variant_inventories_merchant_id
    RENAME TO idx_external_variants_merchant_id;
ALTER INDEX idx_external_variant_inventories_sku
    RENAME TO idx_external_variants_sku;
ALTER INDEX idx_external_variant_inventories_ean
    RENAME TO idx_external_variants_ean;

-- Add the new column 'product_id'
-- If the table might contain data, add with a temporary default value
ALTER TABLE external_variants
    ADD COLUMN product_id TEXT NOT NULL;

ALTER TABLE external_variants
    RENAME CONSTRAINT external_variant_inventories_pkey TO external_variants_pkey;