CREATE TABLE IF NOT EXISTS shopify_variant_inventories (
                                                           merchant_id CHAR(3) NOT NULL,
    variant_id BIGINT NOT NULL,
    display_name TEXT NOT NULL,
    sku TEXT,
    ean TEXT,
    metadata JSONB,
    updated TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
                          PRIMARY KEY (merchant_id, variant_id)
    );

CREATE EXTENSION IF NOT EXISTS pg_trgm;
-- Create a simple trigram index for text search instead of full-text search
CREATE INDEX IF NOT EXISTS idx_shopify_variant_inventories_trigram ON shopify_variant_inventories USING GIN (display_name gin_trgm_ops);
CREATE INDEX IF NOT EXISTS idx_shopify_variant_inventories_merchant_id ON shopify_variant_inventories(merchant_id);
CREATE INDEX IF NOT EXISTS idx_shopify_variant_inventories_sku ON shopify_variant_inventories(sku);
CREATE INDEX IF NOT EXISTS idx_shopify_variant_inventories_ean ON shopify_variant_inventories(ean);