CREATE TABLE IF NOT EXISTS inventory (
    merchant_id CHAR(3) NOT NULL,
    variant_id BIGINT NOT NULL,
    product_name TEXT NOT NULL,
    supplier_model_number TEXT,
    EAN TEXT,
    size TEXT,
    vendor TEXT,
    quantity INTEGER,
    productType TEXT,
    productGroup TEXT,
    department TEXT,
    created TIM<PERSON><PERSON><PERSON> WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (merchant_id, variant_id)
);

CREATE EXTENSION IF NOT EXISTS pg_trgm;
-- Create a simple trigram index for text search instead of full-text search
CREATE INDEX IF NOT EXISTS idx_inventory_trigram ON inventory USING GIN (product_name gin_trgm_ops);
CREATE INDEX IF NOT EXISTS idx_inventory_merchantId ON inventory(merchant_id);
CREATE INDEX IF NOT EXISTS idx_inventory_vendor ON inventory(vendor);
CREATE INDEX IF NOT EXISTS idx_inventory_department ON inventory(department);
CREATE INDEX IF NOT EXISTS idx_inventory_productGroup ON inventory(productGroup);
CREATE INDEX IF NOT EXISTS idx_inventory_productType ON inventory(productType);