-- Step 1: Drop the existing view
DROP VIEW IF EXISTS public.msc_variant_view;

-- Step 2: Create the new view
CREATE OR REPLACE VIEW public.msc_variant_view AS
SELECT msc.multi_sales_channel_item_id AS id,
        msc.merchant_code AS merchant_id,
        il.variant_id,
        v.product_name,
        v.supplier_model_number,
        v.ean,
        v.size,
        v.vendor,
        il.quantity,
        v.product_type,
        v.product_group,
        v.department,
        v.created AS variant_created,
        v.updated AS variant_updated,
        il.created AS inventory_level_created,
        il.updated AS inventory_level_updated,
        v.image_url,
        coalesce(msc.metadata->>'price',v.price) as price,
        v.product_description
FROM multi_sales_channel_items msc
        JOIN inventory_levels il ON msc.prat_variant_id = il.variant_id
        AND msc.merchant_code = il.merchant_id  -- No need for casting anymore
        JOIN variants v ON il.variant_id = v.variant_id;