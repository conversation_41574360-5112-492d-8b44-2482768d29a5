<configuration>
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>TRACE</level>
        </filter>
        <encoder>
            <pattern>%d{HH:mm:ss.SSS} %highlight(%-5level) %boldGreen(%-52logger{52}) - %msg : [%thread]%n</pattern>
            <immediateFlush>true</immediateFlush>
        </encoder>
    </appender>
    <logger name="com.footway" level="trace" additivity="false">
        <appender-ref ref="CONSOLE" />
    </logger>
    <root level="info">
        <appender-ref ref="CONSOLE" />
    </root>
    <statusListener class="ch.qos.logback.core.status.NopStatusListener"/>
</configuration>