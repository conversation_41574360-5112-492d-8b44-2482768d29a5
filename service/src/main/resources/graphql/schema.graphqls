type Query {
    getInventory(merchantID: String!, page: Int, size: Int): InventoryResponse
}

scalar DateTime

type InventoryResponse {
    content: [InventoryItem]
    pageable: Pageable
    totalPages: Int
    totalElements: Int
    last: Boolean
    sort: Sort
    numberOfElements: Int
    first: Boolean
    size: Int
    number: Int
    empty: Boolean
}

type InventoryItem {
    onHandQty: Int
    availableQty: Int
    totalCost: Float
    resetDate: DateTime
    location: Location
    location_id: Int
    merchant: Merchant
    merchant_id: Int
    variant: Variant
    variant_id: Int
    forced: Boolean
    id: Int
    version: Int
    createdDate: DateTime
    updatedDate: DateTime
    createdBy: String
    updatedBy: String
}

type Pageable {
    sort: Sort
    pageNumber: Int
    pageSize: Int
    offset: Int
    paged: Boolean
    unpaged: Boolean
}

type Sort {
    sorted: Boolean
    unsorted: Boolean
    empty: Boolean
}

type Location {
    name: String
    address: String
    postalCode: String
    city: String
    country: String
    contactName: String
    contactTel: String
    shortName: String
    code: String
    type: String
    ediType: String
    storageSpace: Int
    locationId: Int
    version: Int
    createdDate: DateTime
    updatedDate: DateTime
    createdBy: String
    updatedBy: String
}

type Merchant {
    name: String
    code: String
    active: Boolean
    merchantId: Int
    version: Int
    createdDate: DateTime
    updatedDate: DateTime
    createdBy: String
    updatedBy: String
}

type Variant {
    variantNumber: String
    attributes: String
    ean: String
    size: String
    supplierSize: String
    sizeEuLabel: String
    sizeUkLabel: String
    sizeUsLabel: String
    weightGram: Int
    fwmsId: Int
    volumeMilliMeterCubed: Int
    longestDimensionMilliMeter: Int
    minInventoryLevel: Int
    maxInventoryLevel: Int
    fixedLotMultiplier: Int
    dead: Boolean
    snsSyncEnabled: Boolean
    allowBackorder: Boolean
    sourceRef: String
    onHandHbg: Int
    onHandKjula: Int
    existsHbg: Boolean
    existsKjula: Boolean
    product: Product
    barcodes: [String]
    id: Int
    version: Int
    createdDate: DateTime
    updatedDate: DateTime
    createdBy: String
    updatedBy: String
}

type Product {
    productNumber: String
    attributes: Attributes
    sourceRef: String
    id: Int
    version: Int
    createdDate: DateTime
    updatedDate: DateTime
    createdBy: String
    updatedBy: String
}

type Attributes {
    dead: Boolean
    brand: String
    price: [KeyValueInt]
    store: [String]
    active: [KeyValueBoolean]
    images: [Image]
    season: String
    urlKey: String
    visible: Boolean
    newsDate: DateTime
    popularity: [KeyValueFloat]
    globalTexts: [GlobalText]
    intrastatNo: String
    priceReason: [KeyValueString]
    productName: String
    productOwner: String
    purchaseType: String
    specialPrice: [KeyValueInt]
    manualTagCloud: [Tag]
    countryOfOrigin: String
    purchasePriceSek: Float
    automaticTagCloud: [Tag]
    productPerformance: ProductPerformance
    supplierColorName: String
    supplierModelName: String
    specialPriceReason: [KeyValueString]
    lastInboundDelivery: DateTime
    supplierModelNumber: String
    firstInboundDelivery: DateTime
    recommendedRetailPrice: [KeyValueInt]
    recommendedRetailPriceReason: [KeyValueString]
}

type Image {
    url: String
    meta: Meta
    type: String
    listImage: Boolean
    automaticMeta: AutomaticMeta
}

type Meta {
    size: String
    lastUpdated: String
    originalFile: String
}

type AutomaticMeta {
    view: String
    width: Int
    height: Int
    status: String
    feature: [String]
}

type GlobalText {
    key: String
    score: Float
    value: String
    source: String
}

type Tag {
    key: String
    value: String
}

type ProductPerformance {
    SPE14: Float
    SPE60: Float
    maxSkuQty: Int
    minSkuQty: Int
    sumSkuQty: Int
    skuSizeCount: Int
    sumReturnQty14: Float
    sumReturnQty60: Float
    sumGrossAmt14: Float
    sumGrossAmt60: Float
    sumProdCost14: Float
    sumProdCost60: Float
    sumSalesAmt14: Float
    sumSalesAmt60: Float
    sumSkuinstock: Int
    avgSkuPoprice: Float
    startOfSalesDay: DateTime
    sumReturnQty365: Float
    sumGrossAmt365: Float
    sumProdCost365: Float
    sumSalesAmt365: Float
    sumDaysInStock14: Int
    sumDaysInStock60: Int
    sizeCoverageProc: Float
    sumDelivSalesQty14: Int
    sumDelivSalesQty60: Int
    geometricAvgPoprice: Float
    sumDelivSalesQty365: Int
    sumSalesAmtPreCartDisc14: Float
    sumSalesAmtPreCartDisc60: Float
    procReturnsOnOrder30Momentan: Float
}


type KeyValueString {
    key: String
    value: String
}

type KeyValueFloat {
    key: String
    value: Float
}

type KeyValueInt {
    key: String
    value: Int
}

type KeyValueBoolean {
    key: String
    value: Boolean
}
