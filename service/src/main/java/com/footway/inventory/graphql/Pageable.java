package com.footway.inventory.graphql;


@javax.annotation.processing.Generated(
    value = "com.kobylynskyi.graphql.codegen.GraphQLCodegen",
    date = "2024-05-24T09:49:27+0200"
)
public class Pageable implements java.io.Serializable {

    private static final long serialVersionUID = 1L;

    private Sort sort;
    private Integer pageNumber;
    private Integer pageSize;
    private Integer offset;
    private Boolean paged;
    private Boolean unpaged;

    public Pageable() {
    }

    public Pageable(Sort sort, Integer pageNumber, Integer pageSize, Integer offset, Boolean paged, Boolean unpaged) {
        this.sort = sort;
        this.pageNumber = pageNumber;
        this.pageSize = pageSize;
        this.offset = offset;
        this.paged = paged;
        this.unpaged = unpaged;
    }

    public Sort getSort() {
        return sort;
    }
    public void setSort(Sort sort) {
        this.sort = sort;
    }

    public Integer getPageNumber() {
        return pageNumber;
    }
    public void setPageNumber(Integer pageNumber) {
        this.pageNumber = pageNumber;
    }

    public Integer getPageSize() {
        return pageSize;
    }
    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Integer getOffset() {
        return offset;
    }
    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Boolean getPaged() {
        return paged;
    }
    public void setPaged(Boolean paged) {
        this.paged = paged;
    }

    public Boolean getUnpaged() {
        return unpaged;
    }
    public void setUnpaged(Boolean unpaged) {
        this.unpaged = unpaged;
    }



    public static Builder builder() {
        return new Builder();
    }

    @javax.annotation.processing.Generated(
        value = "com.kobylynskyi.graphql.codegen.GraphQLCodegen",
        date = "2024-05-24T09:49:27+0200"
    )
    public static class Builder {

        private Sort sort;
        private Integer pageNumber;
        private Integer pageSize;
        private Integer offset;
        private Boolean paged;
        private Boolean unpaged;

        public Builder() {
        }

        public Builder setSort(Sort sort) {
            this.sort = sort;
            return this;
        }

        public Builder setPageNumber(Integer pageNumber) {
            this.pageNumber = pageNumber;
            return this;
        }

        public Builder setPageSize(Integer pageSize) {
            this.pageSize = pageSize;
            return this;
        }

        public Builder setOffset(Integer offset) {
            this.offset = offset;
            return this;
        }

        public Builder setPaged(Boolean paged) {
            this.paged = paged;
            return this;
        }

        public Builder setUnpaged(Boolean unpaged) {
            this.unpaged = unpaged;
            return this;
        }


        public Pageable build() {
            return new Pageable(sort, pageNumber, pageSize, offset, paged, unpaged);
        }

    }
}
