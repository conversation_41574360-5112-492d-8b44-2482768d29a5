package com.footway.inventory.graphql.mapper;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.footway.inventory.graphql.*;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.OffsetDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.function.Consumer;

@Slf4j
public class InventoryMapper {

    private static final Logger LOGGER = LoggerFactory.getLogger(InventoryMapper.class);
    private static List<SimpleDateFormat> formatters;

    private static List<SimpleDateFormat> formatters() {
        if (formatters == null) {
            formatters = new ArrayList<>();
            formatters.add(new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS"));
            formatters.add(new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss"));
        }
        return formatters;
    }

    public static InventoryResponse restToGraphQL(JsonNode root) {
        InventoryResponse inventoryResponse = new InventoryResponse();
        setIfPresent(root, "empty", inventoryResponse::setEmpty, JsonNode::asBoolean);
        setIfPresent(root, "number", inventoryResponse::setNumber, JsonNode::asInt);
        setIfPresent(root, "size", inventoryResponse::setSize, JsonNode::asInt);
        setIfPresent(root, "first", inventoryResponse::setFirst, JsonNode::asBoolean);
        setIfPresent(root, "numberOfElements", inventoryResponse::setNumberOfElements, JsonNode::asInt);
        setIfPresent(root, "last", inventoryResponse::setLast, JsonNode::asBoolean);
        setIfPresent(root, "totalElements", inventoryResponse::setTotalElements, JsonNode::asInt);
        setIfPresent(root, "totalPages", inventoryResponse::setTotalPages, JsonNode::asInt);

        inventoryResponse.setSort(mapSort(root.get("sort")));
        inventoryResponse.setPageable(mapPageable(root.get("pageable")));

        ArrayNode contentIn = (ArrayNode) root.get("content");
        List<InventoryItem> items = new ArrayList<>();
        inventoryResponse.setContent(items);

        try {
            for (int i = 0; i < contentIn.size(); i++) {
                items.add(mapInventoryItem(contentIn.get(i)));
            }
        } catch (Exception e) {
            LOGGER.debug("Error mapping inventory items: {} json: ----\n{}\n----", e.getMessage(), root, e);
        }

        return inventoryResponse;
    }

    private static Sort mapSort(JsonNode sortIn) {
        Sort sort = new Sort();
        if (sortIn != null) {
            setIfPresent(sortIn, "sorted", sort::setSorted, JsonNode::asBoolean);
            setIfPresent(sortIn, "unsorted", sort::setUnsorted, JsonNode::asBoolean);
            setIfPresent(sortIn, "empty", sort::setEmpty, JsonNode::asBoolean);
        }
        return sort;
    }

    private static Pageable mapPageable(JsonNode pageableIn) {
        Pageable pageable = new Pageable();
        if (pageableIn != null) {
            setIfPresent(pageableIn, "pageSize", pageable::setPageSize, JsonNode::asInt);
            setIfPresent(pageableIn, "pageNumber", pageable::setPageNumber, JsonNode::asInt);
            setIfPresent(pageableIn, "offset", pageable::setOffset, JsonNode::asInt);
            setIfPresent(pageableIn, "paged", pageable::setPaged, JsonNode::asBoolean);
            setIfPresent(pageableIn, "unpaged", pageable::setUnpaged, JsonNode::asBoolean);

            Sort sort = mapSort(pageableIn.get("sort"));
            pageable.setSort(sort);
        }
        return pageable;
    }

    private static InventoryItem mapInventoryItem(JsonNode content) {
        InventoryItem item = new InventoryItem();

        setIfPresent(content, "onHandQty", item::setOnHandQty, JsonNode::asInt);
        setIfPresent(content, "availableQty", item::setAvailableQty, JsonNode::asInt);
        setIfPresent(content, "totalCost", item::setTotalCost, JsonNode::asDouble);
        setIfPresent(content, "location_id", item::setLocation_id, JsonNode::asInt);
        setIfPresent(content, "merchant_id", item::setMerchant_id, JsonNode::asInt);
        setIfPresent(content, "variant_id", item::setVariant_id, JsonNode::asInt);
        setIfPresent(content, "forced", item::setForced, JsonNode::asBoolean);
        setIfPresent(content, "id", item::setId, JsonNode::asInt);
        setIfPresent(content, "version", item::setVersion, JsonNode::asInt);
        setIfPresent(content, "createdBy", item::setCreatedBy, JsonNode::asText);
        setIfPresent(content, "updatedBy", item::setUpdatedBy, JsonNode::asText);

        setIfPresentDate(content, "resetDate", item::setResetDate, JsonNode::asText);
        setIfPresentDate(content, "createdDate", item::setCreatedDate, JsonNode::asText);
        setIfPresentDate(content, "updatedDate", item::setUpdatedDate, JsonNode::asText);

        item.setLocation(mapLocation(content.get("location")));
        item.setMerchant(mapMerchant(content.get("merchant")));
        item.setVariant(mapVariant(content.get("variant")));

        return item;
    }

    private static Location mapLocation(JsonNode locationIn) {
        Location location = new Location();

        setIfPresent(locationIn, "name", location::setName, JsonNode::asText);
        setIfPresent(locationIn, "address", location::setAddress, JsonNode::asText);
        setIfPresent(locationIn, "postalCode", location::setPostalCode, JsonNode::asText);
        setIfPresent(locationIn, "city", location::setCity, JsonNode::asText);
        setIfPresent(locationIn, "country", location::setCountry, JsonNode::asText);
        setIfPresent(locationIn, "contactName", location::setContactName, JsonNode::asText);
        setIfPresent(locationIn, "contactTel", location::setContactTel, JsonNode::asText);
        setIfPresent(locationIn, "shortName", location::setShortName, JsonNode::asText);
        setIfPresent(locationIn, "code", location::setCode, JsonNode::asText);
        setIfPresent(locationIn, "type", location::setType, JsonNode::asText);
        setIfPresent(locationIn, "ediType", location::setEdiType, JsonNode::asText);
        setIfPresent(locationIn, "storageSpace", location::setStorageSpace, JsonNode::asInt);
        setIfPresent(locationIn, "locationId", location::setLocationId, JsonNode::asInt);
        setIfPresent(locationIn, "version", location::setVersion, JsonNode::asInt);
        setIfPresent(locationIn, "createdBy", location::setCreatedBy, JsonNode::asText);
        setIfPresent(locationIn, "updatedBy", location::setUpdatedBy, JsonNode::asText);

        setIfPresentDate(locationIn, "createdDate", location::setCreatedDate, JsonNode::asText);
        setIfPresentDate(locationIn, "updatedDate", location::setUpdatedDate, JsonNode::asText);
        return location;
    }

    private static Merchant mapMerchant(JsonNode merchantIn) {
        Merchant merchant = new Merchant();

        setIfPresent(merchantIn, "name", merchant::setName, JsonNode::asText);
        setIfPresent(merchantIn, "code", merchant::setCode, JsonNode::asText);
        setIfPresent(merchantIn, "active", merchant::setActive, JsonNode::asBoolean);
        setIfPresent(merchantIn, "merchantId", merchant::setMerchantId, JsonNode::asInt);
        setIfPresent(merchantIn, "version", merchant::setVersion, JsonNode::asInt);
        setIfPresent(merchantIn, "createdBy", merchant::setCreatedBy, JsonNode::asText);
        setIfPresent(merchantIn, "updatedBy", merchant::setUpdatedBy, JsonNode::asText);

        setIfPresentDate(merchantIn, "createdDate", merchant::setCreatedDate, JsonNode::asText);
        setIfPresentDate(merchantIn, "updatedDate", merchant::setUpdatedDate, JsonNode::asText);
        return merchant;
    }

    private static Variant mapVariant(JsonNode variantIn) {
        Variant variant = new Variant();

        setIfPresent(variantIn, "variantNumber", variant::setVariantNumber, JsonNode::asText);
        setIfPresent(variantIn, "attributes", variant::setAttributes, JsonNode::asText);
        setIfPresent(variantIn, "ean", variant::setEan, JsonNode::asText);
        setIfPresent(variantIn, "size", variant::setSize, JsonNode::asText);
        setIfPresent(variantIn, "supplierSize", variant::setSupplierSize, JsonNode::asText);
        setIfPresent(variantIn, "sizeEuLabel", variant::setSizeEuLabel, JsonNode::asText);
        setIfPresent(variantIn, "sizeUkLabel", variant::setSizeUkLabel, JsonNode::asText);
        setIfPresent(variantIn, "sizeUsLabel", variant::setSizeUsLabel, JsonNode::asText);
        setIfPresent(variantIn, "weightGram", variant::setWeightGram, JsonNode::asInt);
        setIfPresent(variantIn, "fwmsId", variant::setFwmsId, JsonNode::asInt);
        setIfPresent(variantIn, "volumeMilliMeterCubed", variant::setVolumeMilliMeterCubed, JsonNode::asInt);
        setIfPresent(variantIn, "longestDimensionMilliMeter", variant::setLongestDimensionMilliMeter, JsonNode::asInt);
        setIfPresent(variantIn, "minInventoryLevel", variant::setMinInventoryLevel, JsonNode::asInt);
        setIfPresent(variantIn, "maxInventoryLevel", variant::setMaxInventoryLevel, JsonNode::asInt);
        setIfPresent(variantIn, "fixedLotMultiplier", variant::setFixedLotMultiplier, JsonNode::asInt);
        setIfPresent(variantIn, "dead", variant::setDead, JsonNode::asBoolean);
        setIfPresent(variantIn, "snsSyncEnabled", variant::setSnsSyncEnabled, JsonNode::asBoolean);
        setIfPresent(variantIn, "allowBackorder", variant::setAllowBackorder, JsonNode::asBoolean);
        setIfPresent(variantIn, "sourceRef", variant::setSourceRef, JsonNode::asText);
        setIfPresent(variantIn, "onHandHbg", variant::setOnHandHbg, JsonNode::asInt);
        setIfPresent(variantIn, "onHandKjula", variant::setOnHandKjula, JsonNode::asInt);
        setIfPresent(variantIn, "existsHbg", variant::setExistsHbg, JsonNode::asBoolean);
        setIfPresent(variantIn, "existsKjula", variant::setExistsKjula, JsonNode::asBoolean);
        setIfPresent(variantIn, "id", variant::setId, JsonNode::asInt);
        setIfPresent(variantIn, "version", variant::setVersion, JsonNode::asInt);
        setIfPresent(variantIn, "createdBy", variant::setCreatedBy, JsonNode::asText);
        setIfPresent(variantIn, "updatedBy", variant::setUpdatedBy, JsonNode::asText);

        setIfPresentDate(variantIn, "createdDate", variant::setCreatedDate, JsonNode::asText);
        setIfPresentDate(variantIn, "updatedDate", variant::setUpdatedDate, JsonNode::asText);
        ;

        variant.setBarcodes(mapBarcodes(variantIn));
        variant.setProduct(mapProduct(variantIn.get("product")));
        return variant;
    }

    private static List<String> mapBarcodes(JsonNode variantIn) {
        List<String> barCodes = new ArrayList<>();
        ArrayNode barcodesIn = (ArrayNode) variantIn.get("barcodes");
        for (int b = 0; b < barcodesIn.size(); b++) {
            JsonNode barcode = barcodesIn.get(b);
            barCodes.add(barcode.asText());
        }
        return barCodes;
    }

    private static Product mapProduct(JsonNode productIn) {
        Product product = new Product();

        setIfPresent(productIn, "id", product::setId, JsonNode::asInt);
        setIfPresent(productIn, "version", product::setVersion, JsonNode::asInt);
        setIfPresent(productIn, "createdBy", product::setCreatedBy, JsonNode::asText);
        setIfPresent(productIn, "updatedBy", product::setUpdatedBy, JsonNode::asText);
        setIfPresent(productIn, "sourceRef", product::setSourceRef, JsonNode::asText);
        setIfPresent(productIn, "productNumber", product::setProductNumber, JsonNode::asText);

        setIfPresentDate(productIn, "createdDate", product::setCreatedDate, JsonNode::asText);
        setIfPresentDate(productIn, "updatedDate", product::setUpdatedDate, JsonNode::asText);
        ;

        product.setAttributes(mapAttributes(productIn.get("attributes")));
        return product;
    }

    private static Attributes mapAttributes(JsonNode attributesIn) {
        Attributes attributes = new Attributes();
        setIfPresent(attributesIn, "dead", attributes::setDead, JsonNode::asBoolean);
        setIfPresent(attributesIn, "brand", attributes::setBrand, JsonNode::asText);
        setIfPresent(attributesIn, "season", attributes::setSeason, JsonNode::asText);
        setIfPresent(attributesIn, "url_key", attributes::setUrlKey, JsonNode::asText);
        setIfPresent(attributesIn, "visible", attributes::setVisible, JsonNode::asBoolean);
        setIfPresent(attributesIn, "intrastat_no", attributes::setIntrastatNo, JsonNode::asText);
        setIfPresent(attributesIn, "product_name", attributes::setProductName, JsonNode::asText);
        setIfPresent(attributesIn, "product_owner", attributes::setProductOwner, JsonNode::asText);
        setIfPresent(attributesIn, "purchase_type", attributes::setPurchaseType, JsonNode::asText);
        setIfPresent(attributesIn, "country_of_origin", attributes::setCountryOfOrigin, JsonNode::asText);
        setIfPresent(attributesIn, "purchase_price_sek", attributes::setPurchasePriceSek, JsonNode::asDouble);
        setIfPresent(attributesIn, "supplier_color_name", attributes::setSupplierColorName, JsonNode::asText);
        setIfPresent(attributesIn, "supplier_model_name", attributes::setSupplierModelName, JsonNode::asText);
        setIfPresent(attributesIn, "supplier_model_number", attributes::setSupplierModelNumber, JsonNode::asText);

        setIfPresentDate(attributesIn, "createdDate", attributes::setNewsDate, JsonNode::asText);
        setIfPresentDate(attributesIn, "updatedDate", attributes::setLastInboundDelivery, JsonNode::asText);
        ;
        setIfPresentDate(attributesIn, "updatedDate", attributes::setFirstInboundDelivery, JsonNode::asText);
        ;


        attributes.setPrice(toDictInt(attributesIn.get("price")));
        attributes.setActive(toDictBoolean(attributesIn.get("active")));
        attributes.setPopularity(toDictFloat(attributesIn.get("popularity")));
        attributes.setPriceReason(toDictString(attributesIn.get("price_reason")));
        attributes.setSpecialPrice(toDictInt(attributesIn.get("specialPrice")));
        attributes.setManualTagCloud(toDictTag(attributesIn.get("manual_tag_cloud")));
        attributes.setAutomaticTagCloud(toDictTag(attributesIn.get("automatic_tag_cloud")));
        attributes.setSpecialPriceReason(toDictString(attributesIn.get("special_price_reason")));
        attributes.setRecommendedRetailPrice(toDictInt(attributesIn.get("recommended_retail_price")));
        attributes.setRecommendedRetailPriceReason(toDictString(attributesIn.get("recommended_retail_price_reason")));
        attributes.setStore(toListString(attributesIn.get("store")));
        attributes.setImages(toListImage(attributesIn.get("images")));
        attributes.setProductPerformance(mapProductPerformance(attributesIn.get("product_performance")));
        attributes.setGlobalTexts(toListGlobalText(attributesIn.get("global_texts")));

        return attributes;
    }

    public static List<KeyValueInt> toDictInt(JsonNode dict) {
        if (dict == null) return Collections.emptyList();
        List<KeyValueInt> list = new ArrayList<>();
        Set<Map.Entry<String, JsonNode>> properties = dict.properties();
        for (Map.Entry<String, JsonNode> propery : properties) {
            list.add(new KeyValueInt(propery.getKey(), propery.getValue().asInt()));
        }
        return list;
    }

    public static List<Tag> toDictTag(JsonNode dict) {
        if (dict == null) return Collections.emptyList();
        List<Tag> list = new ArrayList<>();
        Set<Map.Entry<String, JsonNode>> properties = dict.properties();
        for (Map.Entry<String, JsonNode> propery : properties) {
            list.add(new Tag(propery.getKey(), propery.getValue().asText()));
        }
        return list;
    }

    public static List<KeyValueString> toDictString(JsonNode dict) {
        if (dict == null) return Collections.emptyList();
        List<KeyValueString> list = new ArrayList<>();
        Set<Map.Entry<String, JsonNode>> properties = dict.properties();
        for (Map.Entry<String, JsonNode> propery : properties) {
            list.add(new KeyValueString(propery.getKey(), propery.getValue().asText()));
        }
        return list;
    }

    public static List<KeyValueFloat> toDictFloat(JsonNode dict) {
        if (dict == null) return Collections.emptyList();
        List<KeyValueFloat> list = new ArrayList<>();
        Set<Map.Entry<String, JsonNode>> properties = dict.properties();
        for (Map.Entry<String, JsonNode> propery : properties) {
            list.add(new KeyValueFloat(propery.getKey(), propery.getValue().asDouble()));
        }
        return list;
    }

    public static List<KeyValueBoolean> toDictBoolean(JsonNode dict) {
        if (dict == null) return Collections.emptyList();
        List<KeyValueBoolean> list = new ArrayList<>();
        Set<Map.Entry<String, JsonNode>> properties = dict.properties();
        for (Map.Entry<String, JsonNode> propery : properties) {
            list.add(new KeyValueBoolean(propery.getKey(), propery.getValue().asBoolean()));
        }
        return list;
    }

    public static List<String> toListString(JsonNode array) {
        if (array == null) return Collections.emptyList();
        List<String> list = new ArrayList<>();
        ArrayNode arrayNode = (ArrayNode) array;
        for (int b = 0; b < arrayNode.size(); b++) {
            JsonNode barcode = arrayNode.get(b);
            list.add(barcode.asText());
        }
        return list;
    }

    public static List<Image> toListImage(JsonNode array) {
        if (array == null) return Collections.emptyList();
        List<Image> list = new ArrayList<>();
        ArrayNode arrayNode = (ArrayNode) array;
        for (int b = 0; b < arrayNode.size(); b++) {
            JsonNode imageNode = arrayNode.get(b);
            Image image = new Image();
            list.add(image);
            setIfPresent(imageNode, "list_image", image::setListImage, JsonNode::asBoolean);
            setIfPresent(imageNode, "type", image::setType, JsonNode::asText);
            setIfPresent(imageNode, "url", image::setUrl, JsonNode::asText);
            image.setUrl(imageNode.get("url").asText());
//            image.setMeta(imageNode.get("meta"));
//            image.setAutomaticMeta();
        }
        return list;
    }

    private static List<GlobalText> toListGlobalText(JsonNode globalTexts) {
        if (globalTexts == null) return Collections.emptyList();
        List<GlobalText> globalTextList = new ArrayList<>();
        ArrayNode arrayNode = (ArrayNode) globalTexts;
        for (int b = 0; b < arrayNode.size(); b++) {
            JsonNode textNode = arrayNode.get(b);
            GlobalText globalText = new GlobalText();
            globalTextList.add(globalText);
            setIfPresent(textNode, "key", globalText::setKey, JsonNode::asText);
            setIfPresent(textNode, "score", globalText::setScore, JsonNode::asDouble);
            setIfPresent(textNode, "source", globalText::setSource, JsonNode::asText);
            setIfPresent(textNode, "value", globalText::setValue, JsonNode::asText);
        }
        return globalTextList;
    }

    private static ProductPerformance mapProductPerformance(JsonNode productPerformance) {
        if (productPerformance == null) return null;
        //FIXME: add implementation
        return new ProductPerformance();
    }

    public static java.time.OffsetDateTime toDate(String dateString) {
        if (dateString == null || dateString.isEmpty()) {
            return null;
        } else {
            for (SimpleDateFormat formatter : formatters()) {
                try {
                    Date date = formatter.parse(dateString);
                    Instant instant = date.toInstant();
                    return instant.atOffset(ZoneId.systemDefault().getRules().getOffset(instant));
                } catch (ParseException e) {
                    //Do nothing
                }
            }
            throw new RuntimeException("Could not parse " + dateString + " as a date");
        }
    }

    public static <T> void setIfPresentDate(JsonNode node, String fieldName, Consumer<OffsetDateTime> setter, ValueMapper<T> mapper) {
        JsonNode fieldNode = node.path(fieldName);
        if (!fieldNode.isMissingNode() && !fieldNode.isNull()) {
            T value = mapper.map(fieldNode);
            setter.accept(toDate((String) value));
        }
    }

    public static <T> void setIfPresent(JsonNode node, String fieldName, Consumer<T> setter, ValueMapper<T> mapper) {
        JsonNode fieldNode = node.path(fieldName);
        if (!fieldNode.isMissingNode() && !fieldNode.isNull()) {
            T value = mapper.map(fieldNode);
            setter.accept(value);
        }
    }

    @FunctionalInterface
    public interface ValueMapper<T> {
        T map(JsonNode node);
    }
}
