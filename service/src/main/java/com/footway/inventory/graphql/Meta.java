package com.footway.inventory.graphql;


@javax.annotation.processing.Generated(
    value = "com.kobylynskyi.graphql.codegen.GraphQLCodegen",
    date = "2024-05-24T09:49:27+0200"
)
public class Meta implements java.io.Serializable {

    private static final long serialVersionUID = 1L;

    private String size;
    private String lastUpdated;
    private String originalFile;

    public Meta() {
    }

    public Meta(String size, String lastUpdated, String originalFile) {
        this.size = size;
        this.lastUpdated = lastUpdated;
        this.originalFile = originalFile;
    }

    public String getSize() {
        return size;
    }
    public void setSize(String size) {
        this.size = size;
    }

    public String getLastUpdated() {
        return lastUpdated;
    }
    public void setLastUpdated(String lastUpdated) {
        this.lastUpdated = lastUpdated;
    }

    public String getOriginalFile() {
        return originalFile;
    }
    public void setOriginalFile(String originalFile) {
        this.originalFile = originalFile;
    }



    public static Builder builder() {
        return new Builder();
    }

    @javax.annotation.processing.Generated(
        value = "com.kobylynskyi.graphql.codegen.GraphQLCodegen",
        date = "2024-05-24T09:49:27+0200"
    )
    public static class Builder {

        private String size;
        private String lastUpdated;
        private String originalFile;

        public Builder() {
        }

        public Builder setSize(String size) {
            this.size = size;
            return this;
        }

        public Builder setLastUpdated(String lastUpdated) {
            this.lastUpdated = lastUpdated;
            return this;
        }

        public Builder setOriginalFile(String originalFile) {
            this.originalFile = originalFile;
            return this;
        }


        public Meta build() {
            return new Meta(size, lastUpdated, originalFile);
        }

    }
}
