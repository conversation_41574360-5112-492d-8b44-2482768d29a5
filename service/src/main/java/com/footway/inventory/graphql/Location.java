package com.footway.inventory.graphql;


@javax.annotation.processing.Generated(
    value = "com.kobylynskyi.graphql.codegen.GraphQLCodegen",
    date = "2024-05-24T09:49:27+0200"
)
public class Location implements java.io.Serializable {

    private static final long serialVersionUID = 1L;

    private String name;
    private String address;
    private String postalCode;
    private String city;
    private String country;
    private String contactName;
    private String contactTel;
    private String shortName;
    private String code;
    private String type;
    private String ediType;
    private Integer storageSpace;
    private Integer locationId;
    private Integer version;
    private java.time.OffsetDateTime createdDate;
    private java.time.OffsetDateTime updatedDate;
    private String createdBy;
    private String updatedBy;

    public Location() {
    }

    public Location(String name, String address, String postalCode, String city, String country, String contactName, String contactTel, String shortName, String code, String type, String ediType, Integer storageSpace, Integer locationId, Integer version, java.time.OffsetDateTime createdDate, java.time.OffsetDateTime updatedDate, String createdBy, String updatedBy) {
        this.name = name;
        this.address = address;
        this.postalCode = postalCode;
        this.city = city;
        this.country = country;
        this.contactName = contactName;
        this.contactTel = contactTel;
        this.shortName = shortName;
        this.code = code;
        this.type = type;
        this.ediType = ediType;
        this.storageSpace = storageSpace;
        this.locationId = locationId;
        this.version = version;
        this.createdDate = createdDate;
        this.updatedDate = updatedDate;
        this.createdBy = createdBy;
        this.updatedBy = updatedBy;
    }

    public String getName() {
        return name;
    }
    public void setName(String name) {
        this.name = name;
    }

    public String getAddress() {
        return address;
    }
    public void setAddress(String address) {
        this.address = address;
    }

    public String getPostalCode() {
        return postalCode;
    }
    public void setPostalCode(String postalCode) {
        this.postalCode = postalCode;
    }

    public String getCity() {
        return city;
    }
    public void setCity(String city) {
        this.city = city;
    }

    public String getCountry() {
        return country;
    }
    public void setCountry(String country) {
        this.country = country;
    }

    public String getContactName() {
        return contactName;
    }
    public void setContactName(String contactName) {
        this.contactName = contactName;
    }

    public String getContactTel() {
        return contactTel;
    }
    public void setContactTel(String contactTel) {
        this.contactTel = contactTel;
    }

    public String getShortName() {
        return shortName;
    }
    public void setShortName(String shortName) {
        this.shortName = shortName;
    }

    public String getCode() {
        return code;
    }
    public void setCode(String code) {
        this.code = code;
    }

    public String getType() {
        return type;
    }
    public void setType(String type) {
        this.type = type;
    }

    public String getEdiType() {
        return ediType;
    }
    public void setEdiType(String ediType) {
        this.ediType = ediType;
    }

    public Integer getStorageSpace() {
        return storageSpace;
    }
    public void setStorageSpace(Integer storageSpace) {
        this.storageSpace = storageSpace;
    }

    public Integer getLocationId() {
        return locationId;
    }
    public void setLocationId(Integer locationId) {
        this.locationId = locationId;
    }

    public Integer getVersion() {
        return version;
    }
    public void setVersion(Integer version) {
        this.version = version;
    }

    public java.time.OffsetDateTime getCreatedDate() {
        return createdDate;
    }
    public void setCreatedDate(java.time.OffsetDateTime createdDate) {
        this.createdDate = createdDate;
    }

    public java.time.OffsetDateTime getUpdatedDate() {
        return updatedDate;
    }
    public void setUpdatedDate(java.time.OffsetDateTime updatedDate) {
        this.updatedDate = updatedDate;
    }

    public String getCreatedBy() {
        return createdBy;
    }
    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }
    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }



    public static Builder builder() {
        return new Builder();
    }

    @javax.annotation.processing.Generated(
        value = "com.kobylynskyi.graphql.codegen.GraphQLCodegen",
        date = "2024-05-24T09:49:27+0200"
    )
    public static class Builder {

        private String name;
        private String address;
        private String postalCode;
        private String city;
        private String country;
        private String contactName;
        private String contactTel;
        private String shortName;
        private String code;
        private String type;
        private String ediType;
        private Integer storageSpace;
        private Integer locationId;
        private Integer version;
        private java.time.OffsetDateTime createdDate;
        private java.time.OffsetDateTime updatedDate;
        private String createdBy;
        private String updatedBy;

        public Builder() {
        }

        public Builder setName(String name) {
            this.name = name;
            return this;
        }

        public Builder setAddress(String address) {
            this.address = address;
            return this;
        }

        public Builder setPostalCode(String postalCode) {
            this.postalCode = postalCode;
            return this;
        }

        public Builder setCity(String city) {
            this.city = city;
            return this;
        }

        public Builder setCountry(String country) {
            this.country = country;
            return this;
        }

        public Builder setContactName(String contactName) {
            this.contactName = contactName;
            return this;
        }

        public Builder setContactTel(String contactTel) {
            this.contactTel = contactTel;
            return this;
        }

        public Builder setShortName(String shortName) {
            this.shortName = shortName;
            return this;
        }

        public Builder setCode(String code) {
            this.code = code;
            return this;
        }

        public Builder setType(String type) {
            this.type = type;
            return this;
        }

        public Builder setEdiType(String ediType) {
            this.ediType = ediType;
            return this;
        }

        public Builder setStorageSpace(Integer storageSpace) {
            this.storageSpace = storageSpace;
            return this;
        }

        public Builder setLocationId(Integer locationId) {
            this.locationId = locationId;
            return this;
        }

        public Builder setVersion(Integer version) {
            this.version = version;
            return this;
        }

        public Builder setCreatedDate(java.time.OffsetDateTime createdDate) {
            this.createdDate = createdDate;
            return this;
        }

        public Builder setUpdatedDate(java.time.OffsetDateTime updatedDate) {
            this.updatedDate = updatedDate;
            return this;
        }

        public Builder setCreatedBy(String createdBy) {
            this.createdBy = createdBy;
            return this;
        }

        public Builder setUpdatedBy(String updatedBy) {
            this.updatedBy = updatedBy;
            return this;
        }


        public Location build() {
            return new Location(name, address, postalCode, city, country, contactName, contactTel, shortName, code, type, ediType, storageSpace, locationId, version, createdDate, updatedDate, createdBy, updatedBy);
        }

    }
}
