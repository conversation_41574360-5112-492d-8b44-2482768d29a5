package com.footway.inventory.graphql;


@javax.annotation.processing.Generated(
    value = "com.kobylynskyi.graphql.codegen.GraphQLCodegen",
    date = "2024-05-24T09:49:27+0200"
)
public class Product implements java.io.Serializable {

    private static final long serialVersionUID = 1L;

    private String productNumber;
    private Attributes attributes;
    private String sourceRef;
    private Integer id;
    private Integer version;
    private java.time.OffsetDateTime createdDate;
    private java.time.OffsetDateTime updatedDate;
    private String createdBy;
    private String updatedBy;

    public Product() {
    }

    public Product(String productNumber, Attributes attributes, String sourceRef, Integer id, Integer version, java.time.OffsetDateTime createdDate, java.time.OffsetDateTime updatedDate, String createdBy, String updatedBy) {
        this.productNumber = productNumber;
        this.attributes = attributes;
        this.sourceRef = sourceRef;
        this.id = id;
        this.version = version;
        this.createdDate = createdDate;
        this.updatedDate = updatedDate;
        this.createdBy = createdBy;
        this.updatedBy = updatedBy;
    }

    public String getProductNumber() {
        return productNumber;
    }
    public void setProductNumber(String productNumber) {
        this.productNumber = productNumber;
    }

    public Attributes getAttributes() {
        return attributes;
    }
    public void setAttributes(Attributes attributes) {
        this.attributes = attributes;
    }

    public String getSourceRef() {
        return sourceRef;
    }
    public void setSourceRef(String sourceRef) {
        this.sourceRef = sourceRef;
    }

    public Integer getId() {
        return id;
    }
    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getVersion() {
        return version;
    }
    public void setVersion(Integer version) {
        this.version = version;
    }

    public java.time.OffsetDateTime getCreatedDate() {
        return createdDate;
    }
    public void setCreatedDate(java.time.OffsetDateTime createdDate) {
        this.createdDate = createdDate;
    }

    public java.time.OffsetDateTime getUpdatedDate() {
        return updatedDate;
    }
    public void setUpdatedDate(java.time.OffsetDateTime updatedDate) {
        this.updatedDate = updatedDate;
    }

    public String getCreatedBy() {
        return createdBy;
    }
    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }
    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }



    public static Builder builder() {
        return new Builder();
    }

    @javax.annotation.processing.Generated(
        value = "com.kobylynskyi.graphql.codegen.GraphQLCodegen",
        date = "2024-05-24T09:49:27+0200"
    )
    public static class Builder {

        private String productNumber;
        private Attributes attributes;
        private String sourceRef;
        private Integer id;
        private Integer version;
        private java.time.OffsetDateTime createdDate;
        private java.time.OffsetDateTime updatedDate;
        private String createdBy;
        private String updatedBy;

        public Builder() {
        }

        public Builder setProductNumber(String productNumber) {
            this.productNumber = productNumber;
            return this;
        }

        public Builder setAttributes(Attributes attributes) {
            this.attributes = attributes;
            return this;
        }

        public Builder setSourceRef(String sourceRef) {
            this.sourceRef = sourceRef;
            return this;
        }

        public Builder setId(Integer id) {
            this.id = id;
            return this;
        }

        public Builder setVersion(Integer version) {
            this.version = version;
            return this;
        }

        public Builder setCreatedDate(java.time.OffsetDateTime createdDate) {
            this.createdDate = createdDate;
            return this;
        }

        public Builder setUpdatedDate(java.time.OffsetDateTime updatedDate) {
            this.updatedDate = updatedDate;
            return this;
        }

        public Builder setCreatedBy(String createdBy) {
            this.createdBy = createdBy;
            return this;
        }

        public Builder setUpdatedBy(String updatedBy) {
            this.updatedBy = updatedBy;
            return this;
        }


        public Product build() {
            return new Product(productNumber, attributes, sourceRef, id, version, createdDate, updatedDate, createdBy, updatedBy);
        }

    }
}
