package com.footway.inventory.graphql;


@javax.annotation.processing.Generated(
    value = "com.kobylynskyi.graphql.codegen.GraphQLCodegen",
    date = "2024-05-24T09:49:27+0200"
)
public class GlobalText implements java.io.Serializable {

    private static final long serialVersionUID = 1L;

    private String key;
    private Double score;
    private String value;
    private String source;

    public GlobalText() {
    }

    public GlobalText(String key, Double score, String value, String source) {
        this.key = key;
        this.score = score;
        this.value = value;
        this.source = source;
    }

    public String getKey() {
        return key;
    }
    public void setKey(String key) {
        this.key = key;
    }

    public Double getScore() {
        return score;
    }
    public void setScore(Double score) {
        this.score = score;
    }

    public String getValue() {
        return value;
    }
    public void setValue(String value) {
        this.value = value;
    }

    public String getSource() {
        return source;
    }
    public void setSource(String source) {
        this.source = source;
    }



    public static Builder builder() {
        return new Builder();
    }

    @javax.annotation.processing.Generated(
        value = "com.kobylynskyi.graphql.codegen.GraphQLCodegen",
        date = "2024-05-24T09:49:27+0200"
    )
    public static class Builder {

        private String key;
        private Double score;
        private String value;
        private String source;

        public Builder() {
        }

        public Builder setKey(String key) {
            this.key = key;
            return this;
        }

        public Builder setScore(Double score) {
            this.score = score;
            return this;
        }

        public Builder setValue(String value) {
            this.value = value;
            return this;
        }

        public Builder setSource(String source) {
            this.source = source;
            return this;
        }


        public GlobalText build() {
            return new GlobalText(key, score, value, source);
        }

    }
}
