package com.footway.inventory.graphql;


@javax.annotation.processing.Generated(
    value = "com.kobylynskyi.graphql.codegen.GraphQLCodegen",
    date = "2024-05-24T09:49:27+0200"
)
public class InventoryResponse implements java.io.Serializable {

    private static final long serialVersionUID = 1L;

    private java.util.List<InventoryItem> content;
    private Pageable pageable;
    private Integer totalPages;
    private Integer totalElements;
    private Boolean last;
    private Sort sort;
    private Integer numberOfElements;
    private Boolean first;
    private Integer size;
    private Integer number;
    private Boolean empty;

    public InventoryResponse() {
    }

    public InventoryResponse(java.util.List<InventoryItem> content, Pageable pageable, Integer totalPages, Integer totalElements, Boolean last, Sort sort, Integer numberOfElements, Boolean first, Integer size, Integer number, Boolean empty) {
        this.content = content;
        this.pageable = pageable;
        this.totalPages = totalPages;
        this.totalElements = totalElements;
        this.last = last;
        this.sort = sort;
        this.numberOfElements = numberOfElements;
        this.first = first;
        this.size = size;
        this.number = number;
        this.empty = empty;
    }

    public java.util.List<InventoryItem> getContent() {
        return content;
    }
    public void setContent(java.util.List<InventoryItem> content) {
        this.content = content;
    }

    public Pageable getPageable() {
        return pageable;
    }
    public void setPageable(Pageable pageable) {
        this.pageable = pageable;
    }

    public Integer getTotalPages() {
        return totalPages;
    }
    public void setTotalPages(Integer totalPages) {
        this.totalPages = totalPages;
    }

    public Integer getTotalElements() {
        return totalElements;
    }
    public void setTotalElements(Integer totalElements) {
        this.totalElements = totalElements;
    }

    public Boolean getLast() {
        return last;
    }
    public void setLast(Boolean last) {
        this.last = last;
    }

    public Sort getSort() {
        return sort;
    }
    public void setSort(Sort sort) {
        this.sort = sort;
    }

    public Integer getNumberOfElements() {
        return numberOfElements;
    }
    public void setNumberOfElements(Integer numberOfElements) {
        this.numberOfElements = numberOfElements;
    }

    public Boolean getFirst() {
        return first;
    }
    public void setFirst(Boolean first) {
        this.first = first;
    }

    public Integer getSize() {
        return size;
    }
    public void setSize(Integer size) {
        this.size = size;
    }

    public Integer getNumber() {
        return number;
    }
    public void setNumber(Integer number) {
        this.number = number;
    }

    public Boolean getEmpty() {
        return empty;
    }
    public void setEmpty(Boolean empty) {
        this.empty = empty;
    }



    public static Builder builder() {
        return new Builder();
    }

    @javax.annotation.processing.Generated(
        value = "com.kobylynskyi.graphql.codegen.GraphQLCodegen",
        date = "2024-05-24T09:49:27+0200"
    )
    public static class Builder {

        private java.util.List<InventoryItem> content;
        private Pageable pageable;
        private Integer totalPages;
        private Integer totalElements;
        private Boolean last;
        private Sort sort;
        private Integer numberOfElements;
        private Boolean first;
        private Integer size;
        private Integer number;
        private Boolean empty;

        public Builder() {
        }

        public Builder setContent(java.util.List<InventoryItem> content) {
            this.content = content;
            return this;
        }

        public Builder setPageable(Pageable pageable) {
            this.pageable = pageable;
            return this;
        }

        public Builder setTotalPages(Integer totalPages) {
            this.totalPages = totalPages;
            return this;
        }

        public Builder setTotalElements(Integer totalElements) {
            this.totalElements = totalElements;
            return this;
        }

        public Builder setLast(Boolean last) {
            this.last = last;
            return this;
        }

        public Builder setSort(Sort sort) {
            this.sort = sort;
            return this;
        }

        public Builder setNumberOfElements(Integer numberOfElements) {
            this.numberOfElements = numberOfElements;
            return this;
        }

        public Builder setFirst(Boolean first) {
            this.first = first;
            return this;
        }

        public Builder setSize(Integer size) {
            this.size = size;
            return this;
        }

        public Builder setNumber(Integer number) {
            this.number = number;
            return this;
        }

        public Builder setEmpty(Boolean empty) {
            this.empty = empty;
            return this;
        }


        public InventoryResponse build() {
            return new InventoryResponse(content, pageable, totalPages, totalElements, last, sort, numberOfElements, first, size, number, empty);
        }

    }
}
