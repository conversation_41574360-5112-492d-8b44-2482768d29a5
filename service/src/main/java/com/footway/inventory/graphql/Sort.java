package com.footway.inventory.graphql;


@javax.annotation.processing.Generated(
    value = "com.kobylynskyi.graphql.codegen.GraphQLCodegen",
    date = "2024-05-24T09:49:27+0200"
)
public class Sort implements java.io.Serializable {

    private static final long serialVersionUID = 1L;

    private Boolean sorted;
    private Boolean unsorted;
    private Boolean empty;

    public Sort() {
    }

    public Sort(Boolean sorted, Boolean unsorted, Boolean empty) {
        this.sorted = sorted;
        this.unsorted = unsorted;
        this.empty = empty;
    }

    public Boolean getSorted() {
        return sorted;
    }
    public void setSorted(Boolean sorted) {
        this.sorted = sorted;
    }

    public Boolean getUnsorted() {
        return unsorted;
    }
    public void setUnsorted(Boolean unsorted) {
        this.unsorted = unsorted;
    }

    public Boolean getEmpty() {
        return empty;
    }
    public void setEmpty(Boolean empty) {
        this.empty = empty;
    }



    public static Builder builder() {
        return new Builder();
    }

    @javax.annotation.processing.Generated(
        value = "com.kobylynskyi.graphql.codegen.GraphQLCodegen",
        date = "2024-05-24T09:49:27+0200"
    )
    public static class Builder {

        private Boolean sorted;
        private Boolean unsorted;
        private Boolean empty;

        public Builder() {
        }

        public Builder setSorted(Boolean sorted) {
            this.sorted = sorted;
            return this;
        }

        public Builder setUnsorted(Boolean unsorted) {
            this.unsorted = unsorted;
            return this;
        }

        public Builder setEmpty(Boolean empty) {
            this.empty = empty;
            return this;
        }


        public Sort build() {
            return new Sort(sorted, unsorted, empty);
        }

    }
}
