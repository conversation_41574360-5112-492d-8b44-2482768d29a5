package com.footway.inventory.graphql;


@javax.annotation.processing.Generated(
    value = "com.kobylynskyi.graphql.codegen.GraphQLCodegen",
    date = "2024-05-24T09:49:27+0200"
)
public class InventoryItem implements java.io.Serializable {

    private static final long serialVersionUID = 1L;

    private Integer onHandQty;
    private Integer availableQty;
    private Double totalCost;
    private java.time.OffsetDateTime resetDate;
    private Location location;
    private Integer location_id;
    private Merchant merchant;
    private Integer merchant_id;
    private Variant variant;
    private Integer variant_id;
    private Boolean forced;
    private Integer id;
    private Integer version;
    private java.time.OffsetDateTime createdDate;
    private java.time.OffsetDateTime updatedDate;
    private String createdBy;
    private String updatedBy;

    public InventoryItem() {
    }

    public InventoryItem(Integer onHandQty, Integer availableQty, Double totalCost, java.time.OffsetDateTime resetDate, Location location, Integer location_id, Merchant merchant, Integer merchant_id, Variant variant, Integer variant_id, Boolean forced, Integer id, Integer version, java.time.OffsetDateTime createdDate, java.time.OffsetDateTime updatedDate, String createdBy, String updatedBy) {
        this.onHandQty = onHandQty;
        this.availableQty = availableQty;
        this.totalCost = totalCost;
        this.resetDate = resetDate;
        this.location = location;
        this.location_id = location_id;
        this.merchant = merchant;
        this.merchant_id = merchant_id;
        this.variant = variant;
        this.variant_id = variant_id;
        this.forced = forced;
        this.id = id;
        this.version = version;
        this.createdDate = createdDate;
        this.updatedDate = updatedDate;
        this.createdBy = createdBy;
        this.updatedBy = updatedBy;
    }

    public Integer getOnHandQty() {
        return onHandQty;
    }
    public void setOnHandQty(Integer onHandQty) {
        this.onHandQty = onHandQty;
    }

    public Integer getAvailableQty() {
        return availableQty;
    }
    public void setAvailableQty(Integer availableQty) {
        this.availableQty = availableQty;
    }

    public Double getTotalCost() {
        return totalCost;
    }
    public void setTotalCost(Double totalCost) {
        this.totalCost = totalCost;
    }

    public java.time.OffsetDateTime getResetDate() {
        return resetDate;
    }
    public void setResetDate(java.time.OffsetDateTime resetDate) {
        this.resetDate = resetDate;
    }

    public Location getLocation() {
        return location;
    }
    public void setLocation(Location location) {
        this.location = location;
    }

    public Integer getLocation_id() {
        return location_id;
    }
    public void setLocation_id(Integer location_id) {
        this.location_id = location_id;
    }

    public Merchant getMerchant() {
        return merchant;
    }
    public void setMerchant(Merchant merchant) {
        this.merchant = merchant;
    }

    public Integer getMerchant_id() {
        return merchant_id;
    }
    public void setMerchant_id(Integer merchant_id) {
        this.merchant_id = merchant_id;
    }

    public Variant getVariant() {
        return variant;
    }
    public void setVariant(Variant variant) {
        this.variant = variant;
    }

    public Integer getVariant_id() {
        return variant_id;
    }
    public void setVariant_id(Integer variant_id) {
        this.variant_id = variant_id;
    }

    public Boolean getForced() {
        return forced;
    }
    public void setForced(Boolean forced) {
        this.forced = forced;
    }

    public Integer getId() {
        return id;
    }
    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getVersion() {
        return version;
    }
    public void setVersion(Integer version) {
        this.version = version;
    }

    public java.time.OffsetDateTime getCreatedDate() {
        return createdDate;
    }
    public void setCreatedDate(java.time.OffsetDateTime createdDate) {
        this.createdDate = createdDate;
    }

    public java.time.OffsetDateTime getUpdatedDate() {
        return updatedDate;
    }
    public void setUpdatedDate(java.time.OffsetDateTime updatedDate) {
        this.updatedDate = updatedDate;
    }

    public String getCreatedBy() {
        return createdBy;
    }
    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }
    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }



    public static Builder builder() {
        return new Builder();
    }

    @javax.annotation.processing.Generated(
        value = "com.kobylynskyi.graphql.codegen.GraphQLCodegen",
        date = "2024-05-24T09:49:27+0200"
    )
    public static class Builder {

        private Integer onHandQty;
        private Integer availableQty;
        private Double totalCost;
        private java.time.OffsetDateTime resetDate;
        private Location location;
        private Integer location_id;
        private Merchant merchant;
        private Integer merchant_id;
        private Variant variant;
        private Integer variant_id;
        private Boolean forced;
        private Integer id;
        private Integer version;
        private java.time.OffsetDateTime createdDate;
        private java.time.OffsetDateTime updatedDate;
        private String createdBy;
        private String updatedBy;

        public Builder() {
        }

        public Builder setOnHandQty(Integer onHandQty) {
            this.onHandQty = onHandQty;
            return this;
        }

        public Builder setAvailableQty(Integer availableQty) {
            this.availableQty = availableQty;
            return this;
        }

        public Builder setTotalCost(Double totalCost) {
            this.totalCost = totalCost;
            return this;
        }

        public Builder setResetDate(java.time.OffsetDateTime resetDate) {
            this.resetDate = resetDate;
            return this;
        }

        public Builder setLocation(Location location) {
            this.location = location;
            return this;
        }

        public Builder setLocation_id(Integer location_id) {
            this.location_id = location_id;
            return this;
        }

        public Builder setMerchant(Merchant merchant) {
            this.merchant = merchant;
            return this;
        }

        public Builder setMerchant_id(Integer merchant_id) {
            this.merchant_id = merchant_id;
            return this;
        }

        public Builder setVariant(Variant variant) {
            this.variant = variant;
            return this;
        }

        public Builder setVariant_id(Integer variant_id) {
            this.variant_id = variant_id;
            return this;
        }

        public Builder setForced(Boolean forced) {
            this.forced = forced;
            return this;
        }

        public Builder setId(Integer id) {
            this.id = id;
            return this;
        }

        public Builder setVersion(Integer version) {
            this.version = version;
            return this;
        }

        public Builder setCreatedDate(java.time.OffsetDateTime createdDate) {
            this.createdDate = createdDate;
            return this;
        }

        public Builder setUpdatedDate(java.time.OffsetDateTime updatedDate) {
            this.updatedDate = updatedDate;
            return this;
        }

        public Builder setCreatedBy(String createdBy) {
            this.createdBy = createdBy;
            return this;
        }

        public Builder setUpdatedBy(String updatedBy) {
            this.updatedBy = updatedBy;
            return this;
        }


        public InventoryItem build() {
            return new InventoryItem(onHandQty, availableQty, totalCost, resetDate, location, location_id, merchant, merchant_id, variant, variant_id, forced, id, version, createdDate, updatedDate, createdBy, updatedBy);
        }

    }
}
