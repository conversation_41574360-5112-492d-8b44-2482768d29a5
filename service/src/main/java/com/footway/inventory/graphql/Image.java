package com.footway.inventory.graphql;


@javax.annotation.processing.Generated(
    value = "com.kobylynskyi.graphql.codegen.GraphQLCodegen",
    date = "2024-05-24T09:49:27+0200"
)
public class Image implements java.io.Serializable {

    private static final long serialVersionUID = 1L;

    private String url;
    private Meta meta;
    private String type;
    private Boolean listImage;
    private AutomaticMeta automaticMeta;

    public Image() {
    }

    public Image(String url, Meta meta, String type, Boolean listImage, AutomaticMeta automaticMeta) {
        this.url = url;
        this.meta = meta;
        this.type = type;
        this.listImage = listImage;
        this.automaticMeta = automaticMeta;
    }

    public String getUrl() {
        return url;
    }
    public void setUrl(String url) {
        this.url = url;
    }

    public Meta getMeta() {
        return meta;
    }
    public void setMeta(Meta meta) {
        this.meta = meta;
    }

    public String getType() {
        return type;
    }
    public void setType(String type) {
        this.type = type;
    }

    public Boolean getListImage() {
        return listImage;
    }
    public void setListImage(Boolean listImage) {
        this.listImage = listImage;
    }

    public AutomaticMeta getAutomaticMeta() {
        return automaticMeta;
    }
    public void setAutomaticMeta(AutomaticMeta automaticMeta) {
        this.automaticMeta = automaticMeta;
    }



    public static Builder builder() {
        return new Builder();
    }

    @javax.annotation.processing.Generated(
        value = "com.kobylynskyi.graphql.codegen.GraphQLCodegen",
        date = "2024-05-24T09:49:27+0200"
    )
    public static class Builder {

        private String url;
        private Meta meta;
        private String type;
        private Boolean listImage;
        private AutomaticMeta automaticMeta;

        public Builder() {
        }

        public Builder setUrl(String url) {
            this.url = url;
            return this;
        }

        public Builder setMeta(Meta meta) {
            this.meta = meta;
            return this;
        }

        public Builder setType(String type) {
            this.type = type;
            return this;
        }

        public Builder setListImage(Boolean listImage) {
            this.listImage = listImage;
            return this;
        }

        public Builder setAutomaticMeta(AutomaticMeta automaticMeta) {
            this.automaticMeta = automaticMeta;
            return this;
        }


        public Image build() {
            return new Image(url, meta, type, listImage, automaticMeta);
        }

    }
}
