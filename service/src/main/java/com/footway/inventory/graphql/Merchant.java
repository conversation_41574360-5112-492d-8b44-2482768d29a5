package com.footway.inventory.graphql;


@javax.annotation.processing.Generated(
    value = "com.kobylynskyi.graphql.codegen.GraphQLCodegen",
    date = "2024-05-24T09:49:27+0200"
)
public class Merchant implements java.io.Serializable {

    private static final long serialVersionUID = 1L;

    private String name;
    private String code;
    private Boolean active;
    private Integer merchantId;
    private Integer version;
    private java.time.OffsetDateTime createdDate;
    private java.time.OffsetDateTime updatedDate;
    private String createdBy;
    private String updatedBy;

    public Merchant() {
    }

    public Merchant(String name, String code, Boolean active, Integer merchantId, Integer version, java.time.OffsetDateTime createdDate, java.time.OffsetDateTime updatedDate, String createdBy, String updatedBy) {
        this.name = name;
        this.code = code;
        this.active = active;
        this.merchantId = merchantId;
        this.version = version;
        this.createdDate = createdDate;
        this.updatedDate = updatedDate;
        this.createdBy = createdBy;
        this.updatedBy = updatedBy;
    }

    public String getName() {
        return name;
    }
    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }
    public void setCode(String code) {
        this.code = code;
    }

    public Boolean getActive() {
        return active;
    }
    public void setActive(Boolean active) {
        this.active = active;
    }

    public Integer getMerchantId() {
        return merchantId;
    }
    public void setMerchantId(Integer merchantId) {
        this.merchantId = merchantId;
    }

    public Integer getVersion() {
        return version;
    }
    public void setVersion(Integer version) {
        this.version = version;
    }

    public java.time.OffsetDateTime getCreatedDate() {
        return createdDate;
    }
    public void setCreatedDate(java.time.OffsetDateTime createdDate) {
        this.createdDate = createdDate;
    }

    public java.time.OffsetDateTime getUpdatedDate() {
        return updatedDate;
    }
    public void setUpdatedDate(java.time.OffsetDateTime updatedDate) {
        this.updatedDate = updatedDate;
    }

    public String getCreatedBy() {
        return createdBy;
    }
    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }
    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }



    public static Builder builder() {
        return new Builder();
    }

    @javax.annotation.processing.Generated(
        value = "com.kobylynskyi.graphql.codegen.GraphQLCodegen",
        date = "2024-05-24T09:49:27+0200"
    )
    public static class Builder {

        private String name;
        private String code;
        private Boolean active;
        private Integer merchantId;
        private Integer version;
        private java.time.OffsetDateTime createdDate;
        private java.time.OffsetDateTime updatedDate;
        private String createdBy;
        private String updatedBy;

        public Builder() {
        }

        public Builder setName(String name) {
            this.name = name;
            return this;
        }

        public Builder setCode(String code) {
            this.code = code;
            return this;
        }

        public Builder setActive(Boolean active) {
            this.active = active;
            return this;
        }

        public Builder setMerchantId(Integer merchantId) {
            this.merchantId = merchantId;
            return this;
        }

        public Builder setVersion(Integer version) {
            this.version = version;
            return this;
        }

        public Builder setCreatedDate(java.time.OffsetDateTime createdDate) {
            this.createdDate = createdDate;
            return this;
        }

        public Builder setUpdatedDate(java.time.OffsetDateTime updatedDate) {
            this.updatedDate = updatedDate;
            return this;
        }

        public Builder setCreatedBy(String createdBy) {
            this.createdBy = createdBy;
            return this;
        }

        public Builder setUpdatedBy(String updatedBy) {
            this.updatedBy = updatedBy;
            return this;
        }


        public Merchant build() {
            return new Merchant(name, code, active, merchantId, version, createdDate, updatedDate, createdBy, updatedBy);
        }

    }
}
