package com.footway.inventory.graphql;


@javax.annotation.processing.Generated(
    value = "com.kobylynskyi.graphql.codegen.GraphQLCodegen",
    date = "2024-05-24T09:49:27+0200"
)
public class Variant implements java.io.Serializable {

    private static final long serialVersionUID = 1L;

    private String variantNumber;
    private String attributes;
    private String ean;
    private String size;
    private String supplierSize;
    private String sizeEuLabel;
    private String sizeUkLabel;
    private String sizeUsLabel;
    private Integer weightGram;
    private Integer fwmsId;
    private Integer volumeMilliMeterCubed;
    private Integer longestDimensionMilliMeter;
    private Integer minInventoryLevel;
    private Integer maxInventoryLevel;
    private Integer fixedLotMultiplier;
    private Boolean dead;
    private Boolean snsSyncEnabled;
    private Boolean allowBackorder;
    private String sourceRef;
    private Integer onHandHbg;
    private Integer onHandKjula;
    private Boolean existsHbg;
    private Boolean existsKjula;
    private Product product;
    private java.util.List<String> barcodes;
    private Integer id;
    private Integer version;
    private java.time.OffsetDateTime createdDate;
    private java.time.OffsetDateTime updatedDate;
    private String createdBy;
    private String updatedBy;

    public Variant() {
    }

    public Variant(String variantNumber, String attributes, String ean, String size, String supplierSize, String sizeEuLabel, String sizeUkLabel, String sizeUsLabel, Integer weightGram, Integer fwmsId, Integer volumeMilliMeterCubed, Integer longestDimensionMilliMeter, Integer minInventoryLevel, Integer maxInventoryLevel, Integer fixedLotMultiplier, Boolean dead, Boolean snsSyncEnabled, Boolean allowBackorder, String sourceRef, Integer onHandHbg, Integer onHandKjula, Boolean existsHbg, Boolean existsKjula, Product product, java.util.List<String> barcodes, Integer id, Integer version, java.time.OffsetDateTime createdDate, java.time.OffsetDateTime updatedDate, String createdBy, String updatedBy) {
        this.variantNumber = variantNumber;
        this.attributes = attributes;
        this.ean = ean;
        this.size = size;
        this.supplierSize = supplierSize;
        this.sizeEuLabel = sizeEuLabel;
        this.sizeUkLabel = sizeUkLabel;
        this.sizeUsLabel = sizeUsLabel;
        this.weightGram = weightGram;
        this.fwmsId = fwmsId;
        this.volumeMilliMeterCubed = volumeMilliMeterCubed;
        this.longestDimensionMilliMeter = longestDimensionMilliMeter;
        this.minInventoryLevel = minInventoryLevel;
        this.maxInventoryLevel = maxInventoryLevel;
        this.fixedLotMultiplier = fixedLotMultiplier;
        this.dead = dead;
        this.snsSyncEnabled = snsSyncEnabled;
        this.allowBackorder = allowBackorder;
        this.sourceRef = sourceRef;
        this.onHandHbg = onHandHbg;
        this.onHandKjula = onHandKjula;
        this.existsHbg = existsHbg;
        this.existsKjula = existsKjula;
        this.product = product;
        this.barcodes = barcodes;
        this.id = id;
        this.version = version;
        this.createdDate = createdDate;
        this.updatedDate = updatedDate;
        this.createdBy = createdBy;
        this.updatedBy = updatedBy;
    }

    public String getVariantNumber() {
        return variantNumber;
    }
    public void setVariantNumber(String variantNumber) {
        this.variantNumber = variantNumber;
    }

    public String getAttributes() {
        return attributes;
    }
    public void setAttributes(String attributes) {
        this.attributes = attributes;
    }

    public String getEan() {
        return ean;
    }
    public void setEan(String ean) {
        this.ean = ean;
    }

    public String getSize() {
        return size;
    }
    public void setSize(String size) {
        this.size = size;
    }

    public String getSupplierSize() {
        return supplierSize;
    }
    public void setSupplierSize(String supplierSize) {
        this.supplierSize = supplierSize;
    }

    public String getSizeEuLabel() {
        return sizeEuLabel;
    }
    public void setSizeEuLabel(String sizeEuLabel) {
        this.sizeEuLabel = sizeEuLabel;
    }

    public String getSizeUkLabel() {
        return sizeUkLabel;
    }
    public void setSizeUkLabel(String sizeUkLabel) {
        this.sizeUkLabel = sizeUkLabel;
    }

    public String getSizeUsLabel() {
        return sizeUsLabel;
    }
    public void setSizeUsLabel(String sizeUsLabel) {
        this.sizeUsLabel = sizeUsLabel;
    }

    public Integer getWeightGram() {
        return weightGram;
    }
    public void setWeightGram(Integer weightGram) {
        this.weightGram = weightGram;
    }

    public Integer getFwmsId() {
        return fwmsId;
    }
    public void setFwmsId(Integer fwmsId) {
        this.fwmsId = fwmsId;
    }

    public Integer getVolumeMilliMeterCubed() {
        return volumeMilliMeterCubed;
    }
    public void setVolumeMilliMeterCubed(Integer volumeMilliMeterCubed) {
        this.volumeMilliMeterCubed = volumeMilliMeterCubed;
    }

    public Integer getLongestDimensionMilliMeter() {
        return longestDimensionMilliMeter;
    }
    public void setLongestDimensionMilliMeter(Integer longestDimensionMilliMeter) {
        this.longestDimensionMilliMeter = longestDimensionMilliMeter;
    }

    public Integer getMinInventoryLevel() {
        return minInventoryLevel;
    }
    public void setMinInventoryLevel(Integer minInventoryLevel) {
        this.minInventoryLevel = minInventoryLevel;
    }

    public Integer getMaxInventoryLevel() {
        return maxInventoryLevel;
    }
    public void setMaxInventoryLevel(Integer maxInventoryLevel) {
        this.maxInventoryLevel = maxInventoryLevel;
    }

    public Integer getFixedLotMultiplier() {
        return fixedLotMultiplier;
    }
    public void setFixedLotMultiplier(Integer fixedLotMultiplier) {
        this.fixedLotMultiplier = fixedLotMultiplier;
    }

    public Boolean getDead() {
        return dead;
    }
    public void setDead(Boolean dead) {
        this.dead = dead;
    }

    public Boolean getSnsSyncEnabled() {
        return snsSyncEnabled;
    }
    public void setSnsSyncEnabled(Boolean snsSyncEnabled) {
        this.snsSyncEnabled = snsSyncEnabled;
    }

    public Boolean getAllowBackorder() {
        return allowBackorder;
    }
    public void setAllowBackorder(Boolean allowBackorder) {
        this.allowBackorder = allowBackorder;
    }

    public String getSourceRef() {
        return sourceRef;
    }
    public void setSourceRef(String sourceRef) {
        this.sourceRef = sourceRef;
    }

    public Integer getOnHandHbg() {
        return onHandHbg;
    }
    public void setOnHandHbg(Integer onHandHbg) {
        this.onHandHbg = onHandHbg;
    }

    public Integer getOnHandKjula() {
        return onHandKjula;
    }
    public void setOnHandKjula(Integer onHandKjula) {
        this.onHandKjula = onHandKjula;
    }

    public Boolean getExistsHbg() {
        return existsHbg;
    }
    public void setExistsHbg(Boolean existsHbg) {
        this.existsHbg = existsHbg;
    }

    public Boolean getExistsKjula() {
        return existsKjula;
    }
    public void setExistsKjula(Boolean existsKjula) {
        this.existsKjula = existsKjula;
    }

    public Product getProduct() {
        return product;
    }
    public void setProduct(Product product) {
        this.product = product;
    }

    public java.util.List<String> getBarcodes() {
        return barcodes;
    }
    public void setBarcodes(java.util.List<String> barcodes) {
        this.barcodes = barcodes;
    }

    public Integer getId() {
        return id;
    }
    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getVersion() {
        return version;
    }
    public void setVersion(Integer version) {
        this.version = version;
    }

    public java.time.OffsetDateTime getCreatedDate() {
        return createdDate;
    }
    public void setCreatedDate(java.time.OffsetDateTime createdDate) {
        this.createdDate = createdDate;
    }

    public java.time.OffsetDateTime getUpdatedDate() {
        return updatedDate;
    }
    public void setUpdatedDate(java.time.OffsetDateTime updatedDate) {
        this.updatedDate = updatedDate;
    }

    public String getCreatedBy() {
        return createdBy;
    }
    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }
    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }



    public static Builder builder() {
        return new Builder();
    }

    @javax.annotation.processing.Generated(
        value = "com.kobylynskyi.graphql.codegen.GraphQLCodegen",
        date = "2024-05-24T09:49:27+0200"
    )
    public static class Builder {

        private String variantNumber;
        private String attributes;
        private String ean;
        private String size;
        private String supplierSize;
        private String sizeEuLabel;
        private String sizeUkLabel;
        private String sizeUsLabel;
        private Integer weightGram;
        private Integer fwmsId;
        private Integer volumeMilliMeterCubed;
        private Integer longestDimensionMilliMeter;
        private Integer minInventoryLevel;
        private Integer maxInventoryLevel;
        private Integer fixedLotMultiplier;
        private Boolean dead;
        private Boolean snsSyncEnabled;
        private Boolean allowBackorder;
        private String sourceRef;
        private Integer onHandHbg;
        private Integer onHandKjula;
        private Boolean existsHbg;
        private Boolean existsKjula;
        private Product product;
        private java.util.List<String> barcodes;
        private Integer id;
        private Integer version;
        private java.time.OffsetDateTime createdDate;
        private java.time.OffsetDateTime updatedDate;
        private String createdBy;
        private String updatedBy;

        public Builder() {
        }

        public Builder setVariantNumber(String variantNumber) {
            this.variantNumber = variantNumber;
            return this;
        }

        public Builder setAttributes(String attributes) {
            this.attributes = attributes;
            return this;
        }

        public Builder setEan(String ean) {
            this.ean = ean;
            return this;
        }

        public Builder setSize(String size) {
            this.size = size;
            return this;
        }

        public Builder setSupplierSize(String supplierSize) {
            this.supplierSize = supplierSize;
            return this;
        }

        public Builder setSizeEuLabel(String sizeEuLabel) {
            this.sizeEuLabel = sizeEuLabel;
            return this;
        }

        public Builder setSizeUkLabel(String sizeUkLabel) {
            this.sizeUkLabel = sizeUkLabel;
            return this;
        }

        public Builder setSizeUsLabel(String sizeUsLabel) {
            this.sizeUsLabel = sizeUsLabel;
            return this;
        }

        public Builder setWeightGram(Integer weightGram) {
            this.weightGram = weightGram;
            return this;
        }

        public Builder setFwmsId(Integer fwmsId) {
            this.fwmsId = fwmsId;
            return this;
        }

        public Builder setVolumeMilliMeterCubed(Integer volumeMilliMeterCubed) {
            this.volumeMilliMeterCubed = volumeMilliMeterCubed;
            return this;
        }

        public Builder setLongestDimensionMilliMeter(Integer longestDimensionMilliMeter) {
            this.longestDimensionMilliMeter = longestDimensionMilliMeter;
            return this;
        }

        public Builder setMinInventoryLevel(Integer minInventoryLevel) {
            this.minInventoryLevel = minInventoryLevel;
            return this;
        }

        public Builder setMaxInventoryLevel(Integer maxInventoryLevel) {
            this.maxInventoryLevel = maxInventoryLevel;
            return this;
        }

        public Builder setFixedLotMultiplier(Integer fixedLotMultiplier) {
            this.fixedLotMultiplier = fixedLotMultiplier;
            return this;
        }

        public Builder setDead(Boolean dead) {
            this.dead = dead;
            return this;
        }

        public Builder setSnsSyncEnabled(Boolean snsSyncEnabled) {
            this.snsSyncEnabled = snsSyncEnabled;
            return this;
        }

        public Builder setAllowBackorder(Boolean allowBackorder) {
            this.allowBackorder = allowBackorder;
            return this;
        }

        public Builder setSourceRef(String sourceRef) {
            this.sourceRef = sourceRef;
            return this;
        }

        public Builder setOnHandHbg(Integer onHandHbg) {
            this.onHandHbg = onHandHbg;
            return this;
        }

        public Builder setOnHandKjula(Integer onHandKjula) {
            this.onHandKjula = onHandKjula;
            return this;
        }

        public Builder setExistsHbg(Boolean existsHbg) {
            this.existsHbg = existsHbg;
            return this;
        }

        public Builder setExistsKjula(Boolean existsKjula) {
            this.existsKjula = existsKjula;
            return this;
        }

        public Builder setProduct(Product product) {
            this.product = product;
            return this;
        }

        public Builder setBarcodes(java.util.List<String> barcodes) {
            this.barcodes = barcodes;
            return this;
        }

        public Builder setId(Integer id) {
            this.id = id;
            return this;
        }

        public Builder setVersion(Integer version) {
            this.version = version;
            return this;
        }

        public Builder setCreatedDate(java.time.OffsetDateTime createdDate) {
            this.createdDate = createdDate;
            return this;
        }

        public Builder setUpdatedDate(java.time.OffsetDateTime updatedDate) {
            this.updatedDate = updatedDate;
            return this;
        }

        public Builder setCreatedBy(String createdBy) {
            this.createdBy = createdBy;
            return this;
        }

        public Builder setUpdatedBy(String updatedBy) {
            this.updatedBy = updatedBy;
            return this;
        }

        public Variant build() {
            return new Variant(variantNumber, attributes, ean, size, supplierSize, sizeEuLabel, sizeUkLabel, sizeUsLabel, weightGram, fwmsId, volumeMilliMeterCubed, longestDimensionMilliMeter, minInventoryLevel, maxInventoryLevel, fixedLotMultiplier, dead, snsSyncEnabled, allowBackorder, sourceRef, onHandHbg, onHandKjula, existsHbg, existsKjula, product, barcodes, id, version, createdDate, updatedDate, createdBy, updatedBy);
        }

    }
}
