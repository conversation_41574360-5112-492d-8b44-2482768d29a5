package com.footway.inventory.graphql;


@javax.annotation.processing.Generated(
    value = "com.kobylynskyi.graphql.codegen.GraphQLCodegen",
    date = "2024-05-24T09:49:27+0200"
)
public class Attributes implements java.io.Serializable {

    private static final long serialVersionUID = 1L;

    private Boolean dead;
    private String brand;
    private java.util.List<KeyValueInt> price;
    private java.util.List<String> store;
    private java.util.List<KeyValueBoolean> active;
    private java.util.List<Image> images;
    private String season;
    private String urlKey;
    private Boolean visible;
    private java.time.OffsetDateTime newsDate;
    private java.util.List<KeyValueFloat> popularity;
    private java.util.List<GlobalText> globalTexts;
    private String intrastatNo;
    private java.util.List<KeyValueString> priceReason;
    private String productName;
    private String productOwner;
    private String purchaseType;
    private java.util.List<KeyValueInt> specialPrice;
    private java.util.List<Tag> manualTagCloud;
    private String countryOfOrigin;
    private Double purchasePriceSek;
    private java.util.List<Tag> automaticTagCloud;
    private ProductPerformance productPerformance;
    private String supplierColorName;
    private String supplierModelName;
    private java.util.List<KeyValueString> specialPriceReason;
    private java.time.OffsetDateTime lastInboundDelivery;
    private String supplierModelNumber;
    private java.time.OffsetDateTime firstInboundDelivery;
    private java.util.List<KeyValueInt> recommendedRetailPrice;
    private java.util.List<KeyValueString> recommendedRetailPriceReason;

    public Attributes() {
    }

    public Attributes(Boolean dead, String brand, java.util.List<KeyValueInt> price, java.util.List<String> store, java.util.List<KeyValueBoolean> active, java.util.List<Image> images, String season, String urlKey, Boolean visible, java.time.OffsetDateTime newsDate, java.util.List<KeyValueFloat> popularity, java.util.List<GlobalText> globalTexts, String intrastatNo, java.util.List<KeyValueString> priceReason, String productName, String productOwner, String purchaseType, java.util.List<KeyValueInt> specialPrice, java.util.List<Tag> manualTagCloud, String countryOfOrigin, Double purchasePriceSek, java.util.List<Tag> automaticTagCloud, ProductPerformance productPerformance, String supplierColorName, String supplierModelName, java.util.List<KeyValueString> specialPriceReason, java.time.OffsetDateTime lastInboundDelivery, String supplierModelNumber, java.time.OffsetDateTime firstInboundDelivery, java.util.List<KeyValueInt> recommendedRetailPrice, java.util.List<KeyValueString> recommendedRetailPriceReason) {
        this.dead = dead;
        this.brand = brand;
        this.price = price;
        this.store = store;
        this.active = active;
        this.images = images;
        this.season = season;
        this.urlKey = urlKey;
        this.visible = visible;
        this.newsDate = newsDate;
        this.popularity = popularity;
        this.globalTexts = globalTexts;
        this.intrastatNo = intrastatNo;
        this.priceReason = priceReason;
        this.productName = productName;
        this.productOwner = productOwner;
        this.purchaseType = purchaseType;
        this.specialPrice = specialPrice;
        this.manualTagCloud = manualTagCloud;
        this.countryOfOrigin = countryOfOrigin;
        this.purchasePriceSek = purchasePriceSek;
        this.automaticTagCloud = automaticTagCloud;
        this.productPerformance = productPerformance;
        this.supplierColorName = supplierColorName;
        this.supplierModelName = supplierModelName;
        this.specialPriceReason = specialPriceReason;
        this.lastInboundDelivery = lastInboundDelivery;
        this.supplierModelNumber = supplierModelNumber;
        this.firstInboundDelivery = firstInboundDelivery;
        this.recommendedRetailPrice = recommendedRetailPrice;
        this.recommendedRetailPriceReason = recommendedRetailPriceReason;
    }

    public Boolean getDead() {
        return dead;
    }
    public void setDead(Boolean dead) {
        this.dead = dead;
    }

    public String getBrand() {
        return brand;
    }
    public void setBrand(String brand) {
        this.brand = brand;
    }

    public java.util.List<KeyValueInt> getPrice() {
        return price;
    }
    public void setPrice(java.util.List<KeyValueInt> price) {
        this.price = price;
    }

    public java.util.List<String> getStore() {
        return store;
    }
    public void setStore(java.util.List<String> store) {
        this.store = store;
    }

    public java.util.List<KeyValueBoolean> getActive() {
        return active;
    }
    public void setActive(java.util.List<KeyValueBoolean> active) {
        this.active = active;
    }

    public java.util.List<Image> getImages() {
        return images;
    }
    public void setImages(java.util.List<Image> images) {
        this.images = images;
    }

    public String getSeason() {
        return season;
    }
    public void setSeason(String season) {
        this.season = season;
    }

    public String getUrlKey() {
        return urlKey;
    }
    public void setUrlKey(String urlKey) {
        this.urlKey = urlKey;
    }

    public Boolean getVisible() {
        return visible;
    }
    public void setVisible(Boolean visible) {
        this.visible = visible;
    }

    public java.time.OffsetDateTime getNewsDate() {
        return newsDate;
    }
    public void setNewsDate(java.time.OffsetDateTime newsDate) {
        this.newsDate = newsDate;
    }

    public java.util.List<KeyValueFloat> getPopularity() {
        return popularity;
    }
    public void setPopularity(java.util.List<KeyValueFloat> popularity) {
        this.popularity = popularity;
    }

    public java.util.List<GlobalText> getGlobalTexts() {
        return globalTexts;
    }
    public void setGlobalTexts(java.util.List<GlobalText> globalTexts) {
        this.globalTexts = globalTexts;
    }

    public String getIntrastatNo() {
        return intrastatNo;
    }
    public void setIntrastatNo(String intrastatNo) {
        this.intrastatNo = intrastatNo;
    }

    public java.util.List<KeyValueString> getPriceReason() {
        return priceReason;
    }
    public void setPriceReason(java.util.List<KeyValueString> priceReason) {
        this.priceReason = priceReason;
    }

    public String getProductName() {
        return productName;
    }
    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getProductOwner() {
        return productOwner;
    }
    public void setProductOwner(String productOwner) {
        this.productOwner = productOwner;
    }

    public String getPurchaseType() {
        return purchaseType;
    }
    public void setPurchaseType(String purchaseType) {
        this.purchaseType = purchaseType;
    }

    public java.util.List<KeyValueInt> getSpecialPrice() {
        return specialPrice;
    }
    public void setSpecialPrice(java.util.List<KeyValueInt> specialPrice) {
        this.specialPrice = specialPrice;
    }

    public java.util.List<Tag> getManualTagCloud() {
        return manualTagCloud;
    }
    public void setManualTagCloud(java.util.List<Tag> manualTagCloud) {
        this.manualTagCloud = manualTagCloud;
    }

    public String getCountryOfOrigin() {
        return countryOfOrigin;
    }
    public void setCountryOfOrigin(String countryOfOrigin) {
        this.countryOfOrigin = countryOfOrigin;
    }

    public Double getPurchasePriceSek() {
        return purchasePriceSek;
    }
    public void setPurchasePriceSek(Double purchasePriceSek) {
        this.purchasePriceSek = purchasePriceSek;
    }

    public java.util.List<Tag> getAutomaticTagCloud() {
        return automaticTagCloud;
    }
    public void setAutomaticTagCloud(java.util.List<Tag> automaticTagCloud) {
        this.automaticTagCloud = automaticTagCloud;
    }

    public ProductPerformance getProductPerformance() {
        return productPerformance;
    }
    public void setProductPerformance(ProductPerformance productPerformance) {
        this.productPerformance = productPerformance;
    }

    public String getSupplierColorName() {
        return supplierColorName;
    }
    public void setSupplierColorName(String supplierColorName) {
        this.supplierColorName = supplierColorName;
    }

    public String getSupplierModelName() {
        return supplierModelName;
    }
    public void setSupplierModelName(String supplierModelName) {
        this.supplierModelName = supplierModelName;
    }

    public java.util.List<KeyValueString> getSpecialPriceReason() {
        return specialPriceReason;
    }
    public void setSpecialPriceReason(java.util.List<KeyValueString> specialPriceReason) {
        this.specialPriceReason = specialPriceReason;
    }

    public java.time.OffsetDateTime getLastInboundDelivery() {
        return lastInboundDelivery;
    }
    public void setLastInboundDelivery(java.time.OffsetDateTime lastInboundDelivery) {
        this.lastInboundDelivery = lastInboundDelivery;
    }

    public String getSupplierModelNumber() {
        return supplierModelNumber;
    }
    public void setSupplierModelNumber(String supplierModelNumber) {
        this.supplierModelNumber = supplierModelNumber;
    }

    public java.time.OffsetDateTime getFirstInboundDelivery() {
        return firstInboundDelivery;
    }
    public void setFirstInboundDelivery(java.time.OffsetDateTime firstInboundDelivery) {
        this.firstInboundDelivery = firstInboundDelivery;
    }

    public java.util.List<KeyValueInt> getRecommendedRetailPrice() {
        return recommendedRetailPrice;
    }
    public void setRecommendedRetailPrice(java.util.List<KeyValueInt> recommendedRetailPrice) {
        this.recommendedRetailPrice = recommendedRetailPrice;
    }

    public java.util.List<KeyValueString> getRecommendedRetailPriceReason() {
        return recommendedRetailPriceReason;
    }
    public void setRecommendedRetailPriceReason(java.util.List<KeyValueString> recommendedRetailPriceReason) {
        this.recommendedRetailPriceReason = recommendedRetailPriceReason;
    }



    public static Builder builder() {
        return new Builder();
    }

    @javax.annotation.processing.Generated(
        value = "com.kobylynskyi.graphql.codegen.GraphQLCodegen",
        date = "2024-05-24T09:49:27+0200"
    )
    public static class Builder {

        private Boolean dead;
        private String brand;
        private java.util.List<KeyValueInt> price;
        private java.util.List<String> store;
        private java.util.List<KeyValueBoolean> active;
        private java.util.List<Image> images;
        private String season;
        private String urlKey;
        private Boolean visible;
        private java.time.OffsetDateTime newsDate;
        private java.util.List<KeyValueFloat> popularity;
        private java.util.List<GlobalText> globalTexts;
        private String intrastatNo;
        private java.util.List<KeyValueString> priceReason;
        private String productName;
        private String productOwner;
        private String purchaseType;
        private java.util.List<KeyValueInt> specialPrice;
        private java.util.List<Tag> manualTagCloud;
        private String countryOfOrigin;
        private Double purchasePriceSek;
        private java.util.List<Tag> automaticTagCloud;
        private ProductPerformance productPerformance;
        private String supplierColorName;
        private String supplierModelName;
        private java.util.List<KeyValueString> specialPriceReason;
        private java.time.OffsetDateTime lastInboundDelivery;
        private String supplierModelNumber;
        private java.time.OffsetDateTime firstInboundDelivery;
        private java.util.List<KeyValueInt> recommendedRetailPrice;
        private java.util.List<KeyValueString> recommendedRetailPriceReason;

        public Builder() {
        }

        public Builder setDead(Boolean dead) {
            this.dead = dead;
            return this;
        }

        public Builder setBrand(String brand) {
            this.brand = brand;
            return this;
        }

        public Builder setPrice(java.util.List<KeyValueInt> price) {
            this.price = price;
            return this;
        }

        public Builder setStore(java.util.List<String> store) {
            this.store = store;
            return this;
        }

        public Builder setActive(java.util.List<KeyValueBoolean> active) {
            this.active = active;
            return this;
        }

        public Builder setImages(java.util.List<Image> images) {
            this.images = images;
            return this;
        }

        public Builder setSeason(String season) {
            this.season = season;
            return this;
        }

        public Builder setUrlKey(String urlKey) {
            this.urlKey = urlKey;
            return this;
        }

        public Builder setVisible(Boolean visible) {
            this.visible = visible;
            return this;
        }

        public Builder setNewsDate(java.time.OffsetDateTime newsDate) {
            this.newsDate = newsDate;
            return this;
        }

        public Builder setPopularity(java.util.List<KeyValueFloat> popularity) {
            this.popularity = popularity;
            return this;
        }

        public Builder setGlobalTexts(java.util.List<GlobalText> globalTexts) {
            this.globalTexts = globalTexts;
            return this;
        }

        public Builder setIntrastatNo(String intrastatNo) {
            this.intrastatNo = intrastatNo;
            return this;
        }

        public Builder setPriceReason(java.util.List<KeyValueString> priceReason) {
            this.priceReason = priceReason;
            return this;
        }

        public Builder setProductName(String productName) {
            this.productName = productName;
            return this;
        }

        public Builder setProductOwner(String productOwner) {
            this.productOwner = productOwner;
            return this;
        }

        public Builder setPurchaseType(String purchaseType) {
            this.purchaseType = purchaseType;
            return this;
        }

        public Builder setSpecialPrice(java.util.List<KeyValueInt> specialPrice) {
            this.specialPrice = specialPrice;
            return this;
        }

        public Builder setManualTagCloud(java.util.List<Tag> manualTagCloud) {
            this.manualTagCloud = manualTagCloud;
            return this;
        }

        public Builder setCountryOfOrigin(String countryOfOrigin) {
            this.countryOfOrigin = countryOfOrigin;
            return this;
        }

        public Builder setPurchasePriceSek(Double purchasePriceSek) {
            this.purchasePriceSek = purchasePriceSek;
            return this;
        }

        public Builder setAutomaticTagCloud(java.util.List<Tag> automaticTagCloud) {
            this.automaticTagCloud = automaticTagCloud;
            return this;
        }

        public Builder setProductPerformance(ProductPerformance productPerformance) {
            this.productPerformance = productPerformance;
            return this;
        }

        public Builder setSupplierColorName(String supplierColorName) {
            this.supplierColorName = supplierColorName;
            return this;
        }

        public Builder setSupplierModelName(String supplierModelName) {
            this.supplierModelName = supplierModelName;
            return this;
        }

        public Builder setSpecialPriceReason(java.util.List<KeyValueString> specialPriceReason) {
            this.specialPriceReason = specialPriceReason;
            return this;
        }

        public Builder setLastInboundDelivery(java.time.OffsetDateTime lastInboundDelivery) {
            this.lastInboundDelivery = lastInboundDelivery;
            return this;
        }

        public Builder setSupplierModelNumber(String supplierModelNumber) {
            this.supplierModelNumber = supplierModelNumber;
            return this;
        }

        public Builder setFirstInboundDelivery(java.time.OffsetDateTime firstInboundDelivery) {
            this.firstInboundDelivery = firstInboundDelivery;
            return this;
        }

        public Builder setRecommendedRetailPrice(java.util.List<KeyValueInt> recommendedRetailPrice) {
            this.recommendedRetailPrice = recommendedRetailPrice;
            return this;
        }

        public Builder setRecommendedRetailPriceReason(java.util.List<KeyValueString> recommendedRetailPriceReason) {
            this.recommendedRetailPriceReason = recommendedRetailPriceReason;
            return this;
        }


        public Attributes build() {
            return new Attributes(dead, brand, price, store, active, images, season, urlKey, visible, newsDate, popularity, globalTexts, intrastatNo, priceReason, productName, productOwner, purchaseType, specialPrice, manualTagCloud, countryOfOrigin, purchasePriceSek, automaticTagCloud, productPerformance, supplierColorName, supplierModelName, specialPriceReason, lastInboundDelivery, supplierModelNumber, firstInboundDelivery, recommendedRetailPrice, recommendedRetailPriceReason);
        }

    }
}
