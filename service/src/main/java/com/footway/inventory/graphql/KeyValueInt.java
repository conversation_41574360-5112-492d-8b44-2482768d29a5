package com.footway.inventory.graphql;


@javax.annotation.processing.Generated(
    value = "com.kobylynskyi.graphql.codegen.GraphQLCodegen",
    date = "2024-05-24T09:49:27+0200"
)
public class KeyValueInt implements java.io.Serializable {

    private static final long serialVersionUID = 1L;

    private String key;
    private Integer value;

    public KeyValueInt() {
    }

    public KeyValueInt(String key, Integer value) {
        this.key = key;
        this.value = value;
    }

    public String getKey() {
        return key;
    }
    public void setKey(String key) {
        this.key = key;
    }

    public Integer getValue() {
        return value;
    }
    public void setValue(Integer value) {
        this.value = value;
    }



    public static Builder builder() {
        return new Builder();
    }

    @javax.annotation.processing.Generated(
        value = "com.kobylynskyi.graphql.codegen.GraphQLCodegen",
        date = "2024-05-24T09:49:27+0200"
    )
    public static class Builder {

        private String key;
        private Integer value;

        public Builder() {
        }

        public Builder setKey(String key) {
            this.key = key;
            return this;
        }

        public Builder setValue(Integer value) {
            this.value = value;
            return this;
        }


        public KeyValueInt build() {
            return new KeyValueInt(key, value);
        }

    }
}
