package com.footway.inventory.graphql;


@javax.annotation.processing.Generated(
    value = "com.kobylynskyi.graphql.codegen.GraphQLCodegen",
    date = "2024-05-24T09:49:27+0200"
)
public class ProductPerformance implements java.io.Serializable {

    private static final long serialVersionUID = 1L;

    private Double SPE14;
    private Double SPE60;
    private Integer maxSkuQty;
    private Integer minSkuQty;
    private Integer sumSkuQty;
    private Integer skuSizeCount;
    private Double sumReturnQty14;
    private Double sumReturnQty60;
    private Double sumGrossAmt14;
    private Double sumGrossAmt60;
    private Double sumProdCost14;
    private Double sumProdCost60;
    private Double sumSalesAmt14;
    private Double sumSalesAmt60;
    private Integer sumSkuinstock;
    private Double avgSkuPoprice;
    private java.util.Date startOfSalesDay;
    private Double sumReturnQty365;
    private Double sumGrossAmt365;
    private Double sumProdCost365;
    private Double sumSalesAmt365;
    private Integer sumDaysInStock14;
    private Integer sumDaysInStock60;
    private Double sizeCoverageProc;
    private Integer sumDelivSalesQty14;
    private Integer sumDelivSalesQty60;
    private Double geometricAvgPoprice;
    private Integer sumDelivSalesQty365;
    private Double sumSalesAmtPreCartDisc14;
    private Double sumSalesAmtPreCartDisc60;
    private Double procReturnsOnOrder30Momentan;

    public ProductPerformance() {
    }

    public ProductPerformance(Double SPE14, Double SPE60, Integer maxSkuQty, Integer minSkuQty, Integer sumSkuQty, Integer skuSizeCount, Double sumReturnQty14, Double sumReturnQty60, Double sumGrossAmt14, Double sumGrossAmt60, Double sumProdCost14, Double sumProdCost60, Double sumSalesAmt14, Double sumSalesAmt60, Integer sumSkuinstock, Double avgSkuPoprice, java.util.Date startOfSalesDay, Double sumReturnQty365, Double sumGrossAmt365, Double sumProdCost365, Double sumSalesAmt365, Integer sumDaysInStock14, Integer sumDaysInStock60, Double sizeCoverageProc, Integer sumDelivSalesQty14, Integer sumDelivSalesQty60, Double geometricAvgPoprice, Integer sumDelivSalesQty365, Double sumSalesAmtPreCartDisc14, Double sumSalesAmtPreCartDisc60, Double procReturnsOnOrder30Momentan) {
        this.SPE14 = SPE14;
        this.SPE60 = SPE60;
        this.maxSkuQty = maxSkuQty;
        this.minSkuQty = minSkuQty;
        this.sumSkuQty = sumSkuQty;
        this.skuSizeCount = skuSizeCount;
        this.sumReturnQty14 = sumReturnQty14;
        this.sumReturnQty60 = sumReturnQty60;
        this.sumGrossAmt14 = sumGrossAmt14;
        this.sumGrossAmt60 = sumGrossAmt60;
        this.sumProdCost14 = sumProdCost14;
        this.sumProdCost60 = sumProdCost60;
        this.sumSalesAmt14 = sumSalesAmt14;
        this.sumSalesAmt60 = sumSalesAmt60;
        this.sumSkuinstock = sumSkuinstock;
        this.avgSkuPoprice = avgSkuPoprice;
        this.startOfSalesDay = startOfSalesDay;
        this.sumReturnQty365 = sumReturnQty365;
        this.sumGrossAmt365 = sumGrossAmt365;
        this.sumProdCost365 = sumProdCost365;
        this.sumSalesAmt365 = sumSalesAmt365;
        this.sumDaysInStock14 = sumDaysInStock14;
        this.sumDaysInStock60 = sumDaysInStock60;
        this.sizeCoverageProc = sizeCoverageProc;
        this.sumDelivSalesQty14 = sumDelivSalesQty14;
        this.sumDelivSalesQty60 = sumDelivSalesQty60;
        this.geometricAvgPoprice = geometricAvgPoprice;
        this.sumDelivSalesQty365 = sumDelivSalesQty365;
        this.sumSalesAmtPreCartDisc14 = sumSalesAmtPreCartDisc14;
        this.sumSalesAmtPreCartDisc60 = sumSalesAmtPreCartDisc60;
        this.procReturnsOnOrder30Momentan = procReturnsOnOrder30Momentan;
    }

    public Double getSPE14() {
        return SPE14;
    }
    public void setSPE14(Double SPE14) {
        this.SPE14 = SPE14;
    }

    public Double getSPE60() {
        return SPE60;
    }
    public void setSPE60(Double SPE60) {
        this.SPE60 = SPE60;
    }

    public Integer getMaxSkuQty() {
        return maxSkuQty;
    }
    public void setMaxSkuQty(Integer maxSkuQty) {
        this.maxSkuQty = maxSkuQty;
    }

    public Integer getMinSkuQty() {
        return minSkuQty;
    }
    public void setMinSkuQty(Integer minSkuQty) {
        this.minSkuQty = minSkuQty;
    }

    public Integer getSumSkuQty() {
        return sumSkuQty;
    }
    public void setSumSkuQty(Integer sumSkuQty) {
        this.sumSkuQty = sumSkuQty;
    }

    public Integer getSkuSizeCount() {
        return skuSizeCount;
    }
    public void setSkuSizeCount(Integer skuSizeCount) {
        this.skuSizeCount = skuSizeCount;
    }

    public Double getSumReturnQty14() {
        return sumReturnQty14;
    }
    public void setSumReturnQty14(Double sumReturnQty14) {
        this.sumReturnQty14 = sumReturnQty14;
    }

    public Double getSumReturnQty60() {
        return sumReturnQty60;
    }
    public void setSumReturnQty60(Double sumReturnQty60) {
        this.sumReturnQty60 = sumReturnQty60;
    }

    public Double getSumGrossAmt14() {
        return sumGrossAmt14;
    }
    public void setSumGrossAmt14(Double sumGrossAmt14) {
        this.sumGrossAmt14 = sumGrossAmt14;
    }

    public Double getSumGrossAmt60() {
        return sumGrossAmt60;
    }
    public void setSumGrossAmt60(Double sumGrossAmt60) {
        this.sumGrossAmt60 = sumGrossAmt60;
    }

    public Double getSumProdCost14() {
        return sumProdCost14;
    }
    public void setSumProdCost14(Double sumProdCost14) {
        this.sumProdCost14 = sumProdCost14;
    }

    public Double getSumProdCost60() {
        return sumProdCost60;
    }
    public void setSumProdCost60(Double sumProdCost60) {
        this.sumProdCost60 = sumProdCost60;
    }

    public Double getSumSalesAmt14() {
        return sumSalesAmt14;
    }
    public void setSumSalesAmt14(Double sumSalesAmt14) {
        this.sumSalesAmt14 = sumSalesAmt14;
    }

    public Double getSumSalesAmt60() {
        return sumSalesAmt60;
    }
    public void setSumSalesAmt60(Double sumSalesAmt60) {
        this.sumSalesAmt60 = sumSalesAmt60;
    }

    public Integer getSumSkuinstock() {
        return sumSkuinstock;
    }
    public void setSumSkuinstock(Integer sumSkuinstock) {
        this.sumSkuinstock = sumSkuinstock;
    }

    public Double getAvgSkuPoprice() {
        return avgSkuPoprice;
    }
    public void setAvgSkuPoprice(Double avgSkuPoprice) {
        this.avgSkuPoprice = avgSkuPoprice;
    }

    public java.util.Date getStartOfSalesDay() {
        return startOfSalesDay;
    }
    public void setStartOfSalesDay(java.util.Date startOfSalesDay) {
        this.startOfSalesDay = startOfSalesDay;
    }

    public Double getSumReturnQty365() {
        return sumReturnQty365;
    }
    public void setSumReturnQty365(Double sumReturnQty365) {
        this.sumReturnQty365 = sumReturnQty365;
    }

    public Double getSumGrossAmt365() {
        return sumGrossAmt365;
    }
    public void setSumGrossAmt365(Double sumGrossAmt365) {
        this.sumGrossAmt365 = sumGrossAmt365;
    }

    public Double getSumProdCost365() {
        return sumProdCost365;
    }
    public void setSumProdCost365(Double sumProdCost365) {
        this.sumProdCost365 = sumProdCost365;
    }

    public Double getSumSalesAmt365() {
        return sumSalesAmt365;
    }
    public void setSumSalesAmt365(Double sumSalesAmt365) {
        this.sumSalesAmt365 = sumSalesAmt365;
    }

    public Integer getSumDaysInStock14() {
        return sumDaysInStock14;
    }
    public void setSumDaysInStock14(Integer sumDaysInStock14) {
        this.sumDaysInStock14 = sumDaysInStock14;
    }

    public Integer getSumDaysInStock60() {
        return sumDaysInStock60;
    }
    public void setSumDaysInStock60(Integer sumDaysInStock60) {
        this.sumDaysInStock60 = sumDaysInStock60;
    }

    public Double getSizeCoverageProc() {
        return sizeCoverageProc;
    }
    public void setSizeCoverageProc(Double sizeCoverageProc) {
        this.sizeCoverageProc = sizeCoverageProc;
    }

    public Integer getSumDelivSalesQty14() {
        return sumDelivSalesQty14;
    }
    public void setSumDelivSalesQty14(Integer sumDelivSalesQty14) {
        this.sumDelivSalesQty14 = sumDelivSalesQty14;
    }

    public Integer getSumDelivSalesQty60() {
        return sumDelivSalesQty60;
    }
    public void setSumDelivSalesQty60(Integer sumDelivSalesQty60) {
        this.sumDelivSalesQty60 = sumDelivSalesQty60;
    }

    public Double getGeometricAvgPoprice() {
        return geometricAvgPoprice;
    }
    public void setGeometricAvgPoprice(Double geometricAvgPoprice) {
        this.geometricAvgPoprice = geometricAvgPoprice;
    }

    public Integer getSumDelivSalesQty365() {
        return sumDelivSalesQty365;
    }
    public void setSumDelivSalesQty365(Integer sumDelivSalesQty365) {
        this.sumDelivSalesQty365 = sumDelivSalesQty365;
    }

    public Double getSumSalesAmtPreCartDisc14() {
        return sumSalesAmtPreCartDisc14;
    }
    public void setSumSalesAmtPreCartDisc14(Double sumSalesAmtPreCartDisc14) {
        this.sumSalesAmtPreCartDisc14 = sumSalesAmtPreCartDisc14;
    }

    public Double getSumSalesAmtPreCartDisc60() {
        return sumSalesAmtPreCartDisc60;
    }
    public void setSumSalesAmtPreCartDisc60(Double sumSalesAmtPreCartDisc60) {
        this.sumSalesAmtPreCartDisc60 = sumSalesAmtPreCartDisc60;
    }

    public Double getProcReturnsOnOrder30Momentan() {
        return procReturnsOnOrder30Momentan;
    }
    public void setProcReturnsOnOrder30Momentan(Double procReturnsOnOrder30Momentan) {
        this.procReturnsOnOrder30Momentan = procReturnsOnOrder30Momentan;
    }



    public static Builder builder() {
        return new Builder();
    }

    @javax.annotation.processing.Generated(
        value = "com.kobylynskyi.graphql.codegen.GraphQLCodegen",
        date = "2024-05-24T09:49:27+0200"
    )
    public static class Builder {

        private Double SPE14;
        private Double SPE60;
        private Integer maxSkuQty;
        private Integer minSkuQty;
        private Integer sumSkuQty;
        private Integer skuSizeCount;
        private Double sumReturnQty14;
        private Double sumReturnQty60;
        private Double sumGrossAmt14;
        private Double sumGrossAmt60;
        private Double sumProdCost14;
        private Double sumProdCost60;
        private Double sumSalesAmt14;
        private Double sumSalesAmt60;
        private Integer sumSkuinstock;
        private Double avgSkuPoprice;
        private java.util.Date startOfSalesDay;
        private Double sumReturnQty365;
        private Double sumGrossAmt365;
        private Double sumProdCost365;
        private Double sumSalesAmt365;
        private Integer sumDaysInStock14;
        private Integer sumDaysInStock60;
        private Double sizeCoverageProc;
        private Integer sumDelivSalesQty14;
        private Integer sumDelivSalesQty60;
        private Double geometricAvgPoprice;
        private Integer sumDelivSalesQty365;
        private Double sumSalesAmtPreCartDisc14;
        private Double sumSalesAmtPreCartDisc60;
        private Double procReturnsOnOrder30Momentan;

        public Builder() {
        }

        public Builder setSPE14(Double SPE14) {
            this.SPE14 = SPE14;
            return this;
        }

        public Builder setSPE60(Double SPE60) {
            this.SPE60 = SPE60;
            return this;
        }

        public Builder setMaxSkuQty(Integer maxSkuQty) {
            this.maxSkuQty = maxSkuQty;
            return this;
        }

        public Builder setMinSkuQty(Integer minSkuQty) {
            this.minSkuQty = minSkuQty;
            return this;
        }

        public Builder setSumSkuQty(Integer sumSkuQty) {
            this.sumSkuQty = sumSkuQty;
            return this;
        }

        public Builder setSkuSizeCount(Integer skuSizeCount) {
            this.skuSizeCount = skuSizeCount;
            return this;
        }

        public Builder setSumReturnQty14(Double sumReturnQty14) {
            this.sumReturnQty14 = sumReturnQty14;
            return this;
        }

        public Builder setSumReturnQty60(Double sumReturnQty60) {
            this.sumReturnQty60 = sumReturnQty60;
            return this;
        }

        public Builder setSumGrossAmt14(Double sumGrossAmt14) {
            this.sumGrossAmt14 = sumGrossAmt14;
            return this;
        }

        public Builder setSumGrossAmt60(Double sumGrossAmt60) {
            this.sumGrossAmt60 = sumGrossAmt60;
            return this;
        }

        public Builder setSumProdCost14(Double sumProdCost14) {
            this.sumProdCost14 = sumProdCost14;
            return this;
        }

        public Builder setSumProdCost60(Double sumProdCost60) {
            this.sumProdCost60 = sumProdCost60;
            return this;
        }

        public Builder setSumSalesAmt14(Double sumSalesAmt14) {
            this.sumSalesAmt14 = sumSalesAmt14;
            return this;
        }

        public Builder setSumSalesAmt60(Double sumSalesAmt60) {
            this.sumSalesAmt60 = sumSalesAmt60;
            return this;
        }

        public Builder setSumSkuinstock(Integer sumSkuinstock) {
            this.sumSkuinstock = sumSkuinstock;
            return this;
        }

        public Builder setAvgSkuPoprice(Double avgSkuPoprice) {
            this.avgSkuPoprice = avgSkuPoprice;
            return this;
        }

        public Builder setStartOfSalesDay(java.util.Date startOfSalesDay) {
            this.startOfSalesDay = startOfSalesDay;
            return this;
        }

        public Builder setSumReturnQty365(Double sumReturnQty365) {
            this.sumReturnQty365 = sumReturnQty365;
            return this;
        }

        public Builder setSumGrossAmt365(Double sumGrossAmt365) {
            this.sumGrossAmt365 = sumGrossAmt365;
            return this;
        }

        public Builder setSumProdCost365(Double sumProdCost365) {
            this.sumProdCost365 = sumProdCost365;
            return this;
        }

        public Builder setSumSalesAmt365(Double sumSalesAmt365) {
            this.sumSalesAmt365 = sumSalesAmt365;
            return this;
        }

        public Builder setSumDaysInStock14(Integer sumDaysInStock14) {
            this.sumDaysInStock14 = sumDaysInStock14;
            return this;
        }

        public Builder setSumDaysInStock60(Integer sumDaysInStock60) {
            this.sumDaysInStock60 = sumDaysInStock60;
            return this;
        }

        public Builder setSizeCoverageProc(Double sizeCoverageProc) {
            this.sizeCoverageProc = sizeCoverageProc;
            return this;
        }

        public Builder setSumDelivSalesQty14(Integer sumDelivSalesQty14) {
            this.sumDelivSalesQty14 = sumDelivSalesQty14;
            return this;
        }

        public Builder setSumDelivSalesQty60(Integer sumDelivSalesQty60) {
            this.sumDelivSalesQty60 = sumDelivSalesQty60;
            return this;
        }

        public Builder setGeometricAvgPoprice(Double geometricAvgPoprice) {
            this.geometricAvgPoprice = geometricAvgPoprice;
            return this;
        }

        public Builder setSumDelivSalesQty365(Integer sumDelivSalesQty365) {
            this.sumDelivSalesQty365 = sumDelivSalesQty365;
            return this;
        }

        public Builder setSumSalesAmtPreCartDisc14(Double sumSalesAmtPreCartDisc14) {
            this.sumSalesAmtPreCartDisc14 = sumSalesAmtPreCartDisc14;
            return this;
        }

        public Builder setSumSalesAmtPreCartDisc60(Double sumSalesAmtPreCartDisc60) {
            this.sumSalesAmtPreCartDisc60 = sumSalesAmtPreCartDisc60;
            return this;
        }

        public Builder setProcReturnsOnOrder30Momentan(Double procReturnsOnOrder30Momentan) {
            this.procReturnsOnOrder30Momentan = procReturnsOnOrder30Momentan;
            return this;
        }


        public ProductPerformance build() {
            return new ProductPerformance(SPE14, SPE60, maxSkuQty, minSkuQty, sumSkuQty, skuSizeCount, sumReturnQty14, sumReturnQty60, sumGrossAmt14, sumGrossAmt60, sumProdCost14, sumProdCost60, sumSalesAmt14, sumSalesAmt60, sumSkuinstock, avgSkuPoprice, startOfSalesDay, sumReturnQty365, sumGrossAmt365, sumProdCost365, sumSalesAmt365, sumDaysInStock14, sumDaysInStock60, sizeCoverageProc, sumDelivSalesQty14, sumDelivSalesQty60, geometricAvgPoprice, sumDelivSalesQty365, sumSalesAmtPreCartDisc14, sumSalesAmtPreCartDisc60, procReturnsOnOrder30Momentan);
        }

    }
}
