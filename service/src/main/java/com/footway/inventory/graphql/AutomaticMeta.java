package com.footway.inventory.graphql;


@javax.annotation.processing.Generated(
    value = "com.kobylynskyi.graphql.codegen.GraphQLCodegen",
    date = "2024-05-24T09:49:27+0200"
)
public class AutomaticMeta implements java.io.Serializable {

    private static final long serialVersionUID = 1L;

    private String view;
    private Integer width;
    private Integer height;
    private String status;
    private java.util.List<String> feature;

    public AutomaticMeta() {
    }

    public AutomaticMeta(String view, Integer width, Integer height, String status, java.util.List<String> feature) {
        this.view = view;
        this.width = width;
        this.height = height;
        this.status = status;
        this.feature = feature;
    }

    public String getView() {
        return view;
    }
    public void setView(String view) {
        this.view = view;
    }

    public Integer getWidth() {
        return width;
    }
    public void setWidth(Integer width) {
        this.width = width;
    }

    public Integer getHeight() {
        return height;
    }
    public void setHeight(Integer height) {
        this.height = height;
    }

    public String getStatus() {
        return status;
    }
    public void setStatus(String status) {
        this.status = status;
    }

    public java.util.List<String> getFeature() {
        return feature;
    }
    public void setFeature(java.util.List<String> feature) {
        this.feature = feature;
    }



    public static Builder builder() {
        return new Builder();
    }

    @javax.annotation.processing.Generated(
        value = "com.kobylynskyi.graphql.codegen.GraphQLCodegen",
        date = "2024-05-24T09:49:27+0200"
    )
    public static class Builder {

        private String view;
        private Integer width;
        private Integer height;
        private String status;
        private java.util.List<String> feature;

        public Builder() {
        }

        public Builder setView(String view) {
            this.view = view;
            return this;
        }

        public Builder setWidth(Integer width) {
            this.width = width;
            return this;
        }

        public Builder setHeight(Integer height) {
            this.height = height;
            return this;
        }

        public Builder setStatus(String status) {
            this.status = status;
            return this;
        }

        public Builder setFeature(java.util.List<String> feature) {
            this.feature = feature;
            return this;
        }


        public AutomaticMeta build() {
            return new AutomaticMeta(view, width, height, status, feature);
        }

    }
}
