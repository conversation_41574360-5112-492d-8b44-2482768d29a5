package com.footway.inventory;

import com.fasterxml.jackson.databind.JsonNode;
import com.footway.fop.shared.json.helper.ObjectSerializer;
import com.footway.fop.shared.webhook.PubSubMessageController;
import com.footway.fop.shared.webhook.repository.SubscriptionRepository;
import com.footway.fop.shared.webhook.service.PublishService;
import com.footway.fop.shared.webservice.component.SpringLifecycleHandler;
import jakarta.annotation.PreDestroy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@Slf4j
@RestController
public class InventoryPubSubMessageController extends PubSubMessageController {


    public InventoryPubSubMessageController(
            SubscriptionRepository subscriptionRepository,
            PublishService publishService,
            SpringLifecycleHandler springLifecycleHandler,
            ObjectSerializer objectSerializer
    ) {
        super(
                subscriptionRepository,
                publishService,
                springLifecycleHandler,
                objectSerializer
        );
    }

    @RequestMapping(value = "/", method = RequestMethod.POST)
    public ResponseEntity<JsonNode> receiveMessage(
            @RequestBody JsonNode body, @RequestHeader Map<String, String> headers) {
        // receive but do not process messages in inventory service
        return ResponseEntity.ok(body);
    }

    @PreDestroy
    public void onExit() {
        log.debug("Shutting down Pub/Sub controller");
    }
}
