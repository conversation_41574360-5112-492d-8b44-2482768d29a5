package com.footway.inventory.repository.jpa;

import com.footway.inventory.model.dao.InventoryView;
import com.footway.inventory.model.dto.InventoryItem;
import com.footway.inventory.service.filter.dto.AllCounterContainer;
import com.footway.inventory.service.filter.dto.CountPerGroup;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.TypedQuery;
import jakarta.persistence.criteria.*;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Repository;
import org.springframework.data.domain.Sort;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Repository
public class InventoryRepositoryCustomImpl implements InventoryRepositoryCustom {
    @Getter
    @PersistenceContext
    private EntityManager entityManager;

    @Cacheable("getAvailableFilters")
    @Override
    public AllCounterContainer getAvailableFilters() {
        CriteriaBuilder cb = getEntityManager().getCriteriaBuilder();
        CriteriaQuery<CountPerGroup> cq = cb.createQuery(CountPerGroup.class);
        Root<InventoryView> root = cq.from(InventoryView.class);

        // Add quantity > 0 condition
        cq.where(cb.gt(root.get("quantity"), 0));

        Expression<String> merchantId = root.get("merchantId");
        Expression<String> vendor = root.get("vendor");
        Expression<String> department = root.get("department");
        Expression<String> productGroup = root.get("productGroup");
        Expression<String> productType = root.get("productType");

        Expression<Long> count = cb.count(root);
        cq.multiselect(merchantId, vendor, department, productGroup, productType, count);
        cq.groupBy(merchantId, vendor, department, productGroup, productType);

        List<CountPerGroup> results = getEntityManager().createQuery(cq).getResultList();
        return new AllCounterContainer(results);
    }

    @Cacheable("searchInventory")
    @Override
    public List<InventoryView> searchInventory(
            List<String> merchantIds,
            String productName,
            List<String> vendors,
            List<String> departments,
            List<String> productGroups,
            List<String> productTypes,
            List<String> variantIds,
            String searchText,
            Pageable pageable
    ) {
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<InventoryView> cq = cb.createQuery(InventoryView.class);
        Root<InventoryView> root = cq.from(InventoryView.class);

        List<Predicate> predicates = buildPredicatesIncludingProductName(root, cb, merchantIds, vendors, departments, productGroups, productTypes, productName);
        predicates.add(cb.gt(root.get("quantity"), 0));

        if (variantIds != null && !variantIds.isEmpty()) {
            predicates.add(root.get("variantId").in(variantIds));
        }

        if (searchText != null && !searchText.trim().isEmpty()) {
            String likePattern = "%" + searchText.toLowerCase() + "%";
            Expression<List<String>> eanExpression = root.get("ean");

            Predicate textSearchPredicate = cb.or(
                    cb.like(cb.lower(root.get("productName")), likePattern),
                    cb.function("array_to_string", String.class, eanExpression, cb.literal(' ')).in(searchText)
            );
            predicates.add(textSearchPredicate);
        }
        cq.where(cb.and(predicates.toArray(new Predicate[0])));

        if (pageable.getSort().isSorted()) {
            List<Order> orders = new ArrayList<>();
            pageable.getSort().forEach(sort -> {
                if (sort.getDirection() == Sort.Direction.ASC) {
                    orders.add(cb.asc(root.get(sort.getProperty())));
                } else {
                    orders.add(cb.desc(root.get(sort.getProperty())));
                }
            });
            cq.orderBy(orders);
        } else {
            cq.orderBy(cb.desc(root.get("quantity")));
        }

        TypedQuery<InventoryView> query = entityManager.createQuery(cq);

        query.setFirstResult((int) pageable.getOffset());
        query.setMaxResults(pageable.getPageSize());

        return query.getResultList();
    }

    @Cacheable("exportInventory")
    @Override
    public List<InventoryItem> exportInventory(List<String> merchantIds) {
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<InventoryView> cq = cb.createQuery(InventoryView.class);
        Root<InventoryView> root = cq.from(InventoryView.class);

        Predicate merchantPredicate = root.get("merchantId").in(merchantIds);
        Predicate quantityPredicate = cb.gt(root.get("quantity"), 0);
        cq.where(cb.and(merchantPredicate, quantityPredicate));
        cq.orderBy(cb.desc(root.get("quantity")));
        TypedQuery<InventoryView> query = entityManager.createQuery(cq);

        return query.getResultList()
                .stream()
                .map(InventoryView::toInventory)  // assuming toInventory is a method that converts InventoryView to InventoryItem
                .collect(Collectors.toList());
    }

    private String array(List<String> list) {
        return "{" + String.join(",", list) + "}";
    }

    @Cacheable("countTotal")
    public long countTotal(List<String> merchantIds,
                            String productName,
                            List<String> vendors,
                            List<String> departments,
                            List<String> productGroups,
                            List<String> productTypes,
                            List<String> variantIds,
                            String searchText) {
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<Long> cq = cb.createQuery(Long.class);
        Root<InventoryView> root = cq.from(InventoryView.class);

        List<Predicate> predicates = buildPredicatesIncludingProductName(root, cb, merchantIds, vendors, departments, productGroups, productTypes, productName);
        if (variantIds != null && !variantIds.isEmpty()) {
            predicates.add(root.get("variantId").in(variantIds));
        }

        if (searchText != null && !searchText.trim().isEmpty()) {
            String likePattern = "%" + searchText.toLowerCase() + "%";
            Expression<List<String>> eanExpression = root.get("ean");

            Predicate textSearchPredicate = cb.or(
                    cb.like(cb.lower(root.get("productName")), likePattern),
                    cb.function("array_to_string", String.class, eanExpression, cb.literal(' ')).in(searchText)
            );
            predicates.add(textSearchPredicate);
        }
        predicates.add(cb.gt(root.get("quantity"), 0));

        cq.select(cb.count(root.get("id")));
        cq.where(cb.and(predicates.toArray(new Predicate[0])));
        return entityManager.createQuery(cq).getSingleResult();
    }

    private @NotNull List<Predicate> buildPredicatesIncludingProductName(Root<InventoryView> root, CriteriaBuilder cb, List<String> merchantIds, List<String> vendors, List<String> departments, List<String> productGroups, List<String> productTypes, String productName) {
        List<Predicate> predicates = buildPredicates(root, cb, merchantIds, vendors, departments, productGroups, productTypes);
        if (productName != null && !productName.isEmpty()) {
            predicates.add(cb.like(root.get("productName"), "%" + productName + "%"));
        }
        return predicates;
    }

    private @NotNull List<Predicate> buildPredicates(Root<InventoryView> root, CriteriaBuilder cb, List<String> merchantIds, List<String> vendors, List<String> departments, List<String> productGroups, List<String> productTypes) {
        List<Predicate> predicates = new ArrayList<>();

        if (merchantIds != null && !merchantIds.isEmpty()) {
            predicates.add(root.get("merchantId").in(merchantIds));
        }

        if (vendors != null && !vendors.isEmpty()) {
            predicates.add(root.get("vendor").in(vendors));
        }

        if (departments != null && !departments.isEmpty()) {
            predicates.add(cb.isTrue(cb.function("array_overlap", Boolean.class, root.get("department"), cb.literal(array(departments)))));
        }
        if (productGroups != null && !productGroups.isEmpty()) {
            predicates.add(cb.isTrue(cb.function("array_overlap", Boolean.class, root.get("productGroup"), cb.literal(array(productGroups)))));
        }
        if (productTypes != null && !productTypes.isEmpty()) {
            predicates.add(cb.isTrue(cb.function("array_overlap", Boolean.class, root.get("productType"), cb.literal(array(productTypes)))));
        }
        return predicates;
    }
}