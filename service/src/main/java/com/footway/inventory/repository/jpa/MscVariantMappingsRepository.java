package com.footway.inventory.repository.jpa;

import com.footway.inventory.model.dao.MscItemView;
import com.footway.inventory.model.dto.MultiSalesChannelMappingsProjection;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface MscVariantMappingsRepository extends JpaRepository<MscItemView, Long>, MscVariantMappingsRepositoryCustom {
    @Query(value = "SELECT " +
            " mp.merchant_id, mp.footway_variant_id, mp.integration_variant_id, mp.multi_sales_channel_variant_mapping_id msc_mapping_id, " +
            " va.product_name, va.size, va.ean AS msc_ean, va.image_url AS footway_image_url, ev.display_name AS integration_product, " +
            " ev.product_id, ev.ean, ev.image_url AS integration_image_url from" +
            " multi_sales_channel_variant_mappings mp" +
            " join variants va" +
            " on mp.footway_variant_id = va.variant_id" +
            " join external_variants ev" +
            " on mp.integration_variant_id = ev.variant_id" +
            " AND mp.merchant_id = ev.merchant_id" +
            " WHERE " +
            " mp.merchant_id =  :merchantId ",
            nativeQuery = true)
    Page<MultiSalesChannelMappingsProjection> findMultiSalesChannelVariantMappingsByMerchantId(
            @Param("merchantId") String merchantId,
            Pageable pageable
    );
}
