package com.footway.inventory.repository;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository
public class PurchaseOrderRepository {

    private final NamedParameterJdbcTemplate namedParameterJdbcTemplate;

    public PurchaseOrderRepository(@Qualifier("odsNamedParameterJdbcTemplate") NamedParameterJdbcTemplate namedParameterJdbcTemplate) {
        this.namedParameterJdbcTemplate = namedParameterJdbcTemplate;
    }

    public Map<String, Object> findPurchaseOrderHeadByOrderNumber(String orderNumber) {
        String sql = "SELECT * FROM poOrderHead WHERE orderno = :orderNo";
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("orderNo", orderNumber);

        return namedParameterJdbcTemplate.queryForMap(sql, paramMap);}

    public List<Map<String, Object>> findPurchaseOrderLinesByOrderNumber(String orderNumber) {
        String sql = "SELECT * FROM poorderline WHERE orderno = :orderNo";
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("orderNo", orderNumber);

        return namedParameterJdbcTemplate.queryForList(sql, paramMap);}


}
