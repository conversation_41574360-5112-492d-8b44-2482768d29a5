package com.footway.inventory.repository.jpa;

import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import com.footway.inventory.model.dao.StorageOrderLineTable;

@Repository
public interface StorageOrderLineRepository extends JpaRepository<StorageOrderLineTable, Long> {
    List<StorageOrderLineTable> findByStorageOrderId(String storageOrderId);

    List<StorageOrderLineTable> findByMerchantId(String merchantId);

    Optional<StorageOrderLineTable> findByStorageOrderIdAndProductNumber(String storageOrderId, String productNumber);

    long countByStorageOrderId(String storageOrderId);

    long countByMerchantId(String merchantId);

    default List<StorageOrderLineTable> findByStorageOrderIdWithCase(String storageOrderId) {
        return findByStorageOrderId(storageOrderId.toUpperCase());
    }

    default List<StorageOrderLineTable> findByMerchantIdWithCase(String merchantId) {
        return findByMerchantId(merchantId.toUpperCase());
    }

    default Optional<StorageOrderLineTable> findByStorageOrderIdAndProductNumberWithCase(String storageOrderId, String productNumber) {
        return findByStorageOrderIdAndProductNumber(storageOrderId.toUpperCase(), productNumber.toUpperCase());
    }

    default long countByStorageOrderIdWithCase(String storageOrderId) {
        return countByStorageOrderId(storageOrderId.toUpperCase());
    }

    default long countByMerchantIdWithCase(String merchantId) {
        return countByMerchantId(merchantId.toUpperCase());
    }
}