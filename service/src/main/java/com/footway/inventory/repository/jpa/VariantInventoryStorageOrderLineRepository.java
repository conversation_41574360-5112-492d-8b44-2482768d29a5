package com.footway.inventory.repository.jpa;

import com.footway.inventory.model.dao.VariantInventoryStorageOrderView;
import com.footway.inventory.model.dto.StorageOrderLineInventoryResponse;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface VariantInventoryStorageOrderLineRepository extends JpaRepository<VariantInventoryStorageOrderView, String> {
    @Query(value = "SELECT " +
            "  sol.merchant_id, product_name, size, product_brand, product_number, ean, metadata->'images'->0->>'url' AS url " +
            "FROM " +
            "  storage_order_lines sol " +
            "LEFT JOIN " +
            "  prat_variant_inventories_daily_snapshots pvids " +
            "ON " +
            "  sol.gtin_ean = pvids.ean " +
            "  AND pvids.merchant_id = sol.merchant_id " +
            "WHERE " +
            "  sol.merchant_id = :merchantId",
            nativeQuery = true)
    Page<StorageOrderLineInventoryResponse> findStorageOrderLinesWithInventoryInfo(
            @Param("merchantId") String merchantId,
            Pageable pageable
    );
}