package com.footway.inventory.repository.jpa;

import com.footway.inventory.model.dao.PratVariantInventoriesDailySnapshot;
import com.footway.inventory.model.dto.StorageOrderLineInventoryResponse;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface VariantInventoryRepository extends JpaRepository<PratVariantInventoriesDailySnapshot, Long> {
    @Query(value = "SELECT DISTINCT new com.footway.inventory.model.dto.StorageOrderLineInventoryResponse(" +
            "  sol.merchantId, productName,sol.size, sol.productBrand, sol.productNumber, pvids.ean, pvids.metadata->'images'->0->>'url' AS url " +
            "FROM " +
            "  StorageOrderLine sol " +
            "LEFT JOIN " +
            "  PratVariantInventoriesDailySnapshot pvids " +
            "ON " +
            "  sol.gtinEan = pvids.ean " +
            "  AND pvids.merchantId = sol.merchantId " +
            "WHERE " +
            "  sol.merchantId = :merchantId", nativeQuery = true)
        //FIXME: we should not use native in the end!
    Page<StorageOrderLineInventoryResponse> findStorageOrderLinesWithInventoryInfo(
            @Param("merchantId") String merchantId,
            Pageable pageable
    );


    // New method to find all by merchantId and latest snapshot date
    @Query("SELECT s FROM PratVariantInventoriesDailySnapshot s " +
            "WHERE s.id.merchantId IN(:merchantIds) " +
            "AND s.id.snapshotDate = (SELECT MAX(s2.id.snapshotDate) FROM PratVariantInventoriesDailySnapshot s2 WHERE s2.id.merchantId IN (:merchantIds))")
    List<PratVariantInventoriesDailySnapshot> findAllByMerchantIdsAndLatestSnapshotDate(@Param("merchantIds") List<String> merchantIds);

    // New method to find paginated results by merchantId and latest snapshot date
    @Query("SELECT s FROM PratVariantInventoriesDailySnapshot s " +
            "WHERE s.id.merchantId IN(:merchantIds) " +
            "AND s.id.snapshotDate = (SELECT MAX(s2.id.snapshotDate) FROM PratVariantInventoriesDailySnapshot s2 WHERE s2.id.merchantId IN (:merchantIds))" +
            "ORDER BY ean desc")
    Page<PratVariantInventoriesDailySnapshot> findPaginatedByMerchantIdsAndLatestSnapshotDate(
            @Param("merchantIds") List<String> merchantIds,
            Pageable pageable
    );
}