package com.footway.inventory.repository.jpa;

import com.footway.inventory.model.dao.InventoryView;
import com.footway.inventory.model.dto.InventoryItem;
import com.footway.inventory.service.filter.dto.AllCounterContainer;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface InventoryRepositoryCustom {
    AllCounterContainer getAvailableFilters();

    List<InventoryView> searchInventory(
            List<String> merchantIds,
            String productName,
            List<String> vendors,
            List<String> departments,
            List<String> productGroups,
            List<String> productTypes,
            List<String> variantIds,
            String searchText,
            Pageable pageable
    );

    List<InventoryItem> exportInventory(List<String> merchantIds);

    long countTotal(List<String> merchantIds,
                    String productName,
                    List<String> vendors,
                    List<String> departments,
                    List<String> productGroups,
                    List<String> productTypes,
                    List<String> variantIds,
                    String searchText);
}
