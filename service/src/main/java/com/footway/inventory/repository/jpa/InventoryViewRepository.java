package com.footway.inventory.repository.jpa;

import com.footway.inventory.model.dao.InventoryView;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface InventoryViewRepository extends JpaRepository<InventoryView, Long>, InventoryRepositoryCustom {
    Optional<InventoryView> findByMerchantIdAndVariantId(String merchantID, String variantId);

    @Query(value = "SELECT iv.id, iv.department, iv.ean, iv.image_url, iv.inventory_level_created, " +
            "iv.inventory_level_updated, iv.merchant_id, iv.price, iv.product_description, " +
            "iv.product_group, iv.product_name, iv.product_type, iv.quantity, iv.size, " +
            "iv.supplier_model_number, iv.variant_created, iv.variant_id, iv.variant_updated, " +
            "iv.vendor " +
            "FROM inventory_view iv " +
            "WHERE (:merchantIds IS NULL OR iv.merchant_id IN :merchantIds) " +
            "AND (:vendors IS NULL OR iv.vendor IN :vendors) " +
            "AND (:departments IS NULL OR iv.department && ARRAY[:departments]::text[]) " +
            "AND (:productGroups IS NULL OR iv.product_group && ARRAY[:productGroups]::text[]) " +
            "AND (:productTypes IS NULL OR iv.product_type && ARRAY[:productTypes]::text[]) " +
            "AND (:productName IS NULL OR iv.product_name LIKE :productName ESCAPE '') " +
            "AND iv.quantity > 0 " +
            "ORDER BY iv.quantity DESC " +
            "OFFSET :offset ROWS " +
            "FETCH FIRST :length ROWS ONLY", nativeQuery = true)
    List<InventoryView> searchInventoryNative(
            @Param("merchantIds") List<String> merchantIds,
            @Param("productName") String productName,
            @Param("vendors") List<String> vendors,
            @Param("departments") List<String> departments,
            @Param("productGroups") List<String> productGroups,
            @Param("productTypes") List<String> productTypes,
            @Param("offset") long offset,
            @Param("length") long length
    );

    default List<InventoryView> searchInventoryNative(List<String> merchantIds,
                                                      String productName,
                                                      List<String> vendors,
                                                      List<String> departments,
                                                      List<String> productGroups,
                                                      List<String> productTypes,
                                                      Pageable pageable
    ) {
        return searchInventoryNative(merchantIds, productName, vendors, departments, productGroups, productTypes, pageable.getOffset(), pageable.getPageSize());
    }

    @Query(value = "SELECT COUNT(iv.id) " +
            "FROM inventory_view iv " +
            "WHERE (:merchantIds IS NULL OR iv.merchant_id IN :merchantIds) " +
            "AND (:vendors IS NULL OR iv.vendor IN :vendors) " +
            "AND (:departments IS NULL OR iv.department && ARRAY[:departments]::text[]) " +
            "AND (:productGroups IS NULL OR iv.product_group && ARRAY[:productGroups]::text[]) " +
            "AND (:productTypes IS NULL OR iv.product_type && ARRAY[:productTypes]::text[]) " +
            "AND (:productName IS NULL OR iv.product_name LIKE :productName ESCAPE '') " +
            "AND iv.quantity > 0 ORDER BY iv.quantity DESC", nativeQuery = true)
    long countTotalNative(@Param("merchantIds") List<String> merchantIds,
                          @Param("productName") String productName,
                          @Param("vendors") List<String> vendors,
                          @Param("departments") List<String> departments,
                          @Param("productGroups") List<String> productGroups,
                          @Param("productTypes") List<String> productTypes);
}