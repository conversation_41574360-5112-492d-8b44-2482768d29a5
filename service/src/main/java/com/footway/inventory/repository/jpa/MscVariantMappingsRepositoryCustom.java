package com.footway.inventory.repository.jpa;

import com.footway.inventory.model.dto.MultiSalesChannelMappingsProjection;
import org.springframework.data.domain.Slice;
import org.springframework.stereotype.Repository;

@Repository
public interface MscVariantMappingsRepositoryCustom {
    
    /**
     * Find Multi Sales Channel Variant Mappings with flexible keyset pagination
     * 
     * @param merchantId The merchant ID to filter by
     * @param searchTerm Optional search term for ILIKE search on product_name
     * @param eanFilter Optional EAN value to filter by exact match
     * @param afterProductName Product name to start after (for forward pagination)
     * @param afterVariantId Variant ID to start after (for forward pagination)
     * @param beforeProductName Product name to end before (for backward pagination)
     * @param beforeVariantId Variant ID to end before (for backward pagination)
     * @param isDescending Whether to order results in descending order
     * @param limit Maximum number of records to return
     * @return Slice of mappings with pagination information
     */
    Slice<MultiSalesChannelMappingsProjection> findWithKeySetPagination(
            String merchantId,
            String searchTerm,
            String eanFilter,
            String afterProductName,
            String afterVariantId,
            String beforeProductName,
            String beforeVariantId,
            boolean isDescending,
            int limit
    );
} 