package com.footway.inventory.repository.jpa;

import com.footway.inventory.model.dto.MultiSalesChannelMappingsProjection;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Slice;
import org.springframework.data.domain.SliceImpl;
import org.springframework.stereotype.Repository;
import org.springframework.util.StringUtils;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Repository
public class MscVariantMappingsRepositoryImpl implements MscVariantMappingsRepositoryCustom {

    @Getter
    @PersistenceContext
    private EntityManager entityManager;

    @Override
    public Slice<MultiSalesChannelMappingsProjection> findWithKeySetPagination(
            String merchantId, 
            String searchTerm,
            String eanFilter,
            String afterProductName, 
            String afterVariantId,
            String beforeProductName, 
            String beforeVariantId, 
            boolean isDescending, 
            int limit) {
        
        // Parameter map to hold all parameters
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("merchantId", merchantId);
        parameters.put("limit", limit + 1); // Request one more to determine if there are more results
        
        // Build the SQL query dynamically with improved JOIN conditions
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT mp.merchant_id, mp.footway_variant_id, mp.integration_variant_id, ")
           .append("mp.multi_sales_channel_variant_mapping_id as msc_mapping_id, ")
           .append("va.product_name, va.size, va.ean AS msc_ean, va.image_url AS footway_image_url, ")
           .append("ev.display_name AS integration_product, ev.product_id, ev.ean, ")
           .append("ev.image_url AS integration_image_url FROM ")
           .append("multi_sales_channel_variant_mappings mp ")
           .append("JOIN variants va ON va.variant_id = mp.footway_variant_id ");
        
        // Add EAN filter to variants join condition if provided
        if (StringUtils.hasText(eanFilter)) {
            String ean = normaliseEan(eanFilter);
            sql.append("AND va.ean @> ARRAY[:eanFilter]::text[] ");
            parameters.put("eanFilter", ean);
        } else {
            sql.append(" ");
        }
        
        // Add external_variants join with EAN filter
        sql.append("JOIN external_variants ev ON mp.integration_variant_id = ev.variant_id ")
           .append("AND mp.merchant_id = ev.merchant_id ");
        
        sql.append("WHERE mp.merchant_id = :merchantId ");

        // Add search condition if search term is provided (using ILIKE with the GIN trigram index)
        if (StringUtils.hasText(searchTerm)) {
            sql.append(" AND va.product_name ILIKE :searchPattern ");
            // Add wildcards for partial matching
            parameters.put("searchPattern", "%" + searchTerm + "%");
        }

        // Add keySet pagination conditions if provided
        if (afterProductName != null && afterVariantId != null) {
            sql.append(" AND (va.product_name, va.variant_id) > (:afterProductName, :afterVariantId) ");
            parameters.put("afterProductName", afterProductName);
            parameters.put("afterVariantId", afterVariantId);
        }
        
        if (beforeProductName != null && beforeVariantId != null) {
            sql.append(" AND (va.product_name, va.variant_id) < (:beforeProductName, :beforeVariantId) ");
            parameters.put("beforeProductName", beforeProductName);
            parameters.put("beforeVariantId", beforeVariantId);
        }

        // Add ordering
        String direction = isDescending ? "DESC" : "ASC";
        
        // Use consistent ordering regardless of search
        sql.append(" ORDER BY va.product_name ")
           .append(direction)
           .append(", va.variant_id ");
        
        sql.append(" LIMIT :limit");

        // Create the native query
        Query query = entityManager.createNativeQuery(sql.toString());
        
        // Set the parameters
        parameters.forEach(query::setParameter);
        
        // Execute the query and transform results
        List<Object[]> results = query.getResultList();
        
        List<MultiSalesChannelMappingsProjection> mappings = results.stream()
                .map(this::mapToProjection)
                .toList();
        
        // Check if there are more results available
        boolean hasMore = mappings.size() > limit;
        
        // Truncate the results to the requested limit
        List<MultiSalesChannelMappingsProjection> content = hasMore ? 
                mappings.subList(0, limit) : mappings;
        
        // Create a SliceImpl with the content and has-more flag
        return new SliceImpl<>(content, Pageable.unpaged(), hasMore);
    }
    
    // Helper method to map raw database result to projection
    private MultiSalesChannelMappingsProjection mapToProjection(Object[] row) {
        // Safe conversion method to handle potential array values
        String merchantId = convertToString(row[0]);
        String footwayVariantId = convertToString(row[1]);
        String integrationVariantId = convertToString(row[2]);
        Long mscMappingId = (row[3] instanceof Number) ? ((Number) row[3]).longValue() : null;
        String productName = convertToString(row[4]);
        String size = convertToString(row[5]);
        String mscEan = convertToString(row[6]);
        String footwayImageUrl = convertToString(row[7]);
        String integrationProduct = convertToString(row[8]);
        String productId = convertToString(row[9]);
        String ean = convertToString(row[10]);
        String integrationImageUrl = convertToString(row[11]);
        
        return new MultiSalesChannelMappingsProjection() {
            @Override
            public String getMerchantId() {
                return merchantId;
            }

            @Override
            public String getFootwayVariantId() {
                return footwayVariantId;
            }

            @Override
            public String getIntegrationVariantId() {
                return integrationVariantId;
            }

            @Override
            public Long getMscMappingId() {
                return mscMappingId;
            }

            @Override
            public String getProductName() {
                return productName;
            }

            @Override
            public String getSize() {
                return size;
            }

            @Override
            public String getMscEan() {
                return mscEan;
            }

            @Override
            public String getFootwayImageUrl() {
                return footwayImageUrl;
            }

            @Override
            public String getIntegrationProduct() {
                return integrationProduct;
            }

            @Override
            public String getProductId() {
                return productId;
            }

            @Override
            public String getEan() {
                return ean;
            }

            @Override
            public String getIntegrationImageUrl() {
                return integrationImageUrl;
            }
        };
    }
    
    // Helper method to safely convert different types to String
    private String convertToString(Object value) {
        if (value == null) {
            return null;
        }
        // Handle String arrays
        if (value instanceof String[]) {
            String[] array = (String[]) value;
            return array.length > 0 ? array[0] : null;
        }
        // Handle other array types
        if (value.getClass().isArray()) {
            // For any other array type, convert to string representation
            Object[] array = (Object[]) value;
            return array.length > 0 && array[0] != null ? array[0].toString() : null;
        }
        // Regular string or other scalar value
        return value.toString();
    }

    // helper: left-pad with zeros to length 13
    private static String normaliseEan(String raw) {
        if (raw == null) return null;
        String trimmed = raw.trim();
        if (!trimmed.matches("\\d{1,13}")) {      // allow 1-13 digits only
            throw new IllegalArgumentException("EAN must contain 1-13 digits");
        }
        return String.format("%013d", Long.parseLong(trimmed));   // left-pad with 0s
    }
} 