package com.footway.inventory.repository.jpa;

import java.util.List;

import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Repository;

import com.footway.inventory.model.dao.MscItemView;
import com.footway.inventory.service.filter.dto.AllCounterContainer;

@Repository
public interface MscItemViewRepositoryCustom {
    AllCounterContainer getAvailableFilters();

    List<MscItemView> searchMsc(
            List<String> merchantIds,
            String productName,
            List<String> vendors,
            List<String> departments,
            List<String> productGroups,
            List<String> productTypes,
            Boolean isInStock,
            Pageable pageable
    );

    long countTotalMsc(List<String> merchantIds,
                       String productName,
                       List<String> vendors,
                       List<String> departments,
                       List<String> productGroups,
                       List<String> productTypes,
                       Boolean isInStock);

}
