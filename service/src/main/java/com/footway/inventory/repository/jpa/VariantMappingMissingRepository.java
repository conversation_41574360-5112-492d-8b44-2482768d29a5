package com.footway.inventory.repository.jpa;

import com.footway.inventory.model.dao.VariantMappingMissingTable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface VariantMappingMissingRepository extends JpaRepository<VariantMappingMissingTable, Long>, JpaSpecificationExecutor<VariantMappingMissingTable> {

    Optional<VariantMappingMissingTable> findByMerchantCodeAndIntegrationVariantId(String merchantCode, String integrationVariantId);

    Optional<VariantMappingMissingTable> findByMerchantCodeAndFootwayVariantId(String merchantCode, String footwayVariantId);

    Optional<VariantMappingMissingTable> findByMerchantCodeAndFootwayVariantIdAndIntegrationVariantId(String merchantCode, String footwayVariantId, String integrationVariantId);
}
