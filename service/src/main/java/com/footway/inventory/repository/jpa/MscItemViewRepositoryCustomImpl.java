package com.footway.inventory.repository.jpa;

import java.util.ArrayList;
import java.util.List;

import org.jetbrains.annotations.NotNull;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Repository;

import com.footway.inventory.model.dao.MscItemView;
import com.footway.inventory.service.filter.dto.AllCounterContainer;
import com.footway.inventory.service.filter.dto.CountPerGroup;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.TypedQuery;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Expression;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Repository
public class MscItemViewRepositoryCustomImpl implements MscItemViewRepositoryCustom {
    @Getter
    @PersistenceContext
    private EntityManager entityManager;

    @Cacheable("getAvailableFilters")
    @Override
    public AllCounterContainer getAvailableFilters() {
        CriteriaBuilder cb = getEntityManager().getCriteriaBuilder();
        CriteriaQuery<CountPerGroup> cq = cb.createQuery(CountPerGroup.class); //return type
        Root<MscItemView> root = cq.from(MscItemView.class);

        Expression<String> merchantId = root.get("merchantId");
        Expression<String> vendor = root.get("vendor");
        Expression<String> department = root.get("department");
        Expression<String> productGroup = root.get("productGroup");
        Expression<String> productType = root.get("productType");

        Expression<Long> count = cb.count(root);
        cq.multiselect(merchantId, vendor, department, productGroup, productType, count);
        cq.groupBy(merchantId, vendor, department, productGroup, productType);

        List<CountPerGroup> results = getEntityManager().createQuery(cq).getResultList();
        return new AllCounterContainer(results);
    }

    @Cacheable("searchMsc")
    @Override
    public List<MscItemView> searchMsc(
            List<String> merchantIds,
            String productName,
            List<String> vendors,
            List<String> departments,
            List<String> productGroups,
            List<String> productTypes,
            Boolean isInStock,
            Pageable pageable
    ) {

        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<MscItemView> cq = cb.createQuery(MscItemView.class);
        Root<MscItemView> root = cq.from(MscItemView.class);

        cq.where(cb.and(buildPredicatesIncludingProductName(root, cb, merchantIds, vendors, departments, productGroups, productTypes, productName, isInStock).toArray(new Predicate[0])));

        cq.orderBy(cb.asc(root.get("id")));

        TypedQuery<MscItemView> query = entityManager.createQuery(cq);

        query.setFirstResult((int) pageable.getOffset());
        query.setMaxResults(pageable.getPageSize());

        return query.getResultList();
    }

    private String array(List<String> list) {
        return "{" + String.join(",", list) + "}";
    }

    @Cacheable("countTotalMsc")
    @Override
    public long countTotalMsc(List<String> merchantIds,
                              String productName,
                              List<String> vendors,
                              List<String> departments,
                              List<String> productGroups,
                              List<String> productTypes,
                              Boolean isInStock) {
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<Long> cq = cb.createQuery(Long.class);
        Root<MscItemView> root = cq.from(MscItemView.class);

        cq.select(cb.count(root.get("id")));
        cq.where(cb.and(buildPredicatesIncludingProductName(root, cb, merchantIds, vendors, departments, productGroups, productTypes, productName, isInStock).toArray(new Predicate[0])));
        return entityManager.createQuery(cq).getSingleResult();
    }


    private @NotNull List<Predicate> buildPredicatesIncludingProductName(Root<MscItemView> root, CriteriaBuilder cb, List<String> merchantIds, List<String> vendors, List<String> departments, List<String> productGroups, List<String> productTypes, String productName, Boolean isInStock) {
        List<Predicate> predicates = buildPredicates(root, cb, merchantIds, vendors, departments, productGroups, productTypes);
        if (productName != null && !productName.isEmpty()) {
            predicates.add(cb.like(root.get("productName"), "%" + productName + "%"));
        }
        if (isInStock != null) {
            if (isInStock) {
                predicates.add(cb.greaterThan(root.get("quantity"), 0));
            } else {
                predicates.add(cb.equal(root.get("quantity"), 0));
            }
        }
        return predicates;
    }

    private @NotNull List<Predicate> buildPredicates(Root<MscItemView> root, CriteriaBuilder cb, List<String> merchantIds, List<String> vendors, List<String> departments, List<String> productGroups, List<String> productTypes) {
        List<Predicate> predicates = new ArrayList<>();

        if (merchantIds != null && !merchantIds.isEmpty()) {
            predicates.add(root.get("merchantId").in(merchantIds));
        }


        if (vendors != null && !vendors.isEmpty()) {
            predicates.add(root.get("vendor").in(vendors));
        }

        // Array filters using PostgreSQL-specific function
        if (departments != null && !departments.isEmpty()) {
            predicates.add(cb.isTrue(cb.function("array_overlap", Boolean.class, root.get("department"), cb.literal(array(departments)))));
        }
        if (productGroups != null && !productGroups.isEmpty()) {
            predicates.add(cb.isTrue(cb.function("array_overlap", Boolean.class, root.get("productGroup"), cb.literal(array(productGroups)))));
        }
        if (productTypes != null && !productTypes.isEmpty()) {
            predicates.add(cb.isTrue(cb.function("array_overlap", Boolean.class, root.get("productType"), cb.literal(array(productTypes)))));
        }
        return predicates;
    }
}