package com.footway.inventory.repository.jpa;

import com.footway.inventory.model.dao.MscItemView;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface MscItemViewRepository extends JpaRepository<MscItemView, Long>, MscItemViewRepositoryCustom {

    @Query(value = "SELECT mscv.id, mscv.department, mscv.ean, mscv.image_url, mscv.inventory_level_created, " +
            "mscv.inventory_level_updated, mscv.merchant_id, mscv.price, mscv.product_description, " +
            "mscv.product_group, mscv.product_name, mscv.product_type, mscv.quantity, mscv.size, " +
            "mscv.supplier_model_number, mscv.variant_created, mscv.variant_id, mscv.variant_updated, " +
            "mscv.vendor " +
            "FROM msc_variant_view mscv " +
            "WHERE (:merchantIds IS NULL OR (mscv.merchant_id IN :merchantIds)) " +
            "AND (:vendors IS NULL OR mscv.vendor IN :vendors) " +
            "AND (:departments IS NULL OR mscv.department && ARRAY[:departments]::text[]) " +
            "AND (:productGroups IS NULL OR mscv.product_group && ARRAY[:productGroups]::text[]) " +
            "AND (:productTypes IS NULL OR mscv.product_type && ARRAY[:productTypes]::text[]) " +
            "AND (:productName IS NULL OR mscv.product_name LIKE :productName ESCAPE '') " +
            "ORDER BY mscv.id " +
            "OFFSET :offset ROWS " +
            "FETCH FIRST :length ROWS ONLY", nativeQuery = true)
    List<MscItemView> searchMscNative(
            @Param("merchantIds") List<String> merchantIds,
            @Param("productName") String productName,
            @Param("vendors") List<String> vendors,
            @Param("departments") List<String> departments,
            @Param("productGroups") List<String> productGroups,
            @Param("productTypes") List<String> productTypes,
            @Param("offset") long offset,
            @Param("length") long length
    );

    default List<MscItemView> searchMscNative(
            List<String> merchantIds,
            String productName,
            List<String> vendors,
            List<String> departments,
            List<String> productGroups,
            List<String> productTypes,
            Pageable pageable
    ) {
        return searchMscNative(merchantIds, productName, vendors, departments, productGroups, productTypes, pageable.getOffset(), pageable.getPageSize());
    }

    @Query(value = "SELECT COUNT(mscv.id) " +
            "FROM msc_variant_view mscv " +
            "WHERE (:merchantIds IS NULL OR mscv.merchant_id IN :merchantIds) " +
            "AND (:vendors IS NULL OR mscv.vendor IN :vendors) " +
            "AND (:departments IS NULL OR mscv.department && ARRAY[:departments]::text[]) " +
            "AND (:productGroups IS NULL OR mscv.product_group && ARRAY[:productGroups]::text[]) " +
            "AND (:productTypes IS NULL OR mscv.product_type && ARRAY[:productTypes]::text[]) " +
            "AND (:productName IS NULL OR mscv.product_name LIKE :productName ESCAPE '') " +
            "AND mscv.quantity > 0", nativeQuery = true)
    long countTotalMscNative(@Param("merchantIds") List<String> merchantIds,
                             @Param("productName") String productName,
                             @Param("vendors") List<String> vendors,
                             @Param("departments") List<String> departments,
                             @Param("productGroups") List<String> productGroups,
                             @Param("productTypes") List<String> productTypes);
}