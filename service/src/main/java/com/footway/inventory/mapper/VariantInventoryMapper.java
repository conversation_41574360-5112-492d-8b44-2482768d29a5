package com.footway.inventory.mapper;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import com.footway.inventory.model.dao.PratVariantInventoriesDailySnapshot;
import com.footway.inventory.model.dto.MerchantPortalVariantInventory;

@Mapper(componentModel = "spring")
public interface VariantInventoryMapper {

    @Mapping(source = "id.merchantId", target = "merchantId")
    @Mapping(source = "id.variantId", target = "variantId")
    @Mapping(source = "id.snapshotDate", target = "snapshotDate")
    MerchantPortalVariantInventory pratVariantInventoriesDailySnapshotToMerchantPortalVariantInventory(PratVariantInventoriesDailySnapshot source);
}
