package com.footway.inventory.mapper;

import java.time.OffsetDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Map;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

import com.footway.inventory.model.dao.MultiSalesChannelItem;
import com.footway.inventory.model.dto.MultiSalesChannelItemRequest;
import com.footway.inventory.model.dto.MultiSalesChannelItemResponse;
import com.footway.inventory.model.dto.MultiSalesChannelItemSnapshotDto;
import com.footway.inventory.model.dto.MultiSalesChannelItemSnapshotResponse;

@Mapper(componentModel = "spring",  imports = OffsetDateTime.class)
public interface MultiSalesChannelItemMapper {

    @Mapping(target = "created", expression = "java(formatDateTime(source.getCreated()))")
    @Mapping(target = "updated", expression = "java(formatDateTime(source.getUpdated()))")
    @Mapping(target = "metadata", expression = "java(mapMetadata(source.getMetadata()))")
    MultiSalesChannelItemResponse multiSalesChannelItemToMultiSalesChannelItemResponse(MultiSalesChannelItem source);

    default String formatDateTime(OffsetDateTime dateTime) {
        return dateTime != null ? dateTime.format(DateTimeFormatter.ISO_OFFSET_DATE_TIME) : null;
    }

    default Map<String, Object> mapMetadata(Map<String, Object> metadata) {
        return metadata != null ? metadata : Map.of();
    }

    @Mapping(target = "multiSalesChannelItemId", ignore = true)
    @Mapping(target = "created", expression = "java(OffsetDateTime.now())")
    @Mapping(target = "updated", expression = "java(OffsetDateTime.now())")
    MultiSalesChannelItem requestToEntity(MultiSalesChannelItemRequest request);

    @Mapping(target = "multiSalesChannelItemId", ignore = true)
    @Mapping(target = "created", ignore = true)
    @Mapping(target = "updated", expression = "java(OffsetDateTime.now())")
    void updateEntityFromRequest(MultiSalesChannelItemRequest request, @MappingTarget MultiSalesChannelItem entity);


    @Mapping(target = "ean", expression ="java(source.getEan().stream().findFirst().orElse(null))")
    MultiSalesChannelItemSnapshotResponse dtoToResponse(MultiSalesChannelItemSnapshotDto source);
}
