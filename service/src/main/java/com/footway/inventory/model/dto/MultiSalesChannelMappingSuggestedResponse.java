package com.footway.inventory.model.dto;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.ArrayList;
import java.util.List;
import jakarta.validation.Valid;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.annotation.Generated;

/**
 * MultiSalesChannelMappingSuggestedResponse
 */

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen", date = "2025-04-11T11:05:04.226837+02:00[Europe/Stockholm]")
public class MultiSalesChannelMappingSuggestedResponse {

  private String merchantId;

  private String footwayVariantId;

  private String integrationVariantId;

  private String footwayProductName;

  @Valid
  private List<String> footwayEan;

  private String footwayImageUrl;

  private String integrationProductName;

  private String productId;

  private String ean;

  private String integrationImageUrl;

  public MultiSalesChannelMappingSuggestedResponse merchantId(String merchantId) {
    this.merchantId = merchantId;
    return this;
  }

  /**
   * Get merchantId
   * @return merchantId
  */
  
  @Schema(name = "merchantId", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("merchantId")
  public String getMerchantId() {
    return merchantId;
  }

  public void setMerchantId(String merchantId) {
    this.merchantId = merchantId;
  }

  public MultiSalesChannelMappingSuggestedResponse footwayVariantId(String footwayVariantId) {
    this.footwayVariantId = footwayVariantId;
    return this;
  }

  /**
   * Get footwayVariantId
   * @return footwayVariantId
  */
  
  @Schema(name = "footwayVariantId", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("footwayVariantId")
  public String getFootwayVariantId() {
    return footwayVariantId;
  }

  public void setFootwayVariantId(String footwayVariantId) {
    this.footwayVariantId = footwayVariantId;
  }

  public MultiSalesChannelMappingSuggestedResponse integrationVariantId(String integrationVariantId) {
    this.integrationVariantId = integrationVariantId;
    return this;
  }

  /**
   * Get integrationVariantId
   * @return integrationVariantId
  */
  
  @Schema(name = "integrationVariantId", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("integrationVariantId")
  public String getIntegrationVariantId() {
    return integrationVariantId;
  }

  public void setIntegrationVariantId(String integrationVariantId) {
    this.integrationVariantId = integrationVariantId;
  }

  public MultiSalesChannelMappingSuggestedResponse footwayProductName(String footwayProductName) {
    this.footwayProductName = footwayProductName;
    return this;
  }

  /**
   * Get footwayProductName
   * @return footwayProductName
  */
  
  @Schema(name = "footwayProductName", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("footwayProductName")
  public String getFootwayProductName() {
    return footwayProductName;
  }

  public void setFootwayProductName(String footwayProductName) {
    this.footwayProductName = footwayProductName;
  }

  public MultiSalesChannelMappingSuggestedResponse footwayEan(List<String> footwayEan) {
    this.footwayEan = footwayEan;
    return this;
  }

  public MultiSalesChannelMappingSuggestedResponse addFootwayEanItem(String footwayEanItem) {
    if (this.footwayEan == null) {
      this.footwayEan = new ArrayList<>();
    }
    this.footwayEan.add(footwayEanItem);
    return this;
  }

  /**
   * Get footwayEan
   * @return footwayEan
  */
  
  @Schema(name = "footwayEan", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("footwayEan")
  public List<String> getFootwayEan() {
    return footwayEan;
  }

  public void setFootwayEan(List<String> footwayEan) {
    this.footwayEan = footwayEan;
  }

  public MultiSalesChannelMappingSuggestedResponse footwayImageUrl(String footwayImageUrl) {
    this.footwayImageUrl = footwayImageUrl;
    return this;
  }

  /**
   * Get footwayImageUrl
   * @return footwayImageUrl
  */
  
  @Schema(name = "footwayImageUrl", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("footwayImageUrl")
  public String getFootwayImageUrl() {
    return footwayImageUrl;
  }

  public void setFootwayImageUrl(String footwayImageUrl) {
    this.footwayImageUrl = footwayImageUrl;
  }

  public MultiSalesChannelMappingSuggestedResponse integrationProductName(String integrationProductName) {
    this.integrationProductName = integrationProductName;
    return this;
  }

  /**
   * Get integrationProductName
   * @return integrationProductName
  */
  
  @Schema(name = "integrationProductName", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("integrationProductName")
  public String getIntegrationProductName() {
    return integrationProductName;
  }

  public void setIntegrationProductName(String integrationProductName) {
    this.integrationProductName = integrationProductName;
  }

  public MultiSalesChannelMappingSuggestedResponse productId(String productId) {
    this.productId = productId;
    return this;
  }

  /**
   * Get productId
   * @return productId
  */
  
  @Schema(name = "productId", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("productId")
  public String getProductId() {
    return productId;
  }

  public void setProductId(String productId) {
    this.productId = productId;
  }

  public MultiSalesChannelMappingSuggestedResponse ean(String ean) {
    this.ean = ean;
    return this;
  }

  /**
   * Get ean
   * @return ean
  */
  
  @Schema(name = "ean", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("ean")
  public String getEan() {
    return ean;
  }

  public void setEan(String ean) {
    this.ean = ean;
  }

  public MultiSalesChannelMappingSuggestedResponse integrationImageUrl(String integrationImageUrl) {
    this.integrationImageUrl = integrationImageUrl;
    return this;
  }

  /**
   * Get integrationImageUrl
   * @return integrationImageUrl
  */
  
  @Schema(name = "integrationImageUrl", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("integrationImageUrl")
  public String getIntegrationImageUrl() {
    return integrationImageUrl;
  }

  public void setIntegrationImageUrl(String integrationImageUrl) {
    this.integrationImageUrl = integrationImageUrl;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    MultiSalesChannelMappingSuggestedResponse multiSalesChannelMappingSuggestedResponse = (MultiSalesChannelMappingSuggestedResponse) o;
    return Objects.equals(this.merchantId, multiSalesChannelMappingSuggestedResponse.merchantId) &&
        Objects.equals(this.footwayVariantId, multiSalesChannelMappingSuggestedResponse.footwayVariantId) &&
        Objects.equals(this.integrationVariantId, multiSalesChannelMappingSuggestedResponse.integrationVariantId) &&
        Objects.equals(this.footwayProductName, multiSalesChannelMappingSuggestedResponse.footwayProductName) &&
        Objects.equals(this.footwayEan, multiSalesChannelMappingSuggestedResponse.footwayEan) &&
        Objects.equals(this.footwayImageUrl, multiSalesChannelMappingSuggestedResponse.footwayImageUrl) &&
        Objects.equals(this.integrationProductName, multiSalesChannelMappingSuggestedResponse.integrationProductName) &&
        Objects.equals(this.productId, multiSalesChannelMappingSuggestedResponse.productId) &&
        Objects.equals(this.ean, multiSalesChannelMappingSuggestedResponse.ean) &&
        Objects.equals(this.integrationImageUrl, multiSalesChannelMappingSuggestedResponse.integrationImageUrl);
  }

  @Override
  public int hashCode() {
    return Objects.hash(merchantId, footwayVariantId, integrationVariantId, footwayProductName, footwayEan, footwayImageUrl, integrationProductName, productId, ean, integrationImageUrl);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class MultiSalesChannelMappingSuggestedResponse {\n");
    sb.append("    merchantId: ").append(toIndentedString(merchantId)).append("\n");
    sb.append("    footwayVariantId: ").append(toIndentedString(footwayVariantId)).append("\n");
    sb.append("    integrationVariantId: ").append(toIndentedString(integrationVariantId)).append("\n");
    sb.append("    footwayProductName: ").append(toIndentedString(footwayProductName)).append("\n");
    sb.append("    footwayEan: ").append(toIndentedString(footwayEan)).append("\n");
    sb.append("    footwayImageUrl: ").append(toIndentedString(footwayImageUrl)).append("\n");
    sb.append("    integrationProductName: ").append(toIndentedString(integrationProductName)).append("\n");
    sb.append("    productId: ").append(toIndentedString(productId)).append("\n");
    sb.append("    ean: ").append(toIndentedString(ean)).append("\n");
    sb.append("    integrationImageUrl: ").append(toIndentedString(integrationImageUrl)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

