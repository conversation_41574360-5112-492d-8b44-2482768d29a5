package com.footway.inventory.model.dto;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import com.fasterxml.jackson.annotation.JsonProperty;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.annotation.Generated;
import jakarta.validation.Valid;

/**
 * PaginatedMultiSalesChannelItemSnapshotResponse
 */

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen", date = "2024-09-20T09:40:21.232235+02:00[Europe/Stockholm]")
public class PaginatedMultiSalesChannelItemSnapshotResponse {

  @Valid
  private List<com.footway.inventory.model.dto.MultiSalesChannelItemSnapshotResponse> items;

  private Integer totalItems;

  private Integer currentPage;

  private Integer totalPages;

  public PaginatedMultiSalesChannelItemSnapshotResponse items(List<com.footway.inventory.model.dto.MultiSalesChannelItemSnapshotResponse> items) {
    this.items = items;
    return this;
  }

  public PaginatedMultiSalesChannelItemSnapshotResponse addItemsItem(MultiSalesChannelItemSnapshotResponse itemsItem) {
    if (this.items == null) {
      this.items = new ArrayList<>();
    }
    this.items.add(itemsItem);
    return this;
  }

  /**
   * Get items
   * @return items
  */
  @Valid 
  @Schema(name = "Items", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("Items")
  public List<com.footway.inventory.model.dto.MultiSalesChannelItemSnapshotResponse> getItems() {
    return items;
  }

  public void setItems(List<com.footway.inventory.model.dto.MultiSalesChannelItemSnapshotResponse> items) {
    this.items = items;
  }

  public PaginatedMultiSalesChannelItemSnapshotResponse totalItems(Integer totalItems) {
    this.totalItems = totalItems;
    return this;
  }

  /**
   * Get totalItems
   * @return totalItems
  */
  
  @Schema(name = "totalItems", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("totalItems")
  public Integer getTotalItems() {
    return totalItems;
  }

  public void setTotalItems(Integer totalItems) {
    this.totalItems = totalItems;
  }

  public PaginatedMultiSalesChannelItemSnapshotResponse currentPage(Integer currentPage) {
    this.currentPage = currentPage;
    return this;
  }

  /**
   * Get currentPage
   * @return currentPage
  */
  
  @Schema(name = "currentPage", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("currentPage")
  public Integer getCurrentPage() {
    return currentPage;
  }

  public void setCurrentPage(Integer currentPage) {
    this.currentPage = currentPage;
  }

  public PaginatedMultiSalesChannelItemSnapshotResponse totalPages(Integer totalPages) {
    this.totalPages = totalPages;
    return this;
  }

  /**
   * Get totalPages
   * @return totalPages
  */
  
  @Schema(name = "totalPages", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("totalPages")
  public Integer getTotalPages() {
    return totalPages;
  }

  public void setTotalPages(Integer totalPages) {
    this.totalPages = totalPages;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PaginatedMultiSalesChannelItemSnapshotResponse paginatedMultiSalesChannelItemSnapshotResponse = (PaginatedMultiSalesChannelItemSnapshotResponse) o;
    return Objects.equals(this.items, paginatedMultiSalesChannelItemSnapshotResponse.items) &&
        Objects.equals(this.totalItems, paginatedMultiSalesChannelItemSnapshotResponse.totalItems) &&
        Objects.equals(this.currentPage, paginatedMultiSalesChannelItemSnapshotResponse.currentPage) &&
        Objects.equals(this.totalPages, paginatedMultiSalesChannelItemSnapshotResponse.totalPages);
  }

  @Override
  public int hashCode() {
    return Objects.hash(items, totalItems, currentPage, totalPages);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PaginatedMultiSalesChannelItemSnapshotResponse {\n");
    sb.append("    items: ").append(toIndentedString(items)).append("\n");
    sb.append("    totalItems: ").append(toIndentedString(totalItems)).append("\n");
    sb.append("    currentPage: ").append(toIndentedString(currentPage)).append("\n");
    sb.append("    totalPages: ").append(toIndentedString(totalPages)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

