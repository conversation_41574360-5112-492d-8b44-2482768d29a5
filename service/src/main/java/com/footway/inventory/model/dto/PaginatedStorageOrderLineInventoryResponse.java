package com.footway.inventory.model.dto;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import com.fasterxml.jackson.annotation.JsonProperty;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.annotation.Generated;
import jakarta.validation.Valid;

/**
 * PaginatedStorageOrderLineInventoryResponse
 */

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen", date = "2024-10-28T10:06:20.421677+01:00[Europe/Stockholm]")
public class PaginatedStorageOrderLineInventoryResponse {

  @Valid
  private List<@Valid StorageOrderLineInventoryResponse> items;

  private Integer totalItems;

  private Integer currentPage;

  private Integer totalPages;

  public PaginatedStorageOrderLineInventoryResponse items(List<@Valid StorageOrderLineInventoryResponse> items) {
    this.items = items;
    return this;
  }

  public PaginatedStorageOrderLineInventoryResponse addItemsItem(StorageOrderLineInventoryResponse itemsItem) {
    if (this.items == null) {
      this.items = new ArrayList<>();
    }
    this.items.add(itemsItem);
    return this;
  }

  /**
   * Get items
   * @return items
  */
  @Valid 
  @Schema(name = "items", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("items")
  public List<@Valid StorageOrderLineInventoryResponse> getItems() {
    return items;
  }

  public void setItems(List<@Valid StorageOrderLineInventoryResponse> items) {
    this.items = items;
  }

  public PaginatedStorageOrderLineInventoryResponse totalItems(Integer totalItems) {
    this.totalItems = totalItems;
    return this;
  }

  /**
   * Total number of items across all pages
   * @return totalItems
  */
  
  @Schema(name = "totalItems", example = "100", description = "Total number of items across all pages", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("totalItems")
  public Integer getTotalItems() {
    return totalItems;
  }

  public void setTotalItems(Integer totalItems) {
    this.totalItems = totalItems;
  }

  public PaginatedStorageOrderLineInventoryResponse currentPage(Integer currentPage) {
    this.currentPage = currentPage;
    return this;
  }

  /**
   * Current page number
   * @return currentPage
  */
  
  @Schema(name = "currentPage", example = "1", description = "Current page number", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("currentPage")
  public Integer getCurrentPage() {
    return currentPage;
  }

  public void setCurrentPage(Integer currentPage) {
    this.currentPage = currentPage;
  }

  public PaginatedStorageOrderLineInventoryResponse totalPages(Integer totalPages) {
    this.totalPages = totalPages;
    return this;
  }

  /**
   * Total number of pages
   * @return totalPages
  */
  
  @Schema(name = "totalPages", example = "5", description = "Total number of pages", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("totalPages")
  public Integer getTotalPages() {
    return totalPages;
  }

  public void setTotalPages(Integer totalPages) {
    this.totalPages = totalPages;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PaginatedStorageOrderLineInventoryResponse paginatedStorageOrderLineInventoryResponse = (PaginatedStorageOrderLineInventoryResponse) o;
    return Objects.equals(this.items, paginatedStorageOrderLineInventoryResponse.items) &&
        Objects.equals(this.totalItems, paginatedStorageOrderLineInventoryResponse.totalItems) &&
        Objects.equals(this.currentPage, paginatedStorageOrderLineInventoryResponse.currentPage) &&
        Objects.equals(this.totalPages, paginatedStorageOrderLineInventoryResponse.totalPages);
  }

  @Override
  public int hashCode() {
    return Objects.hash(items, totalItems, currentPage, totalPages);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PaginatedStorageOrderLineInventoryResponse {\n");
    sb.append("    items: ").append(toIndentedString(items)).append("\n");
    sb.append("    totalItems: ").append(toIndentedString(totalItems)).append("\n");
    sb.append("    currentPage: ").append(toIndentedString(currentPage)).append("\n");
    sb.append("    totalPages: ").append(toIndentedString(totalPages)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

