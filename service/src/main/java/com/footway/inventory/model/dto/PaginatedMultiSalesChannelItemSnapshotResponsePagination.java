package com.footway.inventory.model.dto;

import java.util.Objects;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonTypeName;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.annotation.Generated;

/**
 * PaginatedMultiSalesChannelItemSnapshotResponsePagination
 */

@JsonTypeName("PaginatedMultiSalesChannelItemSnapshotResponse_Pagination")
@Generated(value = "org.openapitools.codegen.languages.SpringCodegen", date = "2024-09-20T09:32:06.702645+02:00[Europe/Stockholm]")
public class PaginatedMultiSalesChannelItemSnapshotResponsePagination {

  private Integer offset;

  private Integer pageSize;

  private Integer pageNumber;

  private Integer totalPages;

  private Integer totalElements;

  private Boolean last;

  public PaginatedMultiSalesChannelItemSnapshotResponsePagination offset(Integer offset) {
    this.offset = offset;
    return this;
  }

  /**
   * Get offset
   * @return offset
  */
  
  @Schema(name = "offset", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("offset")
  public Integer getOffset() {
    return offset;
  }

  public void setOffset(Integer offset) {
    this.offset = offset;
  }

  public PaginatedMultiSalesChannelItemSnapshotResponsePagination pageSize(Integer pageSize) {
    this.pageSize = pageSize;
    return this;
  }

  /**
   * Get pageSize
   * @return pageSize
  */
  
  @Schema(name = "pageSize", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("pageSize")
  public Integer getPageSize() {
    return pageSize;
  }

  public void setPageSize(Integer pageSize) {
    this.pageSize = pageSize;
  }

  public PaginatedMultiSalesChannelItemSnapshotResponsePagination pageNumber(Integer pageNumber) {
    this.pageNumber = pageNumber;
    return this;
  }

  /**
   * Get pageNumber
   * @return pageNumber
  */
  
  @Schema(name = "pageNumber", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("pageNumber")
  public Integer getPageNumber() {
    return pageNumber;
  }

  public void setPageNumber(Integer pageNumber) {
    this.pageNumber = pageNumber;
  }

  public PaginatedMultiSalesChannelItemSnapshotResponsePagination totalPages(Integer totalPages) {
    this.totalPages = totalPages;
    return this;
  }

  /**
   * Get totalPages
   * @return totalPages
  */
  
  @Schema(name = "totalPages", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("totalPages")
  public Integer getTotalPages() {
    return totalPages;
  }

  public void setTotalPages(Integer totalPages) {
    this.totalPages = totalPages;
  }

  public PaginatedMultiSalesChannelItemSnapshotResponsePagination totalElements(Integer totalElements) {
    this.totalElements = totalElements;
    return this;
  }

  /**
   * Get totalElements
   * @return totalElements
  */
  
  @Schema(name = "totalElements", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("totalElements")
  public Integer getTotalElements() {
    return totalElements;
  }

  public void setTotalElements(Integer totalElements) {
    this.totalElements = totalElements;
  }

  public PaginatedMultiSalesChannelItemSnapshotResponsePagination last(Boolean last) {
    this.last = last;
    return this;
  }

  /**
   * Get last
   * @return last
  */
  
  @Schema(name = "last", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("last")
  public Boolean getLast() {
    return last;
  }

  public void setLast(Boolean last) {
    this.last = last;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PaginatedMultiSalesChannelItemSnapshotResponsePagination paginatedMultiSalesChannelItemSnapshotResponsePagination = (PaginatedMultiSalesChannelItemSnapshotResponsePagination) o;
    return Objects.equals(this.offset, paginatedMultiSalesChannelItemSnapshotResponsePagination.offset) &&
        Objects.equals(this.pageSize, paginatedMultiSalesChannelItemSnapshotResponsePagination.pageSize) &&
        Objects.equals(this.pageNumber, paginatedMultiSalesChannelItemSnapshotResponsePagination.pageNumber) &&
        Objects.equals(this.totalPages, paginatedMultiSalesChannelItemSnapshotResponsePagination.totalPages) &&
        Objects.equals(this.totalElements, paginatedMultiSalesChannelItemSnapshotResponsePagination.totalElements) &&
        Objects.equals(this.last, paginatedMultiSalesChannelItemSnapshotResponsePagination.last);
  }

  @Override
  public int hashCode() {
    return Objects.hash(offset, pageSize, pageNumber, totalPages, totalElements, last);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PaginatedMultiSalesChannelItemSnapshotResponsePagination {\n");
    sb.append("    offset: ").append(toIndentedString(offset)).append("\n");
    sb.append("    pageSize: ").append(toIndentedString(pageSize)).append("\n");
    sb.append("    pageNumber: ").append(toIndentedString(pageNumber)).append("\n");
    sb.append("    totalPages: ").append(toIndentedString(totalPages)).append("\n");
    sb.append("    totalElements: ").append(toIndentedString(totalElements)).append("\n");
    sb.append("    last: ").append(toIndentedString(last)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

