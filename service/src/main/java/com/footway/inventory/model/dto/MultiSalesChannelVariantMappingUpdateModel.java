package com.footway.inventory.model.dto;

import java.util.Objects;

import com.fasterxml.jackson.annotation.JsonProperty;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.annotation.Generated;

/**
 * MultiSalesChannelVariantMappingUpdateModel
 */

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen", date = "2024-10-07T14:09:25.718732+02:00[Europe/Stockholm]")
public class MultiSalesChannelVariantMappingUpdateModel {

  private String footwayVariantId;

  private String integrationVariantId;

  public MultiSalesChannelVariantMappingUpdateModel footwayVariantId(String footwayVariantId) {
    this.footwayVariantId = footwayVariantId;
    return this;
  }

  /**
   * The Footway variant ID
   * @return footwayVariantId
  */
  
  @Schema(name = "footwayVariantId", description = "The Footway variant ID", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("footwayVariantId")
  public String getFootwayVariantId() {
    return footwayVariantId;
  }

  public void setFootwayVariantId(String footwayVariantId) {
    this.footwayVariantId = footwayVariantId;
  }

  public MultiSalesChannelVariantMappingUpdateModel integrationVariantId(String integrationVariantId) {
    this.integrationVariantId = integrationVariantId;
    return this;
  }

  /**
   * The Integration variant ID
   * @return integrationVariantId
  */
  
  @Schema(name = "integrationVariantId", description = "The Integration variant ID", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("integrationVariantId")
  public String getIntegrationVariantId() {
    return integrationVariantId;
  }

  public void setIntegrationVariantId(String integrationVariantId) {
    this.integrationVariantId = integrationVariantId;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    MultiSalesChannelVariantMappingUpdateModel multiSalesChannelVariantMappingUpdateModel = (MultiSalesChannelVariantMappingUpdateModel) o;
    return Objects.equals(this.footwayVariantId, multiSalesChannelVariantMappingUpdateModel.footwayVariantId) &&
        Objects.equals(this.integrationVariantId, multiSalesChannelVariantMappingUpdateModel.integrationVariantId);
  }

  @Override
  public int hashCode() {
    return Objects.hash(footwayVariantId, integrationVariantId);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class MultiSalesChannelVariantMappingUpdateModel {\n");
    sb.append("    footwayVariantId: ").append(toIndentedString(footwayVariantId)).append("\n");
    sb.append("    integrationVariantId: ").append(toIndentedString(integrationVariantId)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

