package com.footway.inventory.model.dto;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import com.fasterxml.jackson.annotation.JsonProperty;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.annotation.Generated;
import jakarta.validation.Valid;

/**
 * Filters
 */

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen", date = "2025-03-19T09:29:39.345792+01:00[Europe/Stockholm]")
public class Filters {

  @Valid
  private List<String> merchantId;

  @Valid
  private List<String> vendor;

  @Valid
  private List<String> department;

  @Valid
  private List<String> productGroup;

  @Valid
  private List<String> productType;

  @Valid
  private List<String> variantIds;

  private String searchText;

  public Filters merchantId(List<String> merchantId) {
    this.merchantId = merchantId;
    return this;
  }

  public Filters addMerchantIdItem(String merchantIdItem) {
    if (this.merchantId == null) {
      this.merchantId = new ArrayList<>();
    }
    this.merchantId.add(merchantIdItem);
    return this;
  }

  /**
   * Get merchantId
   * @return merchantId
  */
  
  @Schema(name = "merchantId", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("merchantId")
  public List<String> getMerchantId() {
    return merchantId;
  }

  public void setMerchantId(List<String> merchantId) {
    this.merchantId = merchantId;
  }

  public Filters vendor(List<String> vendor) {
    this.vendor = vendor;
    return this;
  }

  public Filters addVendorItem(String vendorItem) {
    if (this.vendor == null) {
      this.vendor = new ArrayList<>();
    }
    this.vendor.add(vendorItem);
    return this;
  }

  /**
   * Get vendor
   * @return vendor
  */
  
  @Schema(name = "vendor", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("vendor")
  public List<String> getVendor() {
    return vendor;
  }

  public void setVendor(List<String> vendor) {
    this.vendor = vendor;
  }

  public Filters department(List<String> department) {
    this.department = department;
    return this;
  }

  public Filters addDepartmentItem(String departmentItem) {
    if (this.department == null) {
      this.department = new ArrayList<>();
    }
    this.department.add(departmentItem);
    return this;
  }

  /**
   * Get department
   * @return department
  */
  
  @Schema(name = "department", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("department")
  public List<String> getDepartment() {
    return department;
  }

  public void setDepartment(List<String> department) {
    this.department = department;
  }

  public Filters productGroup(List<String> productGroup) {
    this.productGroup = productGroup;
    return this;
  }

  public Filters addProductGroupItem(String productGroupItem) {
    if (this.productGroup == null) {
      this.productGroup = new ArrayList<>();
    }
    this.productGroup.add(productGroupItem);
    return this;
  }

  /**
   * Get productGroup
   * @return productGroup
  */
  
  @Schema(name = "productGroup", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("productGroup")
  public List<String> getProductGroup() {
    return productGroup;
  }

  public void setProductGroup(List<String> productGroup) {
    this.productGroup = productGroup;
  }

  public Filters productType(List<String> productType) {
    this.productType = productType;
    return this;
  }

  public Filters addProductTypeItem(String productTypeItem) {
    if (this.productType == null) {
      this.productType = new ArrayList<>();
    }
    this.productType.add(productTypeItem);
    return this;
  }

  /**
   * Get productType
   * @return productType
  */
  
  @Schema(name = "productType", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("productType")
  public List<String> getProductType() {
    return productType;
  }

  public void setProductType(List<String> productType) {
    this.productType = productType;
  }

  public Filters variantIds(List<String> variantIds) {
    this.variantIds = variantIds;
    return this;
  }

  public Filters addVariantIdsItem(String variantIdsItem) {
    if (this.variantIds == null) {
      this.variantIds = new ArrayList<>();
    }
    this.variantIds.add(variantIdsItem);
    return this;
  }

  /**
   * Get variantIds
   * @return variantIds
  */
  
  @Schema(name = "variantIds", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("variantIds")
  public List<String> getVariantIds() {
    return variantIds;
  }

  public void setVariantIds(List<String> variantIds) {
    this.variantIds = variantIds;
  }

  public Filters searchText(String searchText) {
    this.searchText = searchText;
    return this;
  }

  /**
   * Get searchText
   * @return searchText
  */
  
  @Schema(name = "searchText", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("searchText")
  public String getSearchText() {
    return searchText;
  }

  public void setSearchText(String searchText) {
    this.searchText = searchText;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    Filters filters = (Filters) o;
    return Objects.equals(this.merchantId, filters.merchantId) &&
        Objects.equals(this.vendor, filters.vendor) &&
        Objects.equals(this.department, filters.department) &&
        Objects.equals(this.productGroup, filters.productGroup) &&
        Objects.equals(this.productType, filters.productType) &&
        Objects.equals(this.variantIds, filters.variantIds) &&
        Objects.equals(this.searchText, filters.searchText);
  }

  @Override
  public int hashCode() {
    return Objects.hash(merchantId, vendor, department, productGroup, productType, variantIds, searchText);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class Filters {\n");
    sb.append("    merchantId: ").append(toIndentedString(merchantId)).append("\n");
    sb.append("    vendor: ").append(toIndentedString(vendor)).append("\n");
    sb.append("    department: ").append(toIndentedString(department)).append("\n");
    sb.append("    productGroup: ").append(toIndentedString(productGroup)).append("\n");
    sb.append("    productType: ").append(toIndentedString(productType)).append("\n");
    sb.append("    variantIds: ").append(toIndentedString(variantIds)).append("\n");
    sb.append("    searchText: ").append(toIndentedString(searchText)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

