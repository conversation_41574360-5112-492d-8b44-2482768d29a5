package com.footway.inventory.model.dto;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.ArrayList;
import java.util.List;
import jakarta.validation.Valid;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.annotation.Generated;

/**
 * PaginatedMultiSalesChannelMappingsResponse
 */

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen", date = "2025-02-25T14:20:03.206531+01:00[Europe/Stockholm]")
public class PaginatedMultiSalesChannelMappingsResponse {

  @Valid
  private List<@Valid MultiSalesChannelMappingsResponse> items;

  private Integer totalItems;

  private Integer currentPage;

  private Integer totalPages;

  public PaginatedMultiSalesChannelMappingsResponse items(List<@Valid MultiSalesChannelMappingsResponse> items) {
    this.items = items;
    return this;
  }

  public PaginatedMultiSalesChannelMappingsResponse addItemsItem(MultiSalesChannelMappingsResponse itemsItem) {
    if (this.items == null) {
      this.items = new ArrayList<>();
    }
    this.items.add(itemsItem);
    return this;
  }

  /**
   * Get items
   * @return items
  */
  @Valid 
  @Schema(name = "items", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("items")
  public List<@Valid MultiSalesChannelMappingsResponse> getItems() {
    return items;
  }

  public void setItems(List<@Valid MultiSalesChannelMappingsResponse> items) {
    this.items = items;
  }

  public PaginatedMultiSalesChannelMappingsResponse totalItems(Integer totalItems) {
    this.totalItems = totalItems;
    return this;
  }

  /**
   * Total number of items across all pages
   * @return totalItems
  */
  
  @Schema(name = "totalItems", example = "100", description = "Total number of items across all pages", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("totalItems")
  public Integer getTotalItems() {
    return totalItems;
  }

  public void setTotalItems(Integer totalItems) {
    this.totalItems = totalItems;
  }

  public PaginatedMultiSalesChannelMappingsResponse currentPage(Integer currentPage) {
    this.currentPage = currentPage;
    return this;
  }

  /**
   * Current page number
   * @return currentPage
  */
  
  @Schema(name = "currentPage", example = "1", description = "Current page number", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("currentPage")
  public Integer getCurrentPage() {
    return currentPage;
  }

  public void setCurrentPage(Integer currentPage) {
    this.currentPage = currentPage;
  }

  public PaginatedMultiSalesChannelMappingsResponse totalPages(Integer totalPages) {
    this.totalPages = totalPages;
    return this;
  }

  /**
   * Total number of pages
   * @return totalPages
  */
  
  @Schema(name = "totalPages", example = "5", description = "Total number of pages", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("totalPages")
  public Integer getTotalPages() {
    return totalPages;
  }

  public void setTotalPages(Integer totalPages) {
    this.totalPages = totalPages;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PaginatedMultiSalesChannelMappingsResponse paginatedMultiSalesChannelMappingsResponse = (PaginatedMultiSalesChannelMappingsResponse) o;
    return Objects.equals(this.items, paginatedMultiSalesChannelMappingsResponse.items) &&
        Objects.equals(this.totalItems, paginatedMultiSalesChannelMappingsResponse.totalItems) &&
        Objects.equals(this.currentPage, paginatedMultiSalesChannelMappingsResponse.currentPage) &&
        Objects.equals(this.totalPages, paginatedMultiSalesChannelMappingsResponse.totalPages);
  }

  @Override
  public int hashCode() {
    return Objects.hash(items, totalItems, currentPage, totalPages);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PaginatedMultiSalesChannelMappingsResponse {\n");
    sb.append("    items: ").append(toIndentedString(items)).append("\n");
    sb.append("    totalItems: ").append(toIndentedString(totalItems)).append("\n");
    sb.append("    currentPage: ").append(toIndentedString(currentPage)).append("\n");
    sb.append("    totalPages: ").append(toIndentedString(totalPages)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

