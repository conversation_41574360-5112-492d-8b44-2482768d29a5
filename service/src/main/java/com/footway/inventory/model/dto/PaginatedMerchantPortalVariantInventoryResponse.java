package com.footway.inventory.model.dto;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import com.fasterxml.jackson.annotation.JsonProperty;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.annotation.Generated;
import jakarta.validation.Valid;

/**
 * PaginatedMerchantPortalVariantInventoryResponse
 */

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen", date = "2024-09-18T10:54:38.903131+02:00[Europe/Stockholm]")
public class PaginatedMerchantPortalVariantInventoryResponse {

  @Valid
  private List<com.footway.inventory.model.dto.MerchantPortalVariantInventory> items;

  private Integer totalItems;

  private Integer currentPage;

  private Integer totalPages;

  public PaginatedMerchantPortalVariantInventoryResponse items(List<com.footway.inventory.model.dto.MerchantPortalVariantInventory> items) {
    this.items = items;
    return this;
  }

  public PaginatedMerchantPortalVariantInventoryResponse addItemsItem(MerchantPortalVariantInventory itemsItem) {
    if (this.items == null) {
      this.items = new ArrayList<>();
    }
    this.items.add(itemsItem);
    return this;
  }

  /**
   * Get items
   * @return items
  */
  @Valid 
  @Schema(name = "items", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("items")
  public List<com.footway.inventory.model.dto.MerchantPortalVariantInventory> getItems() {
    return items;
  }

  public void setItems(List<com.footway.inventory.model.dto.MerchantPortalVariantInventory> items) {
    this.items = items;
  }

  public PaginatedMerchantPortalVariantInventoryResponse totalItems(Integer totalItems) {
    this.totalItems = totalItems;
    return this;
  }

  /**
   * Get totalItems
   * @return totalItems
  */
  
  @Schema(name = "totalItems", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("totalItems")
  public Integer getTotalItems() {
    return totalItems;
  }

  public void setTotalItems(Integer totalItems) {
    this.totalItems = totalItems;
  }

  public PaginatedMerchantPortalVariantInventoryResponse currentPage(Integer currentPage) {
    this.currentPage = currentPage;
    return this;
  }

  /**
   * Get currentPage
   * @return currentPage
  */
  
  @Schema(name = "currentPage", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("currentPage")
  public Integer getCurrentPage() {
    return currentPage;
  }

  public void setCurrentPage(Integer currentPage) {
    this.currentPage = currentPage;
  }

  public PaginatedMerchantPortalVariantInventoryResponse totalPages(Integer totalPages) {
    this.totalPages = totalPages;
    return this;
  }

  /**
   * Get totalPages
   * @return totalPages
  */
  
  @Schema(name = "totalPages", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("totalPages")
  public Integer getTotalPages() {
    return totalPages;
  }

  public void setTotalPages(Integer totalPages) {
    this.totalPages = totalPages;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PaginatedMerchantPortalVariantInventoryResponse paginatedMerchantPortalVariantInventoryResponse = (PaginatedMerchantPortalVariantInventoryResponse) o;
    return Objects.equals(this.items, paginatedMerchantPortalVariantInventoryResponse.items) &&
        Objects.equals(this.totalItems, paginatedMerchantPortalVariantInventoryResponse.totalItems) &&
        Objects.equals(this.currentPage, paginatedMerchantPortalVariantInventoryResponse.currentPage) &&
        Objects.equals(this.totalPages, paginatedMerchantPortalVariantInventoryResponse.totalPages);
  }

  @Override
  public int hashCode() {
    return Objects.hash(items, totalItems, currentPage, totalPages);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PaginatedMerchantPortalVariantInventoryResponse {\n");
    sb.append("    items: ").append(toIndentedString(items)).append("\n");
    sb.append("    totalItems: ").append(toIndentedString(totalItems)).append("\n");
    sb.append("    currentPage: ").append(toIndentedString(currentPage)).append("\n");
    sb.append("    totalPages: ").append(toIndentedString(totalPages)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

