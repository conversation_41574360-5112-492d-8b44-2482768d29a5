/**
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech) (7.1.0).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
package com.footway.inventory.api;

import java.util.List;
import java.util.Optional;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.context.request.NativeWebRequest;
import com.footway.inventory.model.dto.StorageOrderLine;
import com.footway.inventory.model.dto.StorageOrderLineRequest;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import jakarta.validation.constraints.*;
import jakarta.annotation.Generated;

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen", date = "2025-04-01T14:52:46.621343+02:00[Europe/Stockholm]")
@Validated
@Tag(name = "storageOrder", description = "the storageOrder API")
public interface StorageOrderApi {

    default Optional<NativeWebRequest> getRequest() {
        return Optional.empty();
    }

    /**
     * POST /v1/storageOrderLines : Add a new storage order line
     *
     * @param storageOrderLineRequest  (required)
     * @return Created (status code 201)
     *         or Bad request (status code 400)
     *         or Internal server error (status code 500)
     */
    @Operation(
            operationId = "addStorageOrderLine",
            summary = "Add a new storage order line",
            tags = { "storageOrder" },
            responses = {
                    @ApiResponse(responseCode = "201", description = "Created", content = {
                            @Content(mediaType = "application/json", schema = @Schema(implementation = StorageOrderLine.class))
                    }),
                    @ApiResponse(responseCode = "400", description = "Bad request"),
                    @ApiResponse(responseCode = "500", description = "Internal server error")
            }
    )
    @RequestMapping(
            method = RequestMethod.POST,
            value = "/v1/storageOrderLines",
            produces = { "application/json" },
            consumes = { "application/json" }
    )

    default ResponseEntity<StorageOrderLine> addStorageOrderLine(
            @Parameter(name = "StorageOrderLineRequest", description = "", required = true) @Valid @RequestBody StorageOrderLineRequest storageOrderLineRequest
    ) {
        getRequest().ifPresent(request -> {
            for (MediaType mediaType: MediaType.parseMediaTypes(request.getHeader("Accept"))) {
                if (mediaType.isCompatibleWith(MediaType.valueOf("application/json"))) {
                    String exampleString = "{ \"quantity\" : \"quantity\", \"variant_type\" : \"variant_type\", \"expected_delivery_date\" : \"2000-01-23\", \"country_of_origin\" : \"country_of_origin\", \"length\" : \"length\", \"created_at\" : \"2000-01-23T04:56:07.000+00:00\", \"weight\" : \"weight\", \"storage_order_id\" : \"storage_order_id\", \"merchant_id\" : \"merchant_id\", \"storage_order_line_id\" : 0, \"product_name\" : \"product_name\", \"product_number\" : \"product_number\", \"product_type\" : \"product_type\", \"size\" : \"size\", \"gtin_ean\" : \"gtin_ean\", \"price\" : \"price\", \"width\" : \"width\", \"product_brand\" : \"product_brand\", \"product_group\" : \"product_group\", \"value\" : \"value\", \"shopify_variant_id\" : \"shopify_variant_id\", \"height\" : \"height\", \"status\" : \"status\" }";
                    ApiUtil.setExampleResponse(request, "application/json", exampleString);
                    break;
                }
            }
        });
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);

    }


    /**
     * GET /v1/storageOrderLine/{storage_order_line_id} : Get a storage order line
     *
     * @param storageOrderLineId  (required)
     * @return OK (status code 200)
     *         or Storage order line not found (status code 404)
     *         or Internal server error (status code 500)
     */
    @Operation(
            operationId = "getStorageOrderLine",
            summary = "Get a storage order line",
            tags = { "storageOrder" },
            responses = {
                    @ApiResponse(responseCode = "200", description = "OK", content = {
                            @Content(mediaType = "application/json", schema = @Schema(implementation = StorageOrderLine.class))
                    }),
                    @ApiResponse(responseCode = "404", description = "Storage order line not found"),
                    @ApiResponse(responseCode = "500", description = "Internal server error")
            }
    )
    @RequestMapping(
            method = RequestMethod.GET,
            value = "/v1/storageOrderLine/{storage_order_line_id}",
            produces = { "application/json" }
    )

    default ResponseEntity<StorageOrderLine> getStorageOrderLine(
            @Parameter(name = "storage_order_line_id", description = "", required = true, in = ParameterIn.PATH) @PathVariable("storage_order_line_id") Long storageOrderLineId
    ) {
        getRequest().ifPresent(request -> {
            for (MediaType mediaType: MediaType.parseMediaTypes(request.getHeader("Accept"))) {
                if (mediaType.isCompatibleWith(MediaType.valueOf("application/json"))) {
                    String exampleString = "{ \"quantity\" : \"quantity\", \"variant_type\" : \"variant_type\", \"expected_delivery_date\" : \"2000-01-23\", \"country_of_origin\" : \"country_of_origin\", \"length\" : \"length\", \"created_at\" : \"2000-01-23T04:56:07.000+00:00\", \"weight\" : \"weight\", \"storage_order_id\" : \"storage_order_id\", \"merchant_id\" : \"merchant_id\", \"storage_order_line_id\" : 0, \"product_name\" : \"product_name\", \"product_number\" : \"product_number\", \"product_type\" : \"product_type\", \"size\" : \"size\", \"gtin_ean\" : \"gtin_ean\", \"price\" : \"price\", \"width\" : \"width\", \"product_brand\" : \"product_brand\", \"product_group\" : \"product_group\", \"value\" : \"value\", \"shopify_variant_id\" : \"shopify_variant_id\", \"height\" : \"height\", \"status\" : \"status\" }";
                    ApiUtil.setExampleResponse(request, "application/json", exampleString);
                    break;
                }
            }
        });
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);

    }


    /**
     * GET /v1/storageOrderLines/{storage_order_id} : Get storage order lines for a given storage order
     *
     * @param storageOrderId  (required)
     * @return OK (status code 200)
     *         or Storage order line not found (status code 404)
     *         or Internal server error (status code 500)
     */
    @Operation(
            operationId = "getStorageOrderLines",
            summary = "Get storage order lines for a given storage order",
            tags = { "storageOrder" },
            responses = {
                    @ApiResponse(responseCode = "200", description = "OK", content = {
                            @Content(mediaType = "application/json", array = @ArraySchema(schema = @Schema(implementation = StorageOrderLine.class)))
                    }),
                    @ApiResponse(responseCode = "404", description = "Storage order line not found"),
                    @ApiResponse(responseCode = "500", description = "Internal server error")
            }
    )
    @RequestMapping(
            method = RequestMethod.GET,
            value = "/v1/storageOrderLines/{storage_order_id}",
            produces = { "application/json" }
    )

    default ResponseEntity<List<StorageOrderLine>> getStorageOrderLines(
            @Parameter(name = "storage_order_id", description = "", required = true, in = ParameterIn.PATH) @PathVariable("storage_order_id") Long storageOrderId
    ) {
        getRequest().ifPresent(request -> {
            for (MediaType mediaType: MediaType.parseMediaTypes(request.getHeader("Accept"))) {
                if (mediaType.isCompatibleWith(MediaType.valueOf("application/json"))) {
                    String exampleString = "[ { \"quantity\" : \"quantity\", \"variant_type\" : \"variant_type\", \"expected_delivery_date\" : \"2000-01-23\", \"country_of_origin\" : \"country_of_origin\", \"length\" : \"length\", \"created_at\" : \"2000-01-23T04:56:07.000+00:00\", \"weight\" : \"weight\", \"storage_order_id\" : \"storage_order_id\", \"merchant_id\" : \"merchant_id\", \"storage_order_line_id\" : 0, \"product_name\" : \"product_name\", \"product_number\" : \"product_number\", \"product_type\" : \"product_type\", \"size\" : \"size\", \"gtin_ean\" : \"gtin_ean\", \"price\" : \"price\", \"width\" : \"width\", \"product_brand\" : \"product_brand\", \"product_group\" : \"product_group\", \"value\" : \"value\", \"shopify_variant_id\" : \"shopify_variant_id\", \"height\" : \"height\", \"status\" : \"status\" }, { \"quantity\" : \"quantity\", \"variant_type\" : \"variant_type\", \"expected_delivery_date\" : \"2000-01-23\", \"country_of_origin\" : \"country_of_origin\", \"length\" : \"length\", \"created_at\" : \"2000-01-23T04:56:07.000+00:00\", \"weight\" : \"weight\", \"storage_order_id\" : \"storage_order_id\", \"merchant_id\" : \"merchant_id\", \"storage_order_line_id\" : 0, \"product_name\" : \"product_name\", \"product_number\" : \"product_number\", \"product_type\" : \"product_type\", \"size\" : \"size\", \"gtin_ean\" : \"gtin_ean\", \"price\" : \"price\", \"width\" : \"width\", \"product_brand\" : \"product_brand\", \"product_group\" : \"product_group\", \"value\" : \"value\", \"shopify_variant_id\" : \"shopify_variant_id\", \"height\" : \"height\", \"status\" : \"status\" } ]";
                    ApiUtil.setExampleResponse(request, "application/json", exampleString);
                    break;
                }
            }
        });
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);

    }


    /**
     * GET /v1/storageOrderLines/suggest : Get suggested storage order line
     *
     * @param merchantId The merchant ID (required)
     * @param ean The EAN/GTIN code (required)
     * @return OK (status code 200)
     *         or Bad request (status code 400)
     *         or Internal server error (status code 500)
     */
    @Operation(
            operationId = "getSuggestedStorageOrderLine",
            summary = "Get suggested storage order line",
            tags = { "storageOrder" },
            responses = {
                    @ApiResponse(responseCode = "200", description = "OK", content = {
                            @Content(mediaType = "application/json", schema = @Schema(implementation = StorageOrderLine.class))
                    }),
                    @ApiResponse(responseCode = "400", description = "Bad request"),
                    @ApiResponse(responseCode = "500", description = "Internal server error")
            }
    )
    @RequestMapping(
            method = RequestMethod.GET,
            value = "/v1/storageOrderLines/suggest",
            produces = { "application/json" }
    )

    default ResponseEntity<StorageOrderLine> getSuggestedStorageOrderLine(
            @NotNull @Parameter(name = "merchantId", description = "The merchant ID", required = true, in = ParameterIn.QUERY) @Valid @RequestParam(value = "merchantId", required = true) String merchantId,
            @NotNull @Parameter(name = "ean", description = "The EAN/GTIN code", required = true, in = ParameterIn.QUERY) @Valid @RequestParam(value = "ean", required = true) String ean
    ) {
        getRequest().ifPresent(request -> {
            for (MediaType mediaType: MediaType.parseMediaTypes(request.getHeader("Accept"))) {
                if (mediaType.isCompatibleWith(MediaType.valueOf("application/json"))) {
                    String exampleString = "{ \"quantity\" : \"quantity\", \"variant_type\" : \"variant_type\", \"expected_delivery_date\" : \"2000-01-23\", \"country_of_origin\" : \"country_of_origin\", \"length\" : \"length\", \"created_at\" : \"2000-01-23T04:56:07.000+00:00\", \"weight\" : \"weight\", \"storage_order_id\" : \"storage_order_id\", \"merchant_id\" : \"merchant_id\", \"storage_order_line_id\" : 0, \"product_name\" : \"product_name\", \"product_number\" : \"product_number\", \"product_type\" : \"product_type\", \"size\" : \"size\", \"gtin_ean\" : \"gtin_ean\", \"price\" : \"price\", \"width\" : \"width\", \"product_brand\" : \"product_brand\", \"product_group\" : \"product_group\", \"value\" : \"value\", \"shopify_variant_id\" : \"shopify_variant_id\", \"height\" : \"height\", \"status\" : \"status\" }";
                    ApiUtil.setExampleResponse(request, "application/json", exampleString);
                    break;
                }
            }
        });
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);

    }


    /**
     * GET /v1/storageOrderLines/getVariantInfoByEan : Get variant information by ean for storage order line
     *
     * @param merchantId The merchant ID (required)
     * @param ean The EAN/GTIN code (required)
     * @return OK (status code 200)
     *         or Bad request (status code 400)
     *         or Internal server error (status code 500)
     */
    @Operation(
            operationId = "getVariantInfoByEanForStorageOrderLine",
            summary = "Get variant information by ean for storage order line",
            tags = { "storageOrder" },
            responses = {
                    @ApiResponse(responseCode = "200", description = "OK", content = {
                            @Content(mediaType = "application/json", schema = @Schema(implementation = StorageOrderLine.class))
                    }),
                    @ApiResponse(responseCode = "400", description = "Bad request"),
                    @ApiResponse(responseCode = "500", description = "Internal server error")
            }
    )
    @RequestMapping(
            method = RequestMethod.GET,
            value = "/v1/storageOrderLines/getVariantInfoByEan",
            produces = { "application/json" }
    )

    default ResponseEntity<StorageOrderLine> getVariantInfoByEanForStorageOrderLine(
            @NotNull @Parameter(name = "merchantId", description = "The merchant ID", required = true, in = ParameterIn.QUERY) @Valid @RequestParam(value = "merchantId", required = true) String merchantId,
            @NotNull @Parameter(name = "ean", description = "The EAN/GTIN code", required = true, in = ParameterIn.QUERY) @Valid @RequestParam(value = "ean", required = true) String ean
    ) {
        getRequest().ifPresent(request -> {
            for (MediaType mediaType: MediaType.parseMediaTypes(request.getHeader("Accept"))) {
                if (mediaType.isCompatibleWith(MediaType.valueOf("application/json"))) {
                    String exampleString = "{ \"quantity\" : \"quantity\", \"variant_type\" : \"variant_type\", \"expected_delivery_date\" : \"2000-01-23\", \"country_of_origin\" : \"country_of_origin\", \"length\" : \"length\", \"created_at\" : \"2000-01-23T04:56:07.000+00:00\", \"weight\" : \"weight\", \"storage_order_id\" : \"storage_order_id\", \"merchant_id\" : \"merchant_id\", \"storage_order_line_id\" : 0, \"product_name\" : \"product_name\", \"product_number\" : \"product_number\", \"product_type\" : \"product_type\", \"size\" : \"size\", \"gtin_ean\" : \"gtin_ean\", \"price\" : \"price\", \"width\" : \"width\", \"product_brand\" : \"product_brand\", \"product_group\" : \"product_group\", \"value\" : \"value\", \"shopify_variant_id\" : \"shopify_variant_id\", \"height\" : \"height\", \"status\" : \"status\" }";
                    ApiUtil.setExampleResponse(request, "application/json", exampleString);
                    break;
                }
            }
        });
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);

    }


    /**
     * DELETE /v1/storageOrderLine/{storage_order_line_id} : Remove a storage order line
     *
     * @param storageOrderLineId  (required)
     * @return No content (status code 204)
     *         or Storage order line not found (status code 404)
     *         or Internal server error (status code 500)
     */
    @Operation(
            operationId = "removeStorageOrderLine",
            summary = "Remove a storage order line",
            tags = { "storageOrder" },
            responses = {
                    @ApiResponse(responseCode = "204", description = "No content"),
                    @ApiResponse(responseCode = "404", description = "Storage order line not found"),
                    @ApiResponse(responseCode = "500", description = "Internal server error")
            }
    )
    @RequestMapping(
            method = RequestMethod.DELETE,
            value = "/v1/storageOrderLine/{storage_order_line_id}"
    )

    default ResponseEntity<Void> removeStorageOrderLine(
            @Parameter(name = "storage_order_line_id", description = "", required = true, in = ParameterIn.PATH) @PathVariable("storage_order_line_id") Long storageOrderLineId
    ) {
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);

    }


    /**
     * DELETE /v1/storageOrderLines/{storage_order_id} : Remove storage order lines for a given storage order
     *
     * @param storageOrderId  (required)
     * @return No content (status code 204)
     *         or Storage order lines not found (status code 404)
     *         or Internal server error (status code 500)
     */
    @Operation(
            operationId = "removeStorageOrderLines",
            summary = "Remove storage order lines for a given storage order",
            tags = { "storageOrder" },
            responses = {
                    @ApiResponse(responseCode = "204", description = "No content"),
                    @ApiResponse(responseCode = "404", description = "Storage order lines not found"),
                    @ApiResponse(responseCode = "500", description = "Internal server error")
            }
    )
    @RequestMapping(
            method = RequestMethod.DELETE,
            value = "/v1/storageOrderLines/{storage_order_id}"
    )

    default ResponseEntity<Void> removeStorageOrderLines(
            @Parameter(name = "storage_order_id", description = "", required = true, in = ParameterIn.PATH) @PathVariable("storage_order_id") Long storageOrderId
    ) {
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);

    }


    /**
     * PUT /v1/storageOrderLine/{storage_order_line_id} : Update a storage order line
     *
     * @param storageOrderLineId  (required)
     * @param storageOrderLineRequest  (required)
     * @return OK (status code 200)
     *         or Storage order line not found (status code 404)
     *         or Internal server error (status code 500)
     */
    @Operation(
            operationId = "updateStorageOrderLine",
            summary = "Update a storage order line",
            tags = { "storageOrder" },
            responses = {
                    @ApiResponse(responseCode = "200", description = "OK", content = {
                            @Content(mediaType = "application/json", schema = @Schema(implementation = StorageOrderLine.class))
                    }),
                    @ApiResponse(responseCode = "404", description = "Storage order line not found"),
                    @ApiResponse(responseCode = "500", description = "Internal server error")
            }
    )
    @RequestMapping(
            method = RequestMethod.PUT,
            value = "/v1/storageOrderLine/{storage_order_line_id}",
            produces = { "application/json" },
            consumes = { "application/json" }
    )

    default ResponseEntity<StorageOrderLine> updateStorageOrderLine(
            @Parameter(name = "storage_order_line_id", description = "", required = true, in = ParameterIn.PATH) @PathVariable("storage_order_line_id") Long storageOrderLineId,
            @Parameter(name = "StorageOrderLineRequest", description = "", required = true) @Valid @RequestBody StorageOrderLineRequest storageOrderLineRequest
    ) {
        getRequest().ifPresent(request -> {
            for (MediaType mediaType: MediaType.parseMediaTypes(request.getHeader("Accept"))) {
                if (mediaType.isCompatibleWith(MediaType.valueOf("application/json"))) {
                    String exampleString = "{ \"quantity\" : \"quantity\", \"variant_type\" : \"variant_type\", \"expected_delivery_date\" : \"2000-01-23\", \"country_of_origin\" : \"country_of_origin\", \"length\" : \"length\", \"created_at\" : \"2000-01-23T04:56:07.000+00:00\", \"weight\" : \"weight\", \"storage_order_id\" : \"storage_order_id\", \"merchant_id\" : \"merchant_id\", \"storage_order_line_id\" : 0, \"product_name\" : \"product_name\", \"product_number\" : \"product_number\", \"product_type\" : \"product_type\", \"size\" : \"size\", \"gtin_ean\" : \"gtin_ean\", \"price\" : \"price\", \"width\" : \"width\", \"product_brand\" : \"product_brand\", \"product_group\" : \"product_group\", \"value\" : \"value\", \"shopify_variant_id\" : \"shopify_variant_id\", \"height\" : \"height\", \"status\" : \"status\" }";
                    ApiUtil.setExampleResponse(request, "application/json", exampleString);
                    break;
                }
            }
        });
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);

    }


    /**
     * PUT /v1/storageOrderLines/{storage_order_id} : Update storage order lines for a given storage order
     *
     * @param storageOrderId  (required)
     * @param storageOrderLineRequest  (required)
     * @return OK (status code 200)
     *         or Storage order line not found (status code 404)
     *         or Internal server error (status code 500)
     */
    @Operation(
            operationId = "updateStorageOrderLines",
            summary = "Update storage order lines for a given storage order",
            tags = { "storageOrder" },
            responses = {
                    @ApiResponse(responseCode = "200", description = "OK", content = {
                            @Content(mediaType = "application/json", array = @ArraySchema(schema = @Schema(implementation = StorageOrderLine.class)))
                    }),
                    @ApiResponse(responseCode = "404", description = "Storage order line not found"),
                    @ApiResponse(responseCode = "500", description = "Internal server error")
            }
    )
    @RequestMapping(
            method = RequestMethod.PUT,
            value = "/v1/storageOrderLines/{storage_order_id}",
            produces = { "application/json" },
            consumes = { "application/json" }
    )

    default ResponseEntity<List<StorageOrderLine>> updateStorageOrderLines(
            @Parameter(name = "storage_order_id", description = "", required = true, in = ParameterIn.PATH) @PathVariable("storage_order_id") Long storageOrderId,
            @Parameter(name = "StorageOrderLineRequest", description = "", required = true) @Valid @RequestBody List<StorageOrderLineRequest> storageOrderLineRequest
    ) {
        getRequest().ifPresent(request -> {
            for (MediaType mediaType: MediaType.parseMediaTypes(request.getHeader("Accept"))) {
                if (mediaType.isCompatibleWith(MediaType.valueOf("application/json"))) {
                    String exampleString = "[ { \"quantity\" : \"quantity\", \"variant_type\" : \"variant_type\", \"expected_delivery_date\" : \"2000-01-23\", \"country_of_origin\" : \"country_of_origin\", \"length\" : \"length\", \"created_at\" : \"2000-01-23T04:56:07.000+00:00\", \"weight\" : \"weight\", \"storage_order_id\" : \"storage_order_id\", \"merchant_id\" : \"merchant_id\", \"storage_order_line_id\" : 0, \"product_name\" : \"product_name\", \"product_number\" : \"product_number\", \"product_type\" : \"product_type\", \"size\" : \"size\", \"gtin_ean\" : \"gtin_ean\", \"price\" : \"price\", \"width\" : \"width\", \"product_brand\" : \"product_brand\", \"product_group\" : \"product_group\", \"value\" : \"value\", \"shopify_variant_id\" : \"shopify_variant_id\", \"height\" : \"height\", \"status\" : \"status\" }, { \"quantity\" : \"quantity\", \"variant_type\" : \"variant_type\", \"expected_delivery_date\" : \"2000-01-23\", \"country_of_origin\" : \"country_of_origin\", \"length\" : \"length\", \"created_at\" : \"2000-01-23T04:56:07.000+00:00\", \"weight\" : \"weight\", \"storage_order_id\" : \"storage_order_id\", \"merchant_id\" : \"merchant_id\", \"storage_order_line_id\" : 0, \"product_name\" : \"product_name\", \"product_number\" : \"product_number\", \"product_type\" : \"product_type\", \"size\" : \"size\", \"gtin_ean\" : \"gtin_ean\", \"price\" : \"price\", \"width\" : \"width\", \"product_brand\" : \"product_brand\", \"product_group\" : \"product_group\", \"value\" : \"value\", \"shopify_variant_id\" : \"shopify_variant_id\", \"height\" : \"height\", \"status\" : \"status\" } ]";
                    ApiUtil.setExampleResponse(request, "application/json", exampleString);
                    break;
                }
            }
        });
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);

    }

}
