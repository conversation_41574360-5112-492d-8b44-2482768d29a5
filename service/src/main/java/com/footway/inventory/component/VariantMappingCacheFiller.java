package com.footway.inventory.component;

import com.footway.fop.shared.logging.LogContext;
import com.footway.inventory.model.dao.VariantMappingTable;
import com.footway.inventory.repository.jpa.VariantMappingRepository;
import com.github.benmanes.caffeine.cache.Caffeine;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.cache.caffeine.CaffeineCacheManager;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Stream;

@Slf4j
@Component
public class VariantMappingCacheFiller {
    @Getter
    private final String cacheName = "variantMappingByMerchantAndFootwayVariant";
    @Autowired
    private CacheManager cacheManager;
    @Autowired
    private VariantMappingRepository variantMappingRepository;
    private final AtomicInteger count = new AtomicInteger(0);

    @Transactional(readOnly = true, transactionManager = "inventoryTransactionManager")
    public void prefillVariantMappingCache(String instanceId) {
        LogContext.forBlock(() -> {
            log.info("Warming up cache {} for instance {}", cacheName, instanceId);
            CaffeineCacheManager caffeine = (CaffeineCacheManager) cacheManager;

            caffeine.registerCustomCache(cacheName, Caffeine.newBuilder().maximumSize(100000).build());
            Cache cache = cacheManager.getCache(cacheName);

            if (cache != null) {
                // Reset counter
                count.set(0);

                // Use streaming to process records in a memory-efficient way
                try (Stream<VariantMappingTable> mappingStream = variantMappingRepository.findAllBy()) {
                    mappingStream.forEach(variantMapping -> {
                        String cacheKey = variantMapping.getMerchantId() + ":" + variantMapping.getFootwayVariantId();
                        cache.put(cacheKey, variantMapping);

                        // Use atomic counter for thread safety
                        int currentCount = count.incrementAndGet();

                        // Log progress every 10,000 records
                        if (currentCount % 10000 == 0) {
                            log.info("Cached {} variant mappings so far", currentCount);
                        }
                    });
                } // Stream is automatically closed here
            } else {
                log.error("Cache {} not found for instance {}", cacheName, instanceId);
            }

            log.info("Finished warming cache ({}) {} for instance {}", count.get(), cacheName, instanceId);
        }, "instanceId", instanceId);
    }
}

