package com.footway.inventory.component;

import com.footway.fop.shared.webservice.component.SpringLifecycleHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.CacheManager;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.concurrent.ThreadLocalRandom;

@Slf4j
@Component
public class CacheFiller {
    private final SpringLifecycleHandler lifeCycleHandler;

    @Autowired
    private CacheManager cacheManager;

    @Autowired
    private InventoryCacheFiller inventoryCacheFiller;
    @Autowired
    private VariantMappingCacheFiller variantMappingCacheFiller;

    public CacheFiller(SpringLifecycleHandler lifecycleHandler) {
        this.lifeCycleHandler = lifecycleHandler;
    }

    @Transactional(readOnly = true, transactionManager = "inventoryTransactionManager")
//    @EventListener(ApplicationReadyEvent.class)
    public void prefillCaches() {
        randomDelayUpToMinutes(1, variantMappingCacheFiller.getCacheName()); // Introduce a random delay of up to 5 minutes
        variantMappingCacheFiller.prefillVariantMappingCache(lifeCycleHandler.getShortInstanceId());
        randomDelayUpToMinutes(4, inventoryCacheFiller.getCacheName()); // Introduce a random delay of up to 5 minutes
        inventoryCacheFiller.prefillInventoryLevelCache(lifeCycleHandler.getShortInstanceId());
    }

    private void randomDelayUpToMinutes(int maxMinutes, String cacheName) {
        int delayInSeconds = ThreadLocalRandom.current().nextInt(0, maxMinutes * 60 + 1); // Random delay in seconds
        try {
            log.info("Warming up cache {} delayed {} seconds before prefill.", cacheName, delayInSeconds);
            Thread.sleep(delayInSeconds * 1000L); // Convert seconds to milliseconds for Thread.sleep
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt(); // Restore the interrupt status
            log.warn("Random delay interrupted", e);
        }
    }
}

