package com.footway.inventory.component;

import com.footway.fop.shared.logging.LogContext;
import com.footway.inventory.model.dao.InventoryLevel;
import com.footway.inventory.repository.jpa.InventoryLevelRepository;
import com.github.benmanes.caffeine.cache.Caffeine;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.cache.caffeine.CaffeineCacheManager;
import org.springframework.stereotype.Component;

import java.util.stream.Stream;

@Slf4j
@Component
public class InventoryCacheFiller {
    @Getter
    private final String cacheName = "getInventoryLevel";

    @Autowired
    private CacheManager cacheManager;

    @Autowired
    private InventoryLevelRepository inventoryLevelRepository;

    private Integer count = 0;

    public void prefillInventoryLevelCache(String instanceId) {
        LogContext.forBlock(() -> {
            log.info("Warming up cache {} for instance {}", cacheName, instanceId);
            CaffeineCacheManager caffeine = (CaffeineCacheManager) cacheManager;

            //Either load part of what is needed and update using new repository query OR separate into two tables

            caffeine.registerCustomCache(cacheName, Caffeine.newBuilder().maximumSize(500000).build());
            Cache cache = cacheManager.getCache(cacheName);

            if (cache != null) {
                Stream<InventoryLevel> stream = inventoryLevelRepository.streamAll();
                stream.forEach(inventoryLevel -> {
                    String cacheKey = inventoryLevel.getMerchantId() + ":" + inventoryLevel.getVariantId();
                    cache.put(cacheKey, inventoryLevel);
                    count++;
                });
            } else {
                log.error("Cache {} not found for instance {}", cacheName, instanceId);
            }

            log.info("Finished warming cache ({}) {} for instance {}", count, cacheName, instanceId);
        }, "instanceId", instanceId);
    }
}

