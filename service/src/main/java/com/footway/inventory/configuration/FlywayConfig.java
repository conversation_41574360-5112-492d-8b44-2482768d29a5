package com.footway.inventory.configuration;

import com.footway.inventory.factory.ConnectorConnectionPoolFactory;
import org.flywaydb.core.Flyway;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;
import javax.sql.DataSource;

@Configuration
public class FlywayConfig {
    private final ConnectorConnectionPoolFactory connectorConnectionPoolFactory;

    public FlywayConfig(ConnectorConnectionPoolFactory connectorConnectionPoolFactory) {
        this.connectorConnectionPoolFactory = connectorConnectionPoolFactory;
    }

    @Bean
    @DependsOn("inventoryRoutingDataSource")
    public Flyway inventoryFlyway() {
        try {
            // Use only master datasource for Flyway
            DataSource masterDataSource = connectorConnectionPoolFactory.createConnectionPool(false);
            if (masterDataSource == null) {
                throw new IllegalStateException("Master datasource is null");
            }

            Flyway flyway = Flyway.configure()
                    .dataSource(masterDataSource)
                    .locations("classpath:inventory/db/migration") // Add classpath: prefix
                    .baselineOnMigrate(true) // Add this if you're applying Flyway to an existing DB
                    .load();
            flyway.migrate();
            return flyway;
        } catch (Exception e) {
            throw new RuntimeException("Failed to initialize Flyway: ", e);
        }
    }
}
