package com.footway.inventory.configuration;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Getter
@Setter
@Configuration
@ConfigurationProperties(prefix = "bo-public-api")
public class BoPublicAPIConfig {
    private String url;
    private String username;
    private String password;
}
