package com.footway.inventory.service;

import com.footway.inventory.exception.UnknownTagException;
import com.footway.inventory.model.dao.ProductCategory;
import com.footway.inventory.model.dto.ProductCategoryResponse;
import com.footway.inventory.model.dto.PaginatedProductCategoryResponse;
import com.footway.inventory.repository.jpa.ProductCategoryRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.web.server.ResponseStatusException;
import org.springframework.http.HttpStatus;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class ProductCategoryService {

    private final ProductCategoryRepository productCategoryRepository;

    /**
     * Get all strict names for a category type
     */
    public List<String> getAllStrictNamesByCategoryType(String categoryType) {
        return productCategoryRepository.findAllStrictNamesByCategoryType(categoryType);
    }

    /**
     * Add a new source key for an existing strict name
     */
    public ProductCategoryResponse addSourceKeyByCategoryTypeAndStrictName(String categoryType, String strictName, String sourceKey) {
        if (productCategoryRepository.existsByCategoryTypeAndSourceKey(categoryType, sourceKey)) {
            throw new IllegalArgumentException("Source key already exists: " + sourceKey);
        }

        ProductCategory category = ProductCategory.builder()
                .categoryType(categoryType)
                .strictName(strictName)
                .sourceKey(sourceKey)
                .build();

        ProductCategory savedCategory = productCategoryRepository.save(category);
        return convertToDto(savedCategory);
    }

    /**
     * Find a category by its source key
     */
    public ProductCategory fromSourceKey(String categoryType, String key) {
        return productCategoryRepository.findByCategoryTypeAndSourceKey(categoryType, key.toLowerCase())
                .orElseThrow(() -> new UnknownTagException("Could not find key " + key + " for " + categoryType));
    }

    /**
     * Find multiple categories from a collection of source keys
     */
    public List<ProductCategory> fromSourceKeys(String categoryType, Collection<String> keys) {
        List<ProductCategory> categories = new ArrayList<>();
        if (keys != null) {
            for (String key : keys) {
                categories.add(fromSourceKey(categoryType, key));
            }
        }
        return categories;
    }

    /**
     * Extract strict names from a collection of source keys
     */
    public List<String> textFromSourceKeys(String categoryType, Collection<String> keys) {
        return fromSourceKeys(categoryType, keys).stream()
                .map(ProductCategory::getStrictName)
                .collect(Collectors.toList());
    }

    /**
     * Delete a source key
     */
    public void deleteSourceKeyByCategoryTypeAndSourceKey(String categoryType, String sourceKey) {
        ProductCategory category = fromSourceKey(categoryType, sourceKey);
        productCategoryRepository.delete(category);
    }

    /**
     * Get status information
     */
    public String status(String categoryType) {
        long count = productCategoryRepository.findByCategoryType(categoryType).size();
        return categoryType + "(" + count + ")";
    }

    /**
     * Convert a ProductCategory entity to a ProductCategoryResponse DTO
     */
    private ProductCategoryResponse convertToDto(ProductCategory entity) {
        ProductCategoryResponse response = new ProductCategoryResponse();
        response.setId(entity.getId());
        response.setCategoryType(entity.getCategoryType());
        response.setStrictName(entity.getStrictName());
        response.setSourceKey(entity.getSourceKey());

        response.setCreatedAt(entity.getCreated() != null ? entity.getCreated().atOffset(ZoneOffset.UTC) : null);
        response.setUpdatedAt(entity.getUpdated() != null ? entity.getUpdated().atOffset(ZoneOffset.UTC) : null);
        return response;
    }

    /**
     * Get all product categories with pagination
     */
    public PaginatedProductCategoryResponse getAllProductCategories(Pageable pageable) {
        Page<ProductCategory> page = productCategoryRepository.findAll(pageable);
        
        List<ProductCategoryResponse> items = page.getContent().stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
        
        PaginatedProductCategoryResponse response = new PaginatedProductCategoryResponse();
        response.setItems(items);
        response.setTotalItems((int) page.getTotalElements());
        response.setCurrentPage(page.getNumber());
        response.setTotalPages(page.getTotalPages());
        
        return response;
    }

    /**
     * Get a product category by ID
     */
    public ProductCategoryResponse getProductCategoryById(Long id) {
        return productCategoryRepository.findById(id)
                .map(this::convertToDto)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Product category not found with id " + id));
    }

    /**
     * Update a product category
     */
    public ProductCategoryResponse updateProductCategory(Long id, String strictName, String sourceKey) {
        ProductCategory category = productCategoryRepository.findById(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Product category not found with id " + id));
        
        category.setStrictName(strictName);
        category.setSourceKey(sourceKey);
        
        ProductCategory updatedCategory = productCategoryRepository.save(category);
        return convertToDto(updatedCategory);
    }

    /**
     * Get all product categories by type with pagination
     */
    public PaginatedProductCategoryResponse getAllProductCategoriesByType(String categoryType, Pageable pageable) {
        Page<ProductCategory> page = productCategoryRepository.findByCategoryType(categoryType, pageable);
        
        List<ProductCategoryResponse> items = page.getContent().stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
        
        PaginatedProductCategoryResponse response = new PaginatedProductCategoryResponse();
        response.setItems(items);
        response.setTotalItems((int) page.getTotalElements());
        response.setCurrentPage(page.getNumber());
        response.setTotalPages(page.getTotalPages());
        
        return response;
    }
}