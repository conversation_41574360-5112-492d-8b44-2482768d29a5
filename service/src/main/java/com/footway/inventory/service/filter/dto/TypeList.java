package com.footway.inventory.service.filter.dto;

import com.footway.inventory.service.filter.FilterType;

import java.util.ArrayList;


/**
 * Contains all relevant CountPerGroup for a specific filter type and value.
 */
public class TypeList extends ArrayList<CountPerGroup> {
    private final String value;
    private final FilterType type;

    public TypeList(FilterType type, String value) {
        this.type = type;
        this.value = value;
    }

    public Integer count(FilterMap filters) {
        int totalCount = 0;
        for (CountPerGroup countPerGroup : this) {
            if(filters.matches(countPerGroup)) {
                totalCount += countPerGroup.getCount();
            }
        }
        return totalCount;
    }
}
