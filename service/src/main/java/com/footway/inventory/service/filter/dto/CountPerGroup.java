package com.footway.inventory.service.filter.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

@ToString
@Data
@AllArgsConstructor
@EqualsAndHashCode
public class CountPerGroup {
    private String merchantId;
    private String vendor;
    private List<String> department;
    private List<String> productGroup;
    private List<String> productType;
    private Integer count;

    // necessary for conversion from Long to int during repository query
    public CountPerGroup(String merchantId, String vendor, List<String> department, List<String> productGroup, List<String> productType, Long count) {
        this.merchantId = merchantId;
        this.vendor = vendor;
        this.department = department;
        this.productGroup = productGroup;
        this.productType = productType;
        this.count = count.intValue();
    }
}
