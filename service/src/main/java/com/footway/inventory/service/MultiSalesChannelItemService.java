package com.footway.inventory.service;

import com.footway.inventory.mapper.MultiSalesChannelItemMapper;
import com.footway.inventory.model.dao.MultiSalesChannelItem;
import com.footway.inventory.model.dao.VariantTable;
import com.footway.inventory.model.dto.*;
import com.footway.inventory.repository.jpa.InventoryLevelRepository;
import com.footway.inventory.repository.jpa.MultiSalesChannelItemRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.server.ResponseStatusException;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@Service
public class MultiSalesChannelItemService {
    private final MultiSalesChannelItemRepository multiSalesChannelItemRepository;
    private final MultiSalesChannelItemMapper multiSalesChannelItemMapper;
    private final InternalVariantService internalVariantService;
    private final InventoryLevelRepository inventoryLevelRepository;

    public MultiSalesChannelItemService(MultiSalesChannelItemRepository multiSalesChannelItemRepository, MultiSalesChannelItemMapper multiSalesChannelItemMapper, InternalVariantService internalVariantService, InventoryLevelRepository inventoryLevelRepository) {
        this.multiSalesChannelItemRepository = multiSalesChannelItemRepository;
        this.multiSalesChannelItemMapper = multiSalesChannelItemMapper;
        this.internalVariantService = internalVariantService;
        this.inventoryLevelRepository = inventoryLevelRepository;
    }

    public MultiSalesChannelItem createMultiSalesChannelItem(MultiSalesChannelItemRequest request) {
        MultiSalesChannelItem item = multiSalesChannelItemMapper.requestToEntity(request);
        return multiSalesChannelItemRepository.save(item);
    }

    public MultiSalesChannelItem getMultiSalesChannelItem(Long id) {
        return multiSalesChannelItemRepository.findById(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "ConsignmentItem not found"));
    }

    public List<MultiSalesChannelItem> getAllMultiSalesChannelItems() {
        return multiSalesChannelItemRepository.findAll();
    }

    public MultiSalesChannelItem updatemultiSalesChannelItem(Long id, MultiSalesChannelItemRequest request) {
        MultiSalesChannelItem existingItem = getMultiSalesChannelItem(id);
        multiSalesChannelItemMapper.updateEntityFromRequest(request, existingItem);
        return multiSalesChannelItemRepository.save(existingItem);
    }

    public void deleteMultiSalesChannelItem(Long id) {
        multiSalesChannelItemRepository.deleteById(id);
    }

    public List<MultiSalesChannelItem> findByMerchantCodeAndPratVariantId(String merchantCode, String pratVariantId) {
        return multiSalesChannelItemRepository.findByMerchantCodeAndPratVariantId(merchantCode, pratVariantId);
    }

    public List<MultiSalesChannelItem> findByMerchantCode(String merchantCode) {
        return multiSalesChannelItemRepository.findByMerchantCode(merchantCode);
    }

    public List<MultiSalesChannelItem> findByPratVariantId(String pratVariantId) {
        return multiSalesChannelItemRepository.findByPratVariantId(pratVariantId);
    }

    public List<MultiSalesChannelItemSnapshotDto> queryMultiSalesChannelItemsWithInventorySnapshotByMerchant(String merchantCode) {
        return multiSalesChannelItemRepository.queryMultiSalesChannelItemsWithInventorySnapshotByMerchant(merchantCode);
    }

    public PaginatedMultiSalesChannelItemSnapshotResponse getMultiSalesChannelItemsWithInventorySnapshot(int page, int pageSize) {
        Page<MultiSalesChannelItemSnapshotDto> itemsPage = multiSalesChannelItemRepository.getPaginatedMultiSalesChannelItemsWithInventorySnapshot(PageRequest.of(page - 1, pageSize));
        List<MultiSalesChannelItemSnapshotResponse> items = itemsPage.getContent().stream().map(this::convertToDTO).collect(Collectors.toList());

        PaginatedMultiSalesChannelItemSnapshotResponse response = new PaginatedMultiSalesChannelItemSnapshotResponse();
        response.setItems(items);
        response.setTotalItems((int) itemsPage.getTotalElements());
        response.setCurrentPage(itemsPage.getNumber() + 1);
        response.setTotalPages(itemsPage.getTotalPages());
        return response;
    }

    private MultiSalesChannelItemSnapshotResponse convertToDTO(MultiSalesChannelItemSnapshotDto item) {
        if (item == null) {
            return null;
        }
        MultiSalesChannelItemSnapshotResponse itemSnapshotResponse = new MultiSalesChannelItemSnapshotResponse();
        itemSnapshotResponse.setMultiSalesChannelItemId(item.getMultiSalesChannelItemId());
//        itemSnapshotResponse.setMerchantCode(item.getMerchantCode());  //FIXME: missing 1:1 mapping
        itemSnapshotResponse.setVariantId(item.getVariantId());
        itemSnapshotResponse.setDisplayName(item.getDisplayName());
        itemSnapshotResponse.setSku(item.getSku());
        itemSnapshotResponse.setEan(item.getEan().stream().findFirst().orElse(null)); //FIXME: need to get entire array
//        itemSnapshotResponse.setSnapshotDate(item.getSnapshotDate()); //FIXME: missing 1:1 mapping
        itemSnapshotResponse.setUpdated(item.getUpdated());
        itemSnapshotResponse.setCreated(item.getCreated());
        itemSnapshotResponse.setMultiSalesChannelMetadata(item.getMultiSalesChannelMetadata());
        itemSnapshotResponse.setSnapshotMetadata(item.getSnapshotMetadata());
        return itemSnapshotResponse;
    }


    public MultiSalesChannelItem upsertMultiSalesChannelItem(MultiSalesChannelItemRequest request) {
        List<MultiSalesChannelItem> existingItems = multiSalesChannelItemRepository
                .findByMerchantCodeAndPratVariantId(request.getMerchantCode(), request.getPratVariantId());

        if (!existingItems.isEmpty()) {
            MultiSalesChannelItem item = existingItems.get(0);
            multiSalesChannelItemMapper.updateEntityFromRequest(request, item);
            return multiSalesChannelItemRepository.save(item);
        } else {
            MultiSalesChannelItem newItem = multiSalesChannelItemMapper.requestToEntity(request);
            return multiSalesChannelItemRepository.save(newItem);
        }
    }

    public MultiSalesChannelPriceResponse getMultiSalesVariantPrice(String owner, String pratVariantId) {
        Optional<VariantTable> variant = internalVariantService.getVariant(pratVariantId);
        if (variant.isEmpty()) {
            return null;
        } else {
            VariantTable variantData = variant.get();
            String defaultPrice = variantData.getPrice();
            MultiSalesChannelPriceResponse response = new MultiSalesChannelPriceResponse();
            response.setGlobalProductValue(new BigDecimal(String.valueOf(defaultPrice != null ? defaultPrice : 0)));
            response.setVariantId(variantData.getVariantId());

            List<MultiSalesChannelItem> items = multiSalesChannelItemRepository.findByMerchantCodeAndPratVariantId(owner, pratVariantId);
            if (items.size() > 1) {
                throw new RuntimeException("More than one msc item for variant " + pratVariantId + " & owner " + owner);
            } else {
                Optional<MultiSalesChannelItem> optionalMscItem = items.stream().findFirst();
                if (optionalMscItem.isPresent()) {
                    MultiSalesChannelItem mscItem = items.stream().findFirst().get();
                    response.multiSalesChannelItemId(mscItem.getMultiSalesChannelItemId()).owner(mscItem.getMerchantCode());
                    Map<String, Object> metadata = mscItem.getMetadata();
                    final String mscPriceName = "price"; //also sync with Johan
                    if (metadata != null && metadata.containsKey(mscPriceName)) {
                        String mscPriceStr = metadata.get(mscPriceName).toString();
                        BigDecimal mscPrice = new BigDecimal(mscPriceStr);
                        response.setMscPrice(mscPrice);
                    } else {
                        response.setMscPrice(null);
                    }
                }

                return response;
            }
        }
    }
}
