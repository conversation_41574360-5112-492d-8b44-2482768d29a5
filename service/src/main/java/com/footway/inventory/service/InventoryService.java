package com.footway.inventory.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.footway.fop.shared.client.merchant.MerchantClient;
import com.footway.fop.shared.client.merchant.MerchantPortalMerchant;
import com.footway.fop.shared.json.helper.ObjectSerializer;
import com.footway.inventory.component.PratClient;
import com.footway.inventory.configuration.InventoryServiceConfiguration;
import com.footway.inventory.graphql.InventoryResponse;
import com.footway.inventory.graphql.mapper.InventoryMapper;
import com.footway.inventory.mapper.VariantInventoryMapper;
import com.footway.inventory.model.dao.*;
import com.footway.inventory.model.dto.*;
import com.footway.inventory.repository.jpa.InventoryLevelRepository;
import com.footway.inventory.repository.jpa.InventoryViewRepository;
import com.footway.inventory.repository.jpa.VariantInventoryRepository;
import com.footway.inventory.repository.jpa.VariantInventoryStorageOrderLineRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Service
public class InventoryService {
    private final PratClient pratClient;
    private final MerchantService merchantService;
    private final InventoryServiceConfiguration configuration;
    private final ObjectSerializer serializer;
    private final VariantInventoryRepository variantInventoryRepository;
    private final InventoryViewRepository inventoryViewRepository;

    private final VariantInventoryStorageOrderLineRepository variantInventoryStorageOrderLineRepository;
    private final VariantInventoryMapper variantInventoryMapper;
    private final MerchantClient merchantClient;
    private final InventoryLevelRepository inventoryLevelRepository;
    private final InternalVariantService internalVariantService;
    private final VariantMappingService variantMappingService;
    private final FilterService filterService;
    private final MerchantClientService merchantClientService;
    private final ExternalVariantService externalVariantService;
    private final MultiSalesChannelVariantMappingService mscVariantMappingService;

    public InventoryService(PratClient pratClient, InventoryServiceConfiguration configuration, MerchantService merchantService, ObjectSerializer serializer, VariantInventoryRepository variantInventoryRepository, VariantInventoryMapper variantInventoryMapper, VariantInventoryStorageOrderLineRepository variantInventoryStorageOrderLineRepository, com.footway.fop.shared.client.merchant.MerchantClient merchantClient, InventoryViewRepository inventoryViewRepository, InventoryLevelRepository inventoryLevelRepository, InternalVariantService internalVariantService, VariantMappingService variantMappingService, FilterService filterService, MerchantClientService merchantClientService, ExternalVariantService externalVariantService, MultiSalesChannelVariantMappingService mscVariantMappingService) {
        this.configuration = configuration;
        this.serializer = serializer;
        this.merchantService = merchantService;
        this.variantInventoryRepository = variantInventoryRepository;
        this.variantInventoryStorageOrderLineRepository = variantInventoryStorageOrderLineRepository;
        this.variantInventoryMapper = variantInventoryMapper;
        this.merchantClient = merchantClient;
        this.inventoryViewRepository = inventoryViewRepository;
        log.info("{} {}", getClass().getSimpleName(), configuration.getUrl());
        this.pratClient = pratClient;
        this.inventoryLevelRepository = inventoryLevelRepository;
        this.internalVariantService = internalVariantService;
        this.variantMappingService = variantMappingService;
        this.mscVariantMappingService = mscVariantMappingService;
        this.filterService = filterService;
        this.merchantClientService = merchantClientService;
        this.externalVariantService = externalVariantService;
    }

    public List<InventoryItem> exportInventory(List<String> merchantIds) {
        return inventoryViewRepository.exportInventory(merchantIds);
    }

    public InventoryResponse getInventoryForGraphQL(String merchantID, int page, int size) {
        Integer merchantId = merchantService.merchantIDToMerchantId(merchantID);
        if (merchantId != null) {
            ResponseEntity<String> pratInventoryEntity = pratClient.getInventoryForLocation(merchantId, configuration.getLocationId(), page, size);
            if (pratInventoryEntity.getStatusCode() == HttpStatus.OK) {
                JsonNode root = serializer.toJsonNode(pratInventoryEntity.getBody());
                return InventoryMapper.restToGraphQL(root);
            } else {
                return new InventoryResponse();
            }
        } else {
            return new InventoryResponse();
        }
    }

    private List<String> getInternalMerchants(String merchantId) {
        List<String> internalMerchants = merchantClient.getConsignmentBySeller(merchantId);
        if (!internalMerchants.contains(merchantId)) {
            internalMerchants.add(merchantId);
        }
        return internalMerchants;
    }

    public List<MerchantPortalVariantInventory> getInventoryVariantForInternalMerchants(String merchantCode) {
        List<String> merchantIds = getInternalMerchants(merchantCode);
        List<PratVariantInventoriesDailySnapshot> variantInventoriesDailySnapshots = variantInventoryRepository.findAllByMerchantIdsAndLatestSnapshotDate(merchantIds);
        return variantInventoriesDailySnapshots.stream().map(variantInventoryMapper::pratVariantInventoriesDailySnapshotToMerchantPortalVariantInventory).collect(Collectors.toList());
    }

    public PaginatedMerchantPortalVariantInventoryResponse getPaginatedInventoryForInternalMerchants(String merchantId, int page, int pageSize) {
        List<String> merchantIds = getInternalMerchants(merchantId);
        Page<PratVariantInventoriesDailySnapshot> snapshotsPage = variantInventoryRepository.findPaginatedByMerchantIdsAndLatestSnapshotDate(merchantIds, PageRequest.of(page - 1, pageSize));

        List<MerchantPortalVariantInventory> items = snapshotsPage.getContent().stream().map(this::convertToDTO).collect(Collectors.toList());

        PaginatedMerchantPortalVariantInventoryResponse response = new PaginatedMerchantPortalVariantInventoryResponse();
        response.setItems(items);
        response.setTotalItems((int) snapshotsPage.getTotalElements());
        response.setCurrentPage(snapshotsPage.getNumber() + 1);
        response.setTotalPages(snapshotsPage.getTotalPages());
        return response;
    }

    private MerchantPortalVariantInventory convertToDTO(PratVariantInventoriesDailySnapshot snapshot) {
        if (snapshot == null) {
            return null;
        }
        MerchantPortalVariantInventory variantInventory = new MerchantPortalVariantInventory();
        variantInventory.setMerchantId(snapshot.getId().getMerchantId());
        variantInventory.setVariantId(snapshot.getId().getVariantId());
        variantInventory.setDisplayName(snapshot.getDisplayName());
        variantInventory.setSku(snapshot.getSku());
        variantInventory.setEan(snapshot.getEan());
        variantInventory.setMetadata(snapshot.getMetadata());
        variantInventory.setSnapshotDate(snapshot.getId().getSnapshotDate());

        return variantInventory;
    }

    @Cacheable("inventoryByMerchant")
    public ResponseEntity<InventoryImageResponse> getInventoryForMerchant(String merchantCode, Integer page, Integer size) {
        Integer merchantId = merchantService.merchantIDToMerchantId(merchantCode);
        if (merchantId != null) {
            ResponseEntity<String> pratInventoryEntity = pratClient.getInventoryForLocation(merchantId, configuration.getLocationId(), page, (int) size);
            if (pratInventoryEntity.getStatusCode() == HttpStatus.OK) {
                List<MinimalVariant> result = new ArrayList<>();
                JsonNode inventoryRoot = serializer.toJsonNode(pratInventoryEntity.getBody());

                JsonNode pageableRoot = inventoryRoot.get("pageable");
                Pagination pagination = new Pagination();
                pagination.setOffset(pageableRoot.get("offset").asInt());
                pagination.setPageSize(pageableRoot.get("pageSize").asInt());
                pagination.setPageNumber(pageableRoot.get("pageNumber").asInt());
                pagination.setTotalPages(inventoryRoot.get("totalPages").asInt());
                pagination.setTotalElements(inventoryRoot.get("totalElements").asInt());
                pagination.setLast(inventoryRoot.get("last").asBoolean());

                ArrayNode inventoryArray = (ArrayNode) inventoryRoot.get("content");
                for (JsonNode inventoryEntry : inventoryArray) {
                    JsonNode inventoryVariant = inventoryEntry.get("variant");
                    String variantNumber = inventoryVariant.get("variantNumber").asText();
                    ResponseEntity<String> variantResponse = pratClient.getVariantByVariantNumber(variantNumber);
                    JsonNode variantRoot = serializer.toJsonNode(variantResponse.getBody());
                    MinimalVariant minimalVariant = new MinimalVariant();
                    minimalVariant.setVariantNumber(variantNumber);
                    minimalVariant.setDead(variantRoot.get("dead").asBoolean());
                    minimalVariant.setEan(variantRoot.get("ean").asText());
                    minimalVariant.setExistsKjula(variantRoot.has("exists_kjula") ? variantRoot.get("exists_kjula").asBoolean() : null);
                    minimalVariant.setOnHandKjula(variantRoot.has("onHandKjula") ? variantRoot.get("onHandKjula").asInt() : null);
                    minimalVariant.setId(variantRoot.get("id").asInt());

                    JsonNode productRoot = variantRoot.get("product");
                    MinimalProduct minimalProduct = new MinimalProduct();
                    minimalProduct.setProductNumber(productRoot.get("productNumber").asText());
                    minimalProduct.setId(productRoot.get("id").asInt());

                    ArrayNode imageArray = (ArrayNode) productRoot.get("attributes").get("images");
                    List<MinimalImage> images = new ArrayList<>();
                    for (JsonNode imageEntry : imageArray) {
                        MinimalImage image = new MinimalImage();
                        image.setListImage(imageEntry.get("listImage").asBoolean());
                        image.setUrl(imageEntry.get("url").asText());
                        images.add(image);
                    }

                    Collections.sort(images, (o1, o2) -> Boolean.compare(o2.getListImage(), o1.getListImage()));

                    minimalProduct.setImages(images);
                    minimalVariant.setProduct(minimalProduct);
                    result.add(minimalVariant);

                }
                InventoryImageResponse response = new InventoryImageResponse();
                response.setPagination(pagination);
                response.setVariants(result);
                return ResponseEntity.ok(response);
            } else {
                return ResponseEntity.status(HttpStatus.FAILED_DEPENDENCY).build();
            }
        } else {
            return ResponseEntity.status(HttpStatus.NOT_FOUND).build();
        }
    }

    public PaginatedStorageOrderLineInventoryResponse getStorageOrderLinesWithInventory(
            String merchantId,
            int page,
            int pageSize
    ) {
        try {
            Page<StorageOrderLineInventoryResponse> results = variantInventoryStorageOrderLineRepository
                    .findStorageOrderLinesWithInventoryInfo(merchantId, PageRequest.of(page - 1, pageSize));

            PaginatedStorageOrderLineInventoryResponse response = new PaginatedStorageOrderLineInventoryResponse();
            response.setItems(results.getContent());
            response.setTotalItems((int) results.getTotalElements());
            response.setCurrentPage(results.getNumber() + 1);
            response.setTotalPages(results.getTotalPages());

            return response;
        } catch (Exception e) {
            log.error("Error fetching storage order lines with inventory for merchant {}: ", merchantId, e);
            return null;
        }
    }

    public PaginatedInventoryResponse removeInternalFieldsFromItems(PaginatedInventoryResponse response) {
        if (response != null && response.getItems() != null) {
            for (InventoryItem item : response.getItems()) {
                item.setPrice(null);
            }
        }
        return response;
    }

    @Cacheable("getInventory")
    public Optional<InventoryView> getInventory(String merchantID, String footwayVariantId) {
        return inventoryViewRepository.findByMerchantIdAndVariantId(merchantID, footwayVariantId);
    }

    @Cacheable("getInventoryLevel")
    public Optional<InventoryLevel> getInventoryLevel(String merchantID, String footwayVariantId) {
        return inventoryLevelRepository.findByMerchantIdAndVariantId(merchantID, footwayVariantId);
    }

    public boolean updateQuantity(InventoryLevel inventory, int quantity) {
        if (inventory.getQuantity() != quantity) {
            try {
                inventory.setQuantity(quantity);
                inventoryLevelRepository.save(inventory);
                return true;
            } catch (RuntimeException e) {
                Optional<InventoryLevel> existingEntry = inventoryLevelRepository.findByMerchantIdAndVariantId(inventory.getMerchantId(), inventory.getVariantId());
                if (existingEntry.isPresent()) {
                    log.debug("Already persisted inventory level {}", existingEntry.get());
                    return false;
                } else {
                    throw new RuntimeException("Optimistic lock, but there was still nothing in DB", e);
                }
            }
        } else {
            return false;
        }
    }

    public List<InventoryShopifyMappingItem> getInventoryShopifyMappingItem(String merchantId, Integer page, Integer pageSize, Filters filters) {
        // fetch Variants mapping
        List<VariantMapping> mappings = variantMappingService.getVariantMappingsByMerchantCode(merchantId);
        log.info("Found {} variantMappings for merchant {}", mappings.size(), merchantId);

        // fetch shopify variants
        List<ExternalVariant> shopifyVariants = new ArrayList<>();
        PaginatedExternalVariantResponse response = externalVariantService.getExternalVariants(merchantId, page, pageSize);
        if (response.getTotalItems() > 0) {
            shopifyVariants = response.getItems().stream()
                    .map(this::convertToExternalVariant)
                    .toList();
        }

        // fetch Merchant ids by org_id
        List<MerchantPortalMerchant> merchants = merchantClientService.getMerchantsByMerchantCode(merchantId);
        List<String> merchantIds = merchants.stream().map(MerchantPortalMerchant::getMerchantCode).collect(Collectors.toList());

        // fetch Inventory Variants
        PaginatedInventoryResponse rawPaginatedInventoryResponse = filterService.searchInventory(
                merchantIds,
                "",
                filters.getVendor(),
                filters.getDepartment(),
                filters.getProductGroup(),
                filters.getProductType(),
                filters.getVariantIds(),
                filters.getSearchText(),
                page != null ? page : 1,
                pageSize != null ? pageSize : 10000,
                null,
                null);
        PaginatedInventoryResponse santizedPaginatedInventoryResponse = removeInternalFieldsFromItems(rawPaginatedInventoryResponse);
        List<InventoryItem> inventoryItems = santizedPaginatedInventoryResponse.getItems();
        log.info("Found {} inventory items for merchant {}", inventoryItems.size(), merchantId);

        // Create mapping items with all basic information and default status as "unmapped"
        List<InventoryShopifyMappingItem> inventoryShopifyMappingItems = new ArrayList<>();
        Map<String, InventoryShopifyMappingItem> mappingItemsByVariantId = new HashMap<>();

        for (InventoryItem inventoryItem : inventoryItems) {
            InventoryShopifyMappingItem mappingItem = new InventoryShopifyMappingItem();
            mappingItem.setMerchantId(inventoryItem.getMerchantId());
            mappingItem.setFootwayVariantId(inventoryItem.getVariantId());
            mappingItem.setProductName(inventoryItem.getProductName());
            mappingItem.setEan(!inventoryItem.getEan().isEmpty() ? inventoryItem.getEan().get(0) : null);
            mappingItem.setFootwayImageUrl(inventoryItem.getImageUrl());
            mappingItem.setStatus("unmapped");

            inventoryShopifyMappingItems.add(mappingItem);
            mappingItemsByVariantId.put(inventoryItem.getVariantId(), mappingItem);
        }

        // Process existing mappings first (these take precedence)
        for (VariantMapping mapping : mappings) {
            InventoryShopifyMappingItem mappingItem = mappingItemsByVariantId.get(mapping.getFootwayVariantId());
            if (mappingItem != null) {
                // Find the corresponding Shopify variant
                Optional<ExternalVariant> mappedShopifyVariant = shopifyVariants.stream()
                        .filter(sv -> sv.getId().getVariantId().toString().equals(mapping.getintegrationVariantId()))
                        .findFirst();

                if (mappedShopifyVariant.isPresent()) {
                    ExternalVariant shopifyVariant = mappedShopifyVariant.get();
                    mappingItem.setShopifyVariantId(shopifyVariant.getId().getVariantId().toString());
                    mappingItem.setDisplayName(shopifyVariant.getDisplayName());
                    mappingItem.setShopifyImageUrl(extractFirstImageUrlFromMetadata(shopifyVariant));
                    mappingItem.setStatus("mapped");
                }
            }
        }

        // Process unmapped items to find potential matches
        for (InventoryShopifyMappingItem mappingItem : inventoryShopifyMappingItems) {
            if ("unmapped".equals(mappingItem.getStatus()) && mappingItem.getEan() != null) {
                String inventoryEan = mappingItem.getEan();
                List<ExternalVariant> potentialMatches = shopifyVariants.stream()
                        .filter(sv -> inventoryEan.equals(sv.getEan()))
                        .toList();

                if (potentialMatches.size() == 1) {
                    // Single match found - suggest mapping
                    ExternalVariant shopifyVariant = potentialMatches.get(0);
                    mappingItem.setShopifyVariantId(shopifyVariant.getId().getVariantId().toString());
                    mappingItem.setDisplayName(shopifyVariant.getDisplayName());
                    mappingItem.setShopifyImageUrl(extractFirstImageUrlFromMetadata(shopifyVariant));
                    mappingItem.setStatus("suggested");
                } else if (potentialMatches.size() > 1) {
                    // Multiple matches found
                    mappingItem.setStatus("multiple_matches");
                }
            }
        }

        return inventoryShopifyMappingItems;
    }

    private String extractFirstImageUrlFromMetadata(ExternalVariant externalVariant) {
//        if (externalVariant != null && externalVariant.getImageUrl() != null) {
            return externalVariant != null && externalVariant.getImageUrl() != null ? externalVariant.getImageUrl() : null;
//        } else {
//            externalVariant.setMetadata(null);
//            throw new RuntimeException(ObjectSerializer.instance().toString(externalVariant));
//        }
    }

    @Transactional(readOnly = true, transactionManager = "inventoryTransactionManager")
    public Integer syncStaleInventory(int hours, int limit) {
        LocalDateTime cutoffTime = LocalDateTime.now().minusHours(hours);
        log.info("syncing variants updated before {}", cutoffTime);
        try (Stream<VariantTable> staleLevels = internalVariantService.getOlderThan(hours, limit)) {
            return requestVariantInventoryUpdates(staleLevels.map(VariantTable::getVariantId), limit);
        }
    }

    /**
     * Will not take organizations products if unmapped. Also, will only take stuff with levels (if no levels, we'll have to wait).
     */
    @Transactional(readOnly = true, transactionManager = "inventoryTransactionManager")
    public Integer syncInventory(String merchantCode, int limit) {
        Set<String> variantIds = new HashSet<>();

        List<VariantMapping> mscMappings = mscVariantMappingService.getVariantMappingsByMerchantCode(merchantCode);
        List<String> mappedMscVariantIds = mscMappings.stream()
                .map(VariantMapping::getFootwayVariantId)
                .toList();
        variantIds.addAll(mappedMscVariantIds);
        log.debug("Found {} mapped msc variants for {}", mscMappings.size(), merchantCode);

        List<VariantMapping> ownMappings = variantMappingService.getVariantMappingsByMerchantCode(merchantCode);
        List<String> mappedVariantIds = ownMappings.stream()
                .map(VariantMapping::getFootwayVariantId)
                .toList();
        variantIds.addAll(mappedVariantIds);
        log.debug("Found {} mapped variants for {}", mappedVariantIds.size(), merchantCode);

        try (Stream<InventoryLevel> ownLevels = inventoryLevelRepository.findByMerchantId(merchantCode)) {
            List<String> ownLevelVariantIds = ownLevels
                    .map(InventoryLevel::getVariantId)
                    .toList();
            variantIds.addAll(ownLevelVariantIds);
            log.debug("Found {} own level variants for {}", ownLevelVariantIds.size(), merchantCode);
        }
        return requestVariantInventoryUpdates(variantIds.stream(), limit);
    }

    public RecommendedRetailPricesResponse getRecommendedRetailPrice(String variantId) {
        ResponseEntity<Variant> response = pratClient.getPratVariantByVariantId(variantId);
        if (response.getStatusCode().is2xxSuccessful()) {
            Variant pratVariant = response.getBody();
            if (pratVariant != null && pratVariant.getProduct() != null) {
                RecommendedRetailPricesResponse retailPricesResponse = new RecommendedRetailPricesResponse();

                // Extract the recommendedRetailPrice from product attributes using ObjectSerializer
                Object attributes = pratVariant.getProduct().getAttributes();
                if (attributes != null) {
                    try {
                        JsonNode attributesNode = serializer.toJsonNode(attributes);
                        if (attributesNode.has("recommended_retail_price")) {
                            JsonNode recommendedPricesNode = attributesNode.get("recommended_retail_price");

                            // Convert JsonNode to Map<String, Integer>
                            Map<String, Integer> priceMap = new HashMap<>();
                            Iterator<Map.Entry<String, JsonNode>> fields = recommendedPricesNode.fields();
                            while (fields.hasNext()) {
                                Map.Entry<String, JsonNode> entry = fields.next();
                                if (entry.getValue().isNumber()) {
                                    priceMap.put(entry.getKey(), entry.getValue().asInt());
                                }
                            }

                            retailPricesResponse.setRecommendedRetailPrice(priceMap);
                            return retailPricesResponse;
                        }
                    } catch (Exception e) {
                        log.error("Error processing recommended retail price for variant {}: {}", variantId, e.getMessage());
                    }
                }
            }
        }
        return null;
    }

    private int requestVariantInventoryUpdates(Stream<String> variantIds, int limit) {
        AtomicInteger count = new AtomicInteger();
        count.set(0);
        log.debug("request sync of {}", limit);

        variantIds.limit(limit).forEach(footwayVariantId -> {
            if (count.incrementAndGet() % 100 == 0) {
                try {
                    log.debug("request sync of {}", count);
                    Thread.sleep(100);
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }
            }

            try {
                pratClient.sendVariantInventorySNS(footwayVariantId);
            } catch (RuntimeException e) {
                //if we fail, we fail!
            }
        });
        log.info("request sync of {}", count);
        return count.get();
    }

    private ExternalVariant convertToExternalVariant(ExternalVariantResponse response) {
        ExternalVariant variant = new ExternalVariant();
        ExternalVariantId id = new ExternalVariantId();
        id.setMerchantId(response.getMerchantId());
        id.setVariantId(response.getVariantId());
        variant.setId(id);
        variant.setDisplayName(response.getDisplayName());
        variant.setSku(response.getSku());
        variant.setEan(response.getEan());
        variant.setImageUrl(response.getImageUrl());
//        variant.setProductId(response.); //FIXME: missing
        if (response.getMetadata() != null) {
            ObjectMapper mapper = new ObjectMapper();
            variant.setMetadata(mapper.convertValue(response.getMetadata(), new TypeReference<Map<String, Object>>() {
            }));
        }
        return variant;
    }
}