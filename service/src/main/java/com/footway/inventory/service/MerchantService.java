package com.footway.inventory.service;

import com.footway.fop.shared.client.merchant.MerchantClient;
import com.footway.fop.shared.client.merchant.MerchantPortalMerchant;
import com.google.auth.oauth2.GoogleCredentials;
import com.google.auth.oauth2.IdTokenCredentials;
import com.google.auth.oauth2.IdTokenProvider;
import lombok.extern.slf4j.Slf4j;
import org.openapitools.configuration.RemoteServiceConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;

import java.io.IOException;


@Slf4j
@Service
public class MerchantService {
    private final Logger LOGGER = LoggerFactory.getLogger(getClass());
    private final MerchantClient merchantClient;
    private final RemoteServiceConfig remoteServiceConfig;

    public MerchantService(RemoteServiceConfig remoteServiceConfig, MerchantClient merchantClient) {
        this.merchantClient = merchantClient;
        this.remoteServiceConfig = remoteServiceConfig;
    }

    @Cacheable(value = "merchantIds", key = "#merchantID")
    public Integer merchantIDToMerchantId(String merchantID) {
        try {
            return getMerchant(merchantID).getPratMerchantId();
        } catch (RuntimeException e) {
            LOGGER.warn(e.getMessage(), e);
            return null;
        }
    }

    public MerchantPortalMerchant getMerchant(String merchantID) {
        return merchantClient.getFopMerchant(merchantID);
    }

    private HttpHeaders provideHeaders() {
        final String headerKeyName = "x-apikey";
        String accessToken = getToken(remoteServiceConfig.getMerchantServiceUrl());
        HttpHeaders headers = new HttpHeaders();
        headers.set("Authorization", "Bearer " + accessToken);
        return headers;
    }

    public String getToken(String TARGET_AUDIENCE) {
        try {
            GoogleCredentials credentials = GoogleCredentials.getApplicationDefault();

            if (!(credentials instanceof IdTokenProvider)) {
                throw new IllegalArgumentException("Credentials are not an instance of IdTokenProvider.");
            }

            IdTokenCredentials idTokenCredentials = IdTokenCredentials.newBuilder()
                    .setIdTokenProvider((IdTokenProvider) credentials)
                    .setTargetAudience(TARGET_AUDIENCE)
                    .build();

            return idTokenCredentials.refreshAccessToken().getTokenValue();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
}
