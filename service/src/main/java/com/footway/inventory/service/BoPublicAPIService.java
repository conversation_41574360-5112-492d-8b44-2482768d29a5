package com.footway.inventory.service;

import java.util.Base64;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;

import com.footway.inventory.configuration.BoPublicAPIConfig;
import com.footway.inventory.model.dto.MerchantPortalVariantUnidentifiedInventory;

@Service
public class BoPublicAPIService {

    private final WebClient webClient;

    @Autowired
    public BoPublicAPIService(BoPublicAPIConfig config) {
        this.webClient = WebClient.builder()
                .baseUrl(config.getUrl())
                .defaultHeader(HttpHeaders.AUTHORIZATION, getBasicAuthHeader(config))
                .defaultHeader(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON_VALUE)
                .build();
    }

    public MerchantPortalVariantUnidentifiedInventory getUnidentifiedInventory(String merchantCode) {
        return webClient.get()
                .uri("/inventory/unidentified/{merchantCode}", merchantCode)
                .retrieve()
                .bodyToMono(MerchantPortalVariantUnidentifiedInventory.class)
                .block();
    }

    private String getBasicAuthHeader(BoPublicAPIConfig config) {
        String auth = config.getUsername() + ":" + config.getPassword();
        byte[] encodedAuth = Base64.getEncoder().encode(auth.getBytes());
        return "Basic " + new String(encodedAuth);
    }
}
