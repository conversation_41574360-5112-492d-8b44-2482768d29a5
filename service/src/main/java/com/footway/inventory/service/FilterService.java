package com.footway.inventory.service;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import com.footway.inventory.configuration.datasource.ReadOnly;
import com.footway.inventory.model.dao.InventoryView;
import com.footway.inventory.model.dao.MscItemView;
import com.footway.inventory.model.dto.AvailableFilters;
import com.footway.inventory.model.dto.InventoryItem;
import com.footway.inventory.model.dto.PaginatedInventoryResponse;
import com.footway.inventory.repository.jpa.InventoryViewRepository;
import com.footway.inventory.repository.jpa.MscItemViewRepository;
import com.footway.inventory.service.filter.AvailableFilterHelper;
import com.footway.inventory.service.filter.dto.AllCounterContainer;
import com.footway.inventory.service.filter.dto.FilterMap;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class FilterService {
    private final InventoryViewRepository inventoryViewRepository;
    private final MscItemViewRepository mscItemViewRepository;

    public FilterService(InventoryViewRepository inventoryViewRepository, MscItemViewRepository mscItemViewRepository) {
        this.inventoryViewRepository = inventoryViewRepository;
        this.mscItemViewRepository = mscItemViewRepository;
    }

    /**
     * Search inventory items with optional filters and sorting
     *
     * @param merchantId    Optional merchant ID filter
     * @param productName   Optional product name search term
     * @param vendor       Optional vendor filter
     * @param department   Optional department filter
     * @param productGroup Optional product group filter
     * @param productType  Optional product type filter
     * @param page        Page number (1-based)
     * @param pageSize    Number of items per page
     * @param sortBy      Field to sort by (e.g., "productName", "quantity"). Defaults to "productName" if invalid or not specified
     * @param sortDirection Sort direction ("asc" or "desc"). Defaults to "asc" if not specified
     * @return Paginated response containing matching inventory items
     */
    @ReadOnly
    public PaginatedInventoryResponse searchInventory(
            List<String> merchantId,
            String productName,
            List<String> vendor,
            List<String> department,
            List<String> productGroup,
            List<String> productType,
            List<String> variantIds,
            String searchText,
            int page,
            int pageSize,
            String sortBy,
            String sortDirection
    ) {
        try {
            Sort.Direction direction = sortDirection != null && sortDirection.equalsIgnoreCase("desc")
                    ? Sort.Direction.DESC
                    : Sort.Direction.ASC;

            String sortField = sortBy != null ? sortBy : "productName";

            if (!isValidSortProperty(sortField)) {
                sortField = "productName";
            }

            Pageable pageable = PageRequest.of(
                    page - 1,
                    pageSize,
                    Sort.by(direction, sortField)
            );

            List<InventoryView> inventoryList = inventoryViewRepository.searchInventory(
                    nullToList(merchantId),
                    productName,
                    nullToList(vendor),
                    nullToList(department),
                    nullToList(productGroup),
                    nullToList(productType),
                    nullToList(variantIds),
                    searchText,
                    pageable
            );

            long countTotal = inventoryViewRepository.countTotal(
                    nullToList(merchantId),
                    productName,
                    nullToList(vendor),
                    nullToList(department),
                    nullToList(productGroup),
                    nullToList(productType),
                    nullToList(variantIds),
                    searchText
            );

            Page<InventoryView> inventory = new PageImpl<>(inventoryList, pageable, countTotal);

            // Convert InventoryTable items to InventoryItem
            List<InventoryItem> items = inventory.stream()
                    .map(InventoryView::toInventory)
                    .collect(Collectors.toList());

            // Build the response
            PaginatedInventoryResponse response = new PaginatedInventoryResponse();
            response.setItems(items);
            response.setTotalItems((int) inventory.getTotalElements());
            response.setCurrentPage(inventory.getNumber() + 1);  // Convert back to 1-based
            response.setTotalPages(inventory.getTotalPages());

            return response;

        } catch (Exception e) {
            log.error("Error searching inventory with filters - merchantId: {}, productName: {}, vendor: {}, department: {}, productGroup: {}, productType: {}",
                    merchantId, productName, vendor, department, productGroup, productType, e);
            throw new RuntimeException("Failed to search inventory", e);
        }
    }

    private boolean isValidSortProperty(String propertyName) {
        Set<String> validProperties = Set.of(
                "quantity",
                "productName",
                "vendor",
                "department",
                "productGroup",
                "productType"
        );
        return validProperties.contains(propertyName);
    }


    @ReadOnly
    public AvailableFilters getAvailableFilters(
            List<String> merchantIds,
            List<String> vendors,
            List<String> departments,
            List<String> productGroups,
            List<String> productTypes) {
        AllCounterContainer result = inventoryViewRepository.getAvailableFilters();
        FilterMap currentFilters = new FilterMap(merchantIds, vendors, departments, productGroups, productTypes);
//        return new AvailableFilterHelper(result).toAvailableFilters(currentFilters);
        return new AvailableFilterHelper(result).toAvailableFiltersComplete(currentFilters);
    }

    /**
     * Search inventory items with optional filters
     *
     * @param merchantId   Optional merchant ID filter
     * @param productName  Optional product name search term
     * @param vendor       Optional vendor filter
     * @param department   Optional department filter
     * @param productGroup Optional product group filter
     * @param productType  Optional product type filter
     * @param isInStock    Optional in stock filter
     * @param page         Page number (1-based)
     * @param pageSize     Number of items per page
     * @return Paginated response containing matching inventory items
     */
    @ReadOnly
    public PaginatedInventoryResponse searchMsc(
            List<String> merchantId,
            String productName,
            List<String> vendor,
            List<String> department,
            List<String> productGroup,
            List<String> productType,
            Boolean isInStock,
            int page,
            int pageSize
    ) {
        try {
            // Create pageable with sorting
            Pageable pageable = PageRequest.of(
                    page - 1,  // Convert to 0-based
                    pageSize,  // page size limited in openapi spec
                    Sort.by(Sort.Direction.ASC, "productName")
            );

            List<MscItemView> inventoryList = mscItemViewRepository.searchMsc(nullToList(merchantId), productName, nullToList(vendor), nullToList(department), nullToList(productGroup), nullToList(productType), isInStock, pageable);
            long countTotalMsc = mscItemViewRepository.countTotalMsc(nullToList(merchantId), productName, nullToList(vendor), nullToList(department), nullToList(productGroup), nullToList(productType), isInStock);

            Page<MscItemView> inventory = new PageImpl<>(inventoryList, pageable, countTotalMsc);

            // Convert InventoryTable items to InventoryItem
            List<InventoryItem> items = inventory.stream()
                    .map(MscItemView::toInventory)
                    .collect(Collectors.toList());

            // Build the response
            PaginatedInventoryResponse response = new PaginatedInventoryResponse();
            response.setItems(items);
            response.setTotalItems((int) inventory.getTotalElements());
            response.setCurrentPage(inventory.getNumber() + 1);  // Convert back to 1-based
            response.setTotalPages(inventory.getTotalPages());

            return response;

        } catch (Exception e) {
            log.error("Error searching inventory with filters - merchantId: {}, productName: {}, vendor: {}, department: {}, productGroup: {}, productType: {}",
                    merchantId, productName, vendor, department, productGroup, productType, e);
            throw new RuntimeException("Failed to search inventory", e);
        }
    }

    @ReadOnly
    public AvailableFilters getAvailableMscFilters(
            List<String> merchantIds,
            List<String> vendors,
            List<String> departments,
            List<String> productGroups,
            List<String> productTypes) {
        AllCounterContainer result = mscItemViewRepository.getAvailableFilters();
        FilterMap currentFilters = new FilterMap(merchantIds, vendors, departments, productGroups, productTypes);
        return new AvailableFilterHelper(result).toAvailableFiltersComplete(currentFilters);
    }

    private List<String> nullToList(List<String> list) {
        return list!=null?list:new ArrayList<>();
    }
}
