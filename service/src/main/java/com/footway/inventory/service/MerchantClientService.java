package com.footway.inventory.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.footway.fop.shared.client.GoogleRestClient;
import com.footway.fop.shared.client.merchant.MerchantPortalMerchant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.List;

@Slf4j
@Service
public class MerchantClientService {
    private final GoogleRestClient googleRestClient;

    public MerchantClientService(@Value("${fop-api.merchant-service-url}") String baseUrl) {
        this.googleRestClient = new GoogleRestClient(baseUrl);
    }

    @Cacheable({"portalMerchants"})
    public List<MerchantPortalMerchant> getMerchantsByOrgId(Integer orgId) {
        String url = UriComponentsBuilder.fromPath("/merchants/by-org/{orgId}")
                .buildAndExpand(orgId)
                .toUriString();
        ResponseEntity<String> responseEntity = this.googleRestClient.get(url).send();
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            TypeReference<List<MerchantPortalMerchant>> typeReference = new TypeReference<List<MerchantPortalMerchant>>() {};
            return objectMapper.readValue(responseEntity.getBody(), typeReference);
        } catch (Exception e) {
            throw new RuntimeException("Failed to get merchants with orgId=" + orgId + " : " + String.valueOf(responseEntity.getBody()));
        }
    }

    @Cacheable({"portalMerchantsByMerchantCode"})
    public List<MerchantPortalMerchant> getMerchantsByMerchantCode(String merchantCode) {
        String url = UriComponentsBuilder.fromPath("/merchants/by-merchant/{merchantCode}")
                .buildAndExpand(merchantCode)
                .toUriString();
        ResponseEntity<String> responseEntity = this.googleRestClient.get(url).send();
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            TypeReference<List<MerchantPortalMerchant>> typeReference = new TypeReference<>() {
            };
            return objectMapper.readValue(responseEntity.getBody(), typeReference);
        } catch (Exception e) {
            throw new RuntimeException("Failed to get merchants with merchantCode=" + merchantCode + " : " + responseEntity.getBody());
        }
    }
}
