package com.footway.inventory.service;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import com.footway.inventory.model.dao.VariantTable;
import com.footway.inventory.repository.jpa.InventoryLevelRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.footway.inventory.model.dao.StorageOrderLineTable;
import com.footway.inventory.model.dto.StorageOrderLine;
import com.footway.inventory.repository.jpa.StorageOrderLineRepository;
import com.footway.inventory.repository.jpa.ExternalVariantRepository;
import com.footway.inventory.model.dao.ExternalVariant;

import java.util.Map;

@Service
public class StorageOrderService {

    private final StorageOrderLineRepository storageOrderLineRepository;
    private final ExternalVariantRepository externalVariantRepository;
    private final InternalVariantService internalVariantService;
    private final InventoryLevelRepository inventoryLevelRepository;

    @Autowired
    public StorageOrderService(StorageOrderLineRepository storageOrderLineRepository, ExternalVariantRepository externalVariantRepository, InternalVariantService internalVariantService, InventoryLevelRepository inventoryLevelRepository) {
        this.storageOrderLineRepository = storageOrderLineRepository;
        this.externalVariantRepository = externalVariantRepository;
        this.internalVariantService = internalVariantService;
        this.inventoryLevelRepository = inventoryLevelRepository;
    }

    @Transactional(transactionManager = "inventoryTransactionManager")
    public StorageOrderLine createStorageOrderLine(StorageOrderLine storageOrderLine) {
        StorageOrderLineTable storageOrderLineTable = convertToStorageOrderLineTable(storageOrderLine);
        StorageOrderLineTable savedStorageOrderLineTable = storageOrderLineRepository.save(storageOrderLineTable);
        return savedStorageOrderLineTable.toStorageOrderLine();
    }

    @Transactional(transactionManager = "inventoryTransactionManager")
    public StorageOrderLine updateStorageOrderLine(StorageOrderLine storageOrderLine) {
        if (storageOrderLine.getStorageOrderLineId() == null) {
            throw new IllegalArgumentException("Storage order line ID must not be null for update operation");
        }

        Optional<StorageOrderLineTable> existingStorageOrderLineTable = storageOrderLineRepository.findById(storageOrderLine.getStorageOrderLineId());
        if (existingStorageOrderLineTable.isEmpty()) {
            throw new IllegalArgumentException("Storage order line not found with ID: " + storageOrderLine.getStorageOrderLineId());
        }

        StorageOrderLineTable storageOrderLineTable = convertToStorageOrderLineTable(storageOrderLine);
        storageOrderLineTable.setCreatedAt(existingStorageOrderLineTable.get().getCreatedAt()); // Preserve the original creation date
        StorageOrderLineTable updatedStorageOrderLineTable = storageOrderLineRepository.save(storageOrderLineTable);
        return updatedStorageOrderLineTable.toStorageOrderLine();
    }

    @Transactional(transactionManager = "inventoryTransactionManager")
    public List<StorageOrderLine> updateStorageOrderLines(String storageOrderId, List<StorageOrderLine> storageOrderLines) {
        List<StorageOrderLineTable> existingLines = storageOrderLineRepository.findByStorageOrderIdWithCase(storageOrderId);

        // Remove lines that are not in the update list
        List<Long> updateIds = storageOrderLines.stream()
                .map(StorageOrderLine::getStorageOrderLineId)
                .filter(id -> id != null)
                .collect(Collectors.toList());
        existingLines.removeIf(line -> !updateIds.contains(line.getStorageOrderLineId()));

        // Update existing lines and add new ones
        List<StorageOrderLineTable> linesToSave = storageOrderLines.stream()
                .map(line -> {
                    StorageOrderLineTable tableLine = convertToStorageOrderLineTable(line);
                    existingLines.stream()
                            .filter(existing -> existing.getStorageOrderLineId().equals(line.getStorageOrderLineId()))
                            .findFirst()
                            .ifPresent(existing -> tableLine.setCreatedAt(existing.getCreatedAt()));
                    return tableLine;
                })
                .collect(Collectors.toList());

        List<StorageOrderLineTable> savedLines = storageOrderLineRepository.saveAll(linesToSave);
        return savedLines.stream()
                .map(StorageOrderLineTable::toStorageOrderLine)
                .collect(Collectors.toList());
    }

    @Transactional(readOnly = true, transactionManager = "inventoryTransactionManager")
    public Optional<StorageOrderLine> getStorageOrderLine(Long storageOrderLineId) {
        return storageOrderLineRepository.findById(storageOrderLineId)
                .map(StorageOrderLineTable::toStorageOrderLine);
    }

    @Transactional(readOnly = true, transactionManager = "inventoryTransactionManager")
    public List<StorageOrderLine> getStorageOrderLines(String storageOrderId) {
        return storageOrderLineRepository.findByStorageOrderIdWithCase(storageOrderId)
                .stream()
                .map(StorageOrderLineTable::toStorageOrderLine)
                .collect(Collectors.toList());
    }

    @Transactional(readOnly = true, transactionManager = "inventoryTransactionManager")
    public List<StorageOrderLine> getStorageOrderLinesByMerchantId(String merchantId) {
        return storageOrderLineRepository.findByMerchantIdWithCase(merchantId)
                .stream()
                .map(StorageOrderLineTable::toStorageOrderLine)
                .collect(Collectors.toList());
    }

    @Transactional(transactionManager = "inventoryTransactionManager")
    public boolean removeStorageOrderLine(Long storageOrderLineId) {
        if (storageOrderLineRepository.existsById(storageOrderLineId)) {
            storageOrderLineRepository.deleteById(storageOrderLineId);
            return true;
        }
        return false;
    }

    @Transactional(transactionManager = "inventoryTransactionManager")
    public boolean removeStorageOrderLines(String storageOrderId) {
        List<StorageOrderLineTable> lines = storageOrderLineRepository.findByStorageOrderIdWithCase(storageOrderId);
        if (!lines.isEmpty()) {
            storageOrderLineRepository.deleteAll(lines);
            return true;
        }
        return false;
    }

    @Transactional(readOnly = true, transactionManager = "inventoryTransactionManager")
    public long countStorageOrderLinesByStorageOrderId(String storageOrderId) {
        return storageOrderLineRepository.countByStorageOrderIdWithCase(storageOrderId);
    }

    @Transactional(readOnly = true, transactionManager = "inventoryTransactionManager")
    public long countStorageOrderLinesByMerchantId(String merchantId) {
        return storageOrderLineRepository.countByMerchantIdWithCase(merchantId);
    }

    private StorageOrderLineTable convertToStorageOrderLineTable(StorageOrderLine storageOrderLine) {
        StorageOrderLineTable storageOrderLineTable = new StorageOrderLineTable();
        storageOrderLineTable.setStorageOrderLineId(storageOrderLine.getStorageOrderLineId());
        storageOrderLineTable.setStorageOrderId(storageOrderLine.getStorageOrderId());
        storageOrderLineTable.setExpectedDeliveryDate(storageOrderLine.getExpectedDeliveryDate());
        storageOrderLineTable.setMerchantId(storageOrderLine.getMerchantId());
        storageOrderLineTable.setProductName(storageOrderLine.getProductName());
        storageOrderLineTable.setProductNumber(storageOrderLine.getProductNumber());
        storageOrderLineTable.setProductBrand(storageOrderLine.getProductBrand());
        storageOrderLineTable.setGtinEan(storageOrderLine.getGtinEan());
        storageOrderLineTable.setShopifyVariantId(storageOrderLine.getShopifyVariantId());
        storageOrderLineTable.setCountryOfOrigin(storageOrderLine.getCountryOfOrigin());
        storageOrderLineTable.setSize(storageOrderLine.getSize());
        storageOrderLineTable.setQuantity(storageOrderLine.getQuantity());
        storageOrderLineTable.setHeight(storageOrderLine.getHeight());
        storageOrderLineTable.setLength(storageOrderLine.getLength());
        storageOrderLineTable.setWidth(storageOrderLine.getWidth());
        storageOrderLineTable.setWeight(storageOrderLine.getWeight());
        storageOrderLineTable.setValue(storageOrderLine.getValue());
        storageOrderLineTable.setPrice(storageOrderLine.getPrice());
        storageOrderLineTable.setProductType(storageOrderLine.getProductType());
        storageOrderLineTable.setProductGroup(storageOrderLine.getProductGroup());
        storageOrderLineTable.setStatus(storageOrderLine.getStatus());
        storageOrderLineTable.setVariantType(storageOrderLine.getVariantType());
        return storageOrderLineTable;
    }

    public Optional<StorageOrderLine> getSuggestedStorageOrderLine(String merchantId, String ean) {
        Optional<ExternalVariant> externalVariant = externalVariantRepository.findByMerchantIdAndEan(merchantId, ean);
        return externalVariant.map(this::convertToStorageOrderLine);
    }

    /**
     *  get variant information by ean, and fill StorageOrderLine data for existing variants in Storage Order
     * @param merchantId
     * @param ean
     * @return
     */
    public Optional<StorageOrderLine> getVariantInfoByEan(String merchantId, String ean) {
        List<VariantTable> variantTableList = internalVariantService.getByEan(ean);

        if (!variantTableList.isEmpty()) {
            VariantTable variantTable = variantTableList.get(0);
            StorageOrderLine storageOrderLine = convertToStorageOrderLine(variantTable);
            // FIXME: should I verify the merchantId for ean
//            if (inventoryLevelRepository.findByMerchantIdAndVariantId(merchantId, variantTable.getVariantId()).isPresent()) {
//                storageOrderLine.setMerchantId(merchantId);
//                storageOrderLine.setGtinEan(ean);
//                return Optional.of(storageOrderLine);
//            }
            return Optional.of(storageOrderLine);
        }
        
        return Optional.empty();
    }

    private StorageOrderLine convertToStorageOrderLine(ExternalVariant externalVariant) {
        StorageOrderLine storageOrderLine = new StorageOrderLine();
        storageOrderLine.setMerchantId(externalVariant.getId().getMerchantId());
        storageOrderLine.setGtinEan(externalVariant.getEan());

        // Parse metadata to get product details
        if (externalVariant.getMetadata() != null) {
            Map<String, Object> metadata = externalVariant.getMetadata();

            // Extract basic product details
            String titleProduct = (String) metadata.get("title");
            String vendor = (String) metadata.get("vendor");

            // Map product details to existing fields
            storageOrderLine.setProductBrand(vendor);

            // Extract variant details
            List<Map<String, Object>> variants = (List<Map<String, Object>>) metadata.get("variants");
            if (variants != null && !variants.isEmpty()) {
                // Find the matching variant based on variantId
                String variantId = externalVariant.getId().getVariantId();

                Map<String, Object> matchingVariant = variants.stream()
                        .filter(variant -> {
                            String variantIdStr = (String) variant.get("id");
                            return variantIdStr != null && variantIdStr.equals(variantId);
                        })
                        .findFirst()
                        .orElse(null);

                if (matchingVariant != null) {
                    String price = (String) matchingVariant.get("price");
                    String variantSize = (String) matchingVariant.get("title");
                    String displayName = "";

                    if (matchingVariant.get("displayName") != null) {
                        displayName = (String) matchingVariant.get("displayName");
                    } else {
                        displayName = titleProduct + " - " + variantSize;
                    }

                    storageOrderLine.setPrice(price);
                    storageOrderLine.setShopifyVariantId(variantId);
                    storageOrderLine.setSize(variantSize);
                    storageOrderLine.setProductName(displayName);
                }
            }
        }

        return storageOrderLine;
    }
    
    private StorageOrderLine convertToStorageOrderLine(VariantTable variantTable) {
        StorageOrderLine storageOrderLine = new StorageOrderLine();
        
        // Set basic properties
        storageOrderLine.setProductName(variantTable.getProductName());
        storageOrderLine.setProductBrand(variantTable.getVendor());
        storageOrderLine.setSize(variantTable.getSize());
        storageOrderLine.setPrice(variantTable.getPrice());
        
        // Process metadata if available
        if (variantTable.getMetadata() != null) {
            Map<String, Object> metadata = variantTable.getMetadata();
            
            // Set product number from metadata
            if (metadata.containsKey("product_number")) {
                storageOrderLine.setProductNumber((String) metadata.get("product_number"));
            }
            
            // Set country of origin
            if (metadata.containsKey("country_of_origin")) {
                storageOrderLine.setCountryOfOrigin((String) metadata.get("country_of_origin"));
            }
            
            // Set product type and group if available from product_type list
            if (variantTable.getProductType() != null && !variantTable.getProductType().isEmpty()) {
                storageOrderLine.setProductType(variantTable.getProductType().get(0));
            }
            
            if (variantTable.getProductGroup() != null && !variantTable.getProductGroup().isEmpty()) {
                storageOrderLine.setProductGroup(variantTable.getProductGroup().get(0));
            }
            
            // Extract dimensions/weight if available
            if (metadata.containsKey("weight_gram")) {
                Object weightObj = metadata.get("weight_gram");
                if (weightObj instanceof Number) {
                    storageOrderLine.setWeight(String.valueOf(weightObj));
                } else if (weightObj instanceof String) {
                    storageOrderLine.setWeight((String) weightObj);
                }
            }
        }
        
        return storageOrderLine;
    }
}