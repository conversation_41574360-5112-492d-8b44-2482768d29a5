package com.footway.inventory.service;

import com.footway.inventory.repository.PurchaseOrderRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
public class PurchaseOrderService {

    private final PurchaseOrderRepository purchaseOrderRepository;

    @Autowired
    public PurchaseOrderService(PurchaseOrderRepository purchaseOrderRepository) {
        this.purchaseOrderRepository = purchaseOrderRepository;
    }

    public ResponseEntity<Map<String, Object>> getPurchaseOrderHeadByOrderNumber(String orderNumber) {
        Map<String, Object> purchaseOrderHead = purchaseOrderRepository.findPurchaseOrderHeadByOrderNumber(orderNumber);
        return new ResponseEntity<>(purchaseOrderHead, HttpStatus.OK);
    }

    public ResponseEntity<List<Map<String, Object>>> getPurchaseOrderLinesByOrderNumber(String orderNumber) {
        List<Map<String, Object>> purchaseOrderLines = purchaseOrderRepository.findPurchaseOrderLinesByOrderNumber(orderNumber);
        return new ResponseEntity<>(purchaseOrderLines, HttpStatus.OK);
    }
}
