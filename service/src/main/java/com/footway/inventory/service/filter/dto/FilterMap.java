package com.footway.inventory.service.filter.dto;

import com.footway.inventory.service.filter.FilterType;
import lombok.ToString;

import java.util.*;
import java.util.stream.Collectors;

@ToString
public class FilterMap extends HashMap<FilterType, HashSet<String>> {

    public FilterMap(List<String> merchantId,
                     List<String> vendor,
                     List<String> department,
                     List<String> productGroup,
                     List<String> productType) {
        put(FilterType.merchantId, new HashSet<>(merchantId != null ? merchantId : Collections.emptyList()));
        put(FilterType.vendor, new HashSet<>(vendor != null ? vendor : Collections.emptyList()));
        put(FilterType.department, new HashSet<>(department != null ? department : Collections.emptyList()));
        put(FilterType.productGroup, new HashSet<>(productGroup != null ? productGroup : Collections.emptyList()));
        put(FilterType.productType, new HashSet<>(productType != null ? productType : Collections.emptyList()));
    }

    public static FilterMap empty() {
        return new FilterMap(null, null, null, null, null);
    }

    public boolean matches(CountPerGroup countPerGroup) {
        boolean merchantId = get(FilterType.merchantId).isEmpty() || get(FilterType.merchantId).contains(countPerGroup.getMerchantId());
        boolean vendor = get(FilterType.vendor).isEmpty() || get(FilterType.vendor).contains(countPerGroup.getVendor());
        boolean department = get(FilterType.department).isEmpty() || intersecting(get(FilterType.department), countPerGroup.getDepartment());
        boolean productGroup = get(FilterType.productGroup).isEmpty() || intersecting(get(FilterType.productGroup), countPerGroup.getProductGroup());
        boolean productType = get(FilterType.productType).isEmpty() || intersecting(get(FilterType.productType), countPerGroup.getProductType());

        return merchantId && vendor && department && productGroup && productType;
    }

    private boolean intersecting(HashSet<String> strings, List<String> department) {
        return department.stream().anyMatch(strings::contains);
    }

    public FilterMap copyExcept(FilterType... except) {
        return copyExcept(Arrays.stream(except).collect(Collectors.toSet()));
    }

    public FilterMap copyExcept(Set<FilterType> except) {
        FilterMap copy = empty();
        for (FilterType type : FilterType.values()) {
            if (!except.contains(type)) {
                copy.get(type).addAll(this.get(type));
            }
        }
        return copy;
    }

    public boolean isEmpty() {
        for(Set<String> values : this.values()){
            if(!values.isEmpty()) {
                return false;
            }
        }
        return true;
    }

    public Map<FilterType, FilterMap> createPermutations(AllCounterContainer allCounterContainer) {
        Map<FilterType, FilterMap> permutations = new HashMap<>();
        for (FilterType type : FilterType.values()) {
            permutations.put(type, copyExcept(type));
        }
        return permutations;
    }


    public Map<FilterType, FilterMap> createPermutationsAdvanced(AllCounterContainer allCounterContainer) {
        Map<FilterType, FilterMap> permutations = new HashMap<>();
        for (FilterType type : FilterType.values()) {
            if (!get(type).isEmpty()) {
                Set<String> specificTypeFilterTags = get(type);
                Set<String> remainingTagsForType = new HashSet<>(allCounterContainer.getPossibleValues(type));
                remainingTagsForType.removeAll(specificTypeFilterTags);

                FilterMap permutation = copyExcept(type);
                permutation.get(type).addAll(remainingTagsForType);

                permutations.put(type, permutation);
            }
        }
        return permutations;
    }
}
