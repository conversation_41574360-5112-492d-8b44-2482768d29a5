package com.footway.inventory.service;

import com.footway.inventory.model.dao.ExternalVariant;
import com.footway.inventory.model.dto.ExternalVariantResponse;
import com.footway.inventory.model.dto.PaginatedExternalVariantResponse;
import com.footway.inventory.repository.jpa.ExternalVariantRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

@Slf4j
@Service
public class ExternalVariantService {

    private final ExternalVariantRepository externalVariantRepository;
    private final VariantMappingService variantMappingService;

    public ExternalVariantService(ExternalVariantRepository externalVariantRepository, VariantMappingService variantMappingService) {
        this.externalVariantRepository = externalVariantRepository;
        this.variantMappingService = variantMappingService;
    }

    public PaginatedExternalVariantResponse getExternalVariants(String merchantId, Integer page, Integer pageSize) {
        try {
            LocalDateTime endDate = LocalDateTime.now();
            LocalDateTime startDate = endDate.minusHours(24);
            Page<ExternalVariant> results = externalVariantRepository
                    .findByIdMerchantIdAndUpdatedWithin24Hours(merchantId, startDate, endDate, PageRequest.of(page - 1, pageSize));

            PaginatedExternalVariantResponse response = new PaginatedExternalVariantResponse();
            response.setItems(results.getContent().stream()
                    .map(this::convertToResponse)
                    .toList());
            response.setTotalItems((int) results.getTotalElements());
            response.setCurrentPage(results.getNumber() + 1);
            response.setTotalPages(results.getTotalPages());

            return response;
        } catch (Exception e) {
            log.error("Error fetching external variants for merchant {}: ", merchantId, e);
            return null;
        }
    }

    public List<ExternalVariantResponse> getExternalVariantsByDisplayNameAndMerchantId(String searchTerm, String merchantId) {

        if (searchTerm.startsWith("id:")) {
            // Extract the numeric ID
            String variantId = searchTerm.substring(3);
            // Verify the variantId is pure numeric
            if (variantId.matches("\\d+")) {
                // Perform exact ID search
                return externalVariantRepository.findByMerchantIdAndVariantId(merchantId, variantId)
                        .stream()
                        .filter(ev -> variantMappingService.notExistMappingForMerchantIdAndIntegrationId(ev.getId().getMerchantId(), ev.getId().getVariantId()))
                        .map(this::convertToResponse)
                        .toList();
            }
            // If not numeric, fall through to the else block
        }

        return externalVariantRepository.findByIdMerchantIdAndDisplayName(merchantId, searchTerm)
                .stream()
                .filter(ev -> variantMappingService.notExistMappingForMerchantIdAndIntegrationId(ev.getId().getMerchantId(), ev.getId().getVariantId()))
                .map(this::convertToResponse)
                .toList();
    }

    private ExternalVariantResponse convertToResponse(ExternalVariant variant) {
        ExternalVariantResponse response = new ExternalVariantResponse();
        response.setMerchantId(variant.getId().getMerchantId());
        response.setVariantId(variant.getId().getVariantId());
        response.setDisplayName(variant.getDisplayName());
        response.setSku(variant.getSku());
        response.setEan(variant.getEan());
        response.setImageUrl(variant.getImageUrl());
        response.setMetadata(variant.getMetadata());
        return response;
    }

}
