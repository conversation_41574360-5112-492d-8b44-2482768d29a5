package com.footway.inventory.service.filter.dto;

import com.footway.inventory.service.filter.FilterType;

import java.util.HashMap;
import java.util.Map;

public class CountsPerTagForTypeMap extends HashMap<String, TypeList> {
    private final FilterType type;
    public CountsPerTagForTypeMap(FilterType type) {
        this.type = type;
    }

    public TypeList access(String tagName) {
        if(!containsKey(tagName)) {
            put(tagName, new TypeList(type, tagName));
        }
        return get(tagName);
    }

    public Map<String, Integer> count(FilterMap filters) {
        Map<String, Integer> counts = new HashMap<>();
        for(Map.Entry<String, TypeList> entry : entrySet())  {
            counts.put(entry.getKey(), entry.getValue().count(filters));
        }
        return counts;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("{");
        for (Map.Entry<String, TypeList> entry : this.entrySet()) {
            sb.append(entry.getKey())
                    .append("=")
                    .append(entry.getValue().toString())
                    .append(", ");
        }
        if (sb.length() > 1) {
            sb.setLength(sb.length() - 2); // Remove the trailing ", "
        }
        sb.append("}");
        return sb.toString();
    }
}
