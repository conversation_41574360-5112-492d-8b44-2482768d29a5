package com.footway.inventory.service;

import com.footway.inventory.component.PratClient;
import com.footway.inventory.exception.VariantIdentifiersNullException;
import com.footway.inventory.model.dao.*;
import com.footway.inventory.model.dto.*;
import com.footway.inventory.model.dto.variant.mapping.missing.CreateVariantMappingMissingRequest;
import com.footway.inventory.model.dto.variant.mapping.missing.VariantMappingMissing;
import com.footway.inventory.repository.jpa.ExternalVariantRepository;
import com.footway.inventory.repository.jpa.VariantMappingMissingRepository;
import com.footway.inventory.repository.jpa.VariantMappingRepository;
import com.footway.inventory.repository.jpa.VariantMappingSuggestedRepository;
import jakarta.persistence.criteria.Predicate;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.query.criteria.HibernateCriteriaBuilder;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;


@Slf4j
@Service
public class VariantMappingService {
    private final VariantMappingRepository variantMappingRepository;
    private final VariantMappingMissingRepository variantMappingMissingRepository;
    private final VariantMappingSuggestedRepository variantMappingSuggestedRepository;
    private final PratClient pratClient;
    private final ExternalVariantRepository externalVariantRepository;

    @Autowired
    VariantMappingService(
            VariantMappingRepository variantMappingRepository,
            VariantMappingMissingRepository variantMappingMissingRepository,
            VariantMappingSuggestedRepository variantMappingSuggestedRepository,
            PratClient pratClient, ExternalVariantRepository externalVariantRepository) {
        this.variantMappingRepository = variantMappingRepository;
        this.variantMappingMissingRepository = variantMappingMissingRepository;
        this.variantMappingSuggestedRepository = variantMappingSuggestedRepository;
        this.pratClient = pratClient;
        this.externalVariantRepository = externalVariantRepository;
    }

    public VariantMapping createVariantMapping(CreateVariantMappingRequest createVariantMappingRequest) {
        long count = variantMappingRepository.countByMerchantCodeAndBothVariantIdsWithCase(createVariantMappingRequest.getMerchantCode(), createVariantMappingRequest.getIntegrationVariantId(),
                createVariantMappingRequest.getFootwayVariantId());
        if (count == 0) {
            try {
                VariantMappingTable mappingTable = new VariantMappingTable(createVariantMappingRequest.getMerchantCode(), createVariantMappingRequest.getIntegrationVariantId(),
                        createVariantMappingRequest.getFootwayVariantId()
                );

                variantMappingRepository.save(mappingTable);
                pratClient.sendVariantInventorySNS(createVariantMappingRequest.getFootwayVariantId());
                return mappingTable.toVariantMapping();

            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        } else {
            return null;
        }
    }

    public List<VariantMapping> upsertVariantMapping(String merchantCode, List<VariantMappingUpdateModel> variantMappingUpdateModels) {
        String normalizedMerchantCode = merchantCode.toUpperCase();
        List<VariantMapping> conflictMappings = new ArrayList<>();

        for (VariantMappingUpdateModel updateModel : variantMappingUpdateModels) {
            String normalizedFootwayVariantId = updateModel.getFootwayVariantId().toLowerCase();
            String normalizedIntegrationVariantId = updateModel.getIntegrationVariantId().toLowerCase();

            List<VariantMappingTable> existingMappingsByFootwayVariantId = variantMappingRepository.findByMerchantCodeAndFootwayVariantIdWithCase(
                    normalizedMerchantCode, normalizedFootwayVariantId);

            List<VariantMappingTable> existingMappingsByIntegrationVariantId = variantMappingRepository.findByMerchantCodeAndIntegrationVariantIdWithCase(normalizedMerchantCode, normalizedIntegrationVariantId);

            List<VariantMappingTable> existingMappings = Stream.concat(
                            existingMappingsByFootwayVariantId.stream(),
                            existingMappingsByIntegrationVariantId.stream())
                    .distinct()
                    .toList();

            if (existingMappings.isEmpty()) {
                VariantMappingTable newMapping = new VariantMappingTable(
                        normalizedMerchantCode, normalizedIntegrationVariantId, normalizedFootwayVariantId);

                variantMappingRepository.save(newMapping).toVariantMapping();

                try {
                    Optional<VariantMappingMissingTable> missingMapping = variantMappingMissingRepository.findByMerchantCodeAndFootwayVariantIdAndIntegrationVariantId(normalizedMerchantCode, normalizedFootwayVariantId, normalizedIntegrationVariantId);
                    if (missingMapping.isPresent()) {
                        boolean removed = removeVariantMapping(missingMapping.get().getVariantMappingId());
                        if (removed)
                            log.info("Remove missing mapping for newly mapped footway variant {} of {}", normalizedFootwayVariantId, normalizedMerchantCode);
                        else
                            log.warn("Tried but failed to remove missing mapping for newly mapped footway variant {} of {}", normalizedFootwayVariantId, normalizedMerchantCode);
                    }
                } catch (RuntimeException e) {
                    log.warn("Tried but failed to view or remove missing mapping for newly mapped footway variant {} of {}", normalizedFootwayVariantId, normalizedMerchantCode);
                }


            } else if (existingMappings.size() == 1) {
                VariantMappingTable existingMapping = existingMappings.stream().findFirst().orElse(null);
                existingMapping.setFootwayVariantId(normalizedFootwayVariantId);
                existingMapping.setIntegrationVariantId(normalizedIntegrationVariantId);
                conflictMappings.add(existingMapping.toVariantMapping());
            } else {
                throw new RuntimeException("Cannot update because there are multiple integration variants for footway variant " + normalizedFootwayVariantId);
            }

            pratClient.sendVariantInventorySNS(normalizedFootwayVariantId);
        }

        return conflictMappings;
    }

    public List<VariantMapping> confirmConflictedVariantMappings(String merchantCode, List<VariantMappingUpdateModel> variantMappingUpdateModels) {
        String normalizedMerchantCode = merchantCode.toUpperCase();
        List<VariantMapping> updatedMappings = new ArrayList<>();

        for (VariantMappingUpdateModel updateModel : variantMappingUpdateModels) {
            String normalizedFootwayVariantId = updateModel.getFootwayVariantId().toLowerCase();
            String normalizedIntegrationVariantId = updateModel.getIntegrationVariantId().toLowerCase();

            List<VariantMappingTable> existingMappingsByFootwayVariantId = variantMappingRepository.findByMerchantCodeAndFootwayVariantIdWithCase(
                    normalizedMerchantCode, normalizedFootwayVariantId);

            List<VariantMappingTable> existingMappingsByIntegrationVariantId = variantMappingRepository.findByMerchantCodeAndIntegrationVariantIdWithCase(normalizedMerchantCode, normalizedIntegrationVariantId);

            List<VariantMappingTable> existingMappings = Stream.concat(
                            existingMappingsByFootwayVariantId.stream(),
                            existingMappingsByIntegrationVariantId.stream())
                    .distinct()
                    .toList();

            if (existingMappings.size() == 1) {
                VariantMappingTable existingMapping = existingMappings.stream().findFirst().orElse(null);
                existingMapping.setFootwayVariantId(normalizedFootwayVariantId);
                existingMapping.setIntegrationVariantId(normalizedIntegrationVariantId);
                VariantMappingTable savedMapping = variantMappingRepository.save(existingMapping);
                updatedMappings.add(savedMapping.toVariantMapping());

                try {
                    Optional<VariantMappingMissingTable> missingMapping = variantMappingMissingRepository.findByMerchantCodeAndFootwayVariantIdAndIntegrationVariantId(normalizedMerchantCode, normalizedFootwayVariantId, normalizedIntegrationVariantId);
                    if (missingMapping.isPresent()) {
                        boolean removed = removeVariantMapping(missingMapping.get().getVariantMappingId());
                        if (removed)
                            log.info("Remove missing mapping for newly mapped footway variant {} and integration variant {} of {}", normalizedFootwayVariantId, normalizedIntegrationVariantId, normalizedMerchantCode);
                        else
                            log.warn("Tried but failed to remove missing mapping for newly mapped footway variant {} and integration variant {} of {}", normalizedFootwayVariantId, normalizedIntegrationVariantId, normalizedMerchantCode);
                    }
                } catch (RuntimeException e) {
                    log.warn("Tried but failed to view or remove missing mapping for newly mapped footway variant {} and integration variant {} of {}", normalizedFootwayVariantId, normalizedIntegrationVariantId, normalizedMerchantCode);
                }
            } else {
                throw new RuntimeException("Cannot update because there are multiple integration variants for footway variant " + normalizedFootwayVariantId);
            }
            pratClient.sendVariantInventorySNS(normalizedFootwayVariantId);
        }

        return updatedMappings;
    }

    public PaginatedVariantMappingSuggestedResponse getPaginatedVariantMappingSuggestedByMerchantId(String merchantId, Integer page, Integer pageSize) {
        try {
            Page<VariantMappingSuggested> results = variantMappingSuggestedRepository.findByMerchantIdAndStatus(merchantId, "suggested", PageRequest.of(page - 1, pageSize));

            List<VariantMappingSuggestedResponse> mappingResponses = results.getContent().stream()
                    .map(suggested -> {
                        VariantMappingSuggestedResponse response = new VariantMappingSuggestedResponse();
                        response.setMerchantId(suggested.getMerchantId());
                        response.setFootwayVariantId(suggested.getFootwayVariantId());
                        response.setIntegrationVariantId(suggested.getIntegrationVariantId());

                        // Set footway variant details if available
                        if (suggested.getFootwayVariant() != null) {
                            if (suggested.getFootwayVariant().getSize() != null) {
                                response.setFootwayProductName(suggested.getFootwayVariant().getProductName() + " - " + suggested.getFootwayVariant().getSize());
                            } else {
                                response.setFootwayProductName(suggested.getFootwayVariant().getProductName());
                            }

                            // Try to get image URL from getImageUrl() first, then fall back to metadata
                            String footwayImageUrl = suggested.getFootwayVariant().getImageUrl();
                            if (footwayImageUrl == null && suggested.getFootwayVariant().getMetadata() != null) {
                                Map<String, Object> metadata = suggested.getFootwayVariant().getMetadata();
                                if (metadata.containsKey("images") && metadata.get("images") instanceof List) {
                                    Object imagesObj = metadata.get("images");
                                    if (imagesObj instanceof List<?> && !((List<?>) imagesObj).isEmpty()) {
                                        Object firstImage = ((List<?>) imagesObj).get(0);
                                        if (firstImage instanceof Map<?, ?> && ((Map<?, ?>) firstImage).containsKey("url")) {
                                            footwayImageUrl = (String) ((Map<?, ?>) firstImage).get("url");
                                        }
                                    }
                                }
                            }
                            response.setFootwayImageUrl(footwayImageUrl);

                            if (suggested.getFootwayVariant().getEan() != null) {
                                response.setFootwayEan(suggested.getFootwayVariant().getEan());
                            }
                        }

                        // Set integration variant details if available
                        if (suggested.getIntegrationVariant() != null) {
                            response.setIntegrationProductName(suggested.getIntegrationVariant().getDisplayName());
                            if (suggested.getIntegrationVariant().getMetadata() != null) {
                                Map<String, Object> metadata = suggested.getIntegrationVariant().getMetadata();
                                // Try to get image URL from root level "images" array first
                                if (metadata.containsKey("images") && metadata.get("images") instanceof List) {
                                    Object imagesObj = metadata.get("images");
                                    if (imagesObj instanceof List<?> && !((List<?>) imagesObj).isEmpty()) {
                                        Object firstImage = ((List<?>) imagesObj).get(0);
                                        if (firstImage instanceof Map<?, ?>) {
                                            Map<?, ?> imageMap = (Map<?, ?>) firstImage;
                                            if (imageMap.containsKey("url")) {
                                                response.setIntegrationImageUrl((String) imageMap.get("url"));
                                            } else if (imageMap.containsKey("src")) {
                                                response.setIntegrationImageUrl((String) imageMap.get("src"));
                                            }
                                        }
                                    }
                                }
                                // If no image found, try the legacy format with "image" object
                                else if (metadata.containsKey("image") && metadata.get("image") instanceof Map) {
                                    Object imageObj = metadata.get("image");
                                    if (imageObj instanceof Map<?, ?> && ((Map<?, ?>) imageObj).containsKey("src")) {
                                        response.setIntegrationImageUrl((String) ((Map<?, ?>) imageObj).get("src"));
                                    }
                                }
                            }
                            response.setProductId(suggested.getIntegrationVariant().getProductId());
                            response.setEan(suggested.getIntegrationVariant().getEan());
                        }

                        return response;
                    })
                    .toList();

            PaginatedVariantMappingSuggestedResponse response = new PaginatedVariantMappingSuggestedResponse();
            response.setItems(mappingResponses);
            response.setTotalItems((int) results.getTotalElements());
            response.setCurrentPage(results.getNumber() + 1);
            response.setTotalPages(results.getTotalPages());

            return response;
        } catch (Exception e) {
            log.error("Error fetching variant mapping suggested for merchant {}: ", merchantId, e);
            return null;
        }
    }

    public boolean removeVariantMapping(Long mappingId) {
        VariantMappingTable mapping = variantMappingRepository.findById(mappingId).orElse(null);
        if (mapping != null) {
            try {
                variantMappingRepository.delete(mapping);
                return true;
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        } else {
            return false;
        }
    }

    @Cacheable("variantMappingByMappingId")
    public VariantMapping getVariantMappingById(Long mappingId) {
        return variantMappingRepository.findById(mappingId).map(VariantMappingTable::toVariantMapping).orElse(null);
    }

    @Cacheable("variantMappingByMerchantAndintegrationVariant")
    public List<VariantMapping> getVariantMappingByintegrationVariantId(String merchantCode, String integrationVariantId) {
        return variantMappingRepository.findByMerchantCodeAndIntegrationVariantIdWithCase(merchantCode, integrationVariantId).stream()
                .map(VariantMappingTable::toVariantMapping).toList();
    }

    @Cacheable("variantMappingByMerchantAndFootwayVariant")
    public List<VariantMapping> getVariantMappingByFootwayVariantId(String merchantCode, String footwayVariantId) {
        return variantMappingRepository.findByMerchantCodeAndFootwayVariantIdWithCase(merchantCode, footwayVariantId).stream()
                .map(VariantMappingTable::toVariantMapping).toList();
    }

    @Cacheable("variantMappingByMerchantAndVariants") //FIXME: need to handle that it can be lists
    public VariantMapping getVariantMappingBySomeVariantId(String merchantCode, String integrationVariantId, String footwayVariantId) { //handle null
        if (integrationVariantId == null && footwayVariantId == null) {
            throw new VariantIdentifiersNullException("Both integration variant id and footway variant id cannot be null");
        }
        if (integrationVariantId != null && footwayVariantId != null) {
            return Optional.ofNullable(variantMappingRepository.findByMerchantCodeAndIntegrationVariantIdAndFootwayVariantIdWithCase(merchantCode, integrationVariantId, footwayVariantId))
                    .map(VariantMappingTable::toVariantMapping)
                    .orElse(null);
        }

        if (integrationVariantId != null && footwayVariantId == null) {
            List<VariantMapping> result = variantMappingRepository.findByMerchantCodeAndIntegrationVariantIdWithCase(merchantCode, integrationVariantId).stream()
                    .map(VariantMappingTable::toVariantMapping).toList();
            return result.stream().findFirst().orElse(null);
        }

        if (integrationVariantId == null && footwayVariantId != null) {
            List<VariantMapping> result = variantMappingRepository.findByMerchantCodeAndFootwayVariantIdWithCase(merchantCode, footwayVariantId).stream()
                    .map(VariantMappingTable::toVariantMapping)
                    .toList();
            return result.stream().findFirst().orElse(null);
        }

        throw new RuntimeException("Impossible combination");
    }

    public List<VariantMapping> getVariantMappingsByMerchantCode(String merchantCode) {
        return variantMappingRepository.findByMerchantCodeWithCase(merchantCode).stream().map(VariantMappingTable::toVariantMapping).toList();
    }

    private VariantMappingMissing daoToDTO(VariantMappingMissingTable table, ExternalVariant externalVariant) {
        VariantMappingMissing mapping = new VariantMappingMissing();
        mapping.setMerchantCode(table.getMerchantCode());
        mapping.setIntegrationVariantId(table.getIntegrationVariantId());
        mapping.setFootwayVariantId(table.getFootwayVariantId());
        mapping.setCause(table.getCause());
        mapping.setCreatedAt(table.getCreatedAt().toString());
        mapping.setMappingId(table.getVariantMappingId());

        try {
            if (mapping.getIntegrationVariantId() != null) {
                if (externalVariant != null) {
                    mapping.setEan(externalVariant.getEan());
                    mapping.setSku(externalVariant.getSku());
                    mapping.setDisplayName(externalVariant.getDisplayName());
                }
            }
        } catch (RuntimeException e) {
            log.error("Trouble extracting externalVariant data for integration variant {}", mapping.getIntegrationVariantId(), e);
        }

        return mapping;
    }

    private VariantMappingMissing daoToDTO(VariantMappingMissingTable table) {
        VariantMappingMissing mapping = new VariantMappingMissing();
        mapping.setMerchantCode(table.getMerchantCode());
        mapping.setIntegrationVariantId(table.getIntegrationVariantId());
        mapping.setFootwayVariantId(table.getFootwayVariantId());
        mapping.setCause(table.getCause());
        mapping.setCreatedAt(table.getCreatedAt().toString());
        mapping.setMappingId(table.getVariantMappingId());

        try {
            if (mapping.getIntegrationVariantId() != null) {
                @NotNull Optional<ExternalVariant> externalVariant = externalVariantRepository.findById(new ExternalVariantId(mapping.getMerchantCode(), mapping.getIntegrationVariantId()));
                if(externalVariant.isPresent()) {
                    ExternalVariant variant = externalVariant.get();
                    mapping.setEan(variant.getEan());
                    mapping.setSku(variant.getSku());
                    mapping.setDisplayName(variant.getDisplayName());
                }
            }
        }catch (RuntimeException e) {
            log.error("Trouble extracting externalVariant data for integration variant {}", mapping.getIntegrationVariantId(), e);
        }

        return mapping;
    }

    public VariantMappingMissing removeVariantMappingMissing(Long mappingId) {
        VariantMappingMissingTable mapping = variantMappingMissingRepository.findById(mappingId).orElse(null);
        if (mapping != null) {
            try {
                variantMappingMissingRepository.delete(mapping);
                return daoToDTO(mapping);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        } else {
            return null;
        }
    }


    public List<VariantMappingMissingTable> search(String merchantCode,
                                                   LocalDateTime before,
                                                   LocalDateTime after,
                                                   String cause) {
        Specification<VariantMappingMissingTable> spec = (root, query, cb) -> {
            // Ensure the CriteriaBuilder is a HibernateCriteriaBuilder
            HibernateCriteriaBuilder hibernateCb = (HibernateCriteriaBuilder) cb;
            List<Predicate> predicates = new ArrayList<>();

            // Apply merchantCode filter
            if (merchantCode != null) {
                predicates.add(cb.equal(root.get("merchantCode"), merchantCode));
            }

            // Apply before date filter
            if (before != null) {
                predicates.add(cb.lessThan(root.get("createdAt"), before));
            }

            // Apply after date filter
            if (after != null) {
                predicates.add(cb.greaterThan(root.get("createdAt"), after));
            }

            // Apply cause filter
            if (cause != null) {
                predicates.add(cb.equal(root.get("cause"), cause));
            }

            return cb.and(predicates.toArray(new Predicate[0]));
        };

        return variantMappingMissingRepository.findAll(spec, Sort.by("createdAt"));
    }


    public List<VariantMappingMissing> getVariantMappingsMissing(String merchantCode, String cause, LocalDate before, LocalDate after) {
        LocalDateTime beforeDateTime = before != null ? before.atStartOfDay() : null;
        LocalDateTime afterDateTime = after != null ? after.atTime(LocalTime.MAX) : null;

        List<VariantMappingMissingTable> result = search(merchantCode, beforeDateTime, afterDateTime, cause);
        Map<ExternalVariantId, ExternalVariant> externalVariants = getExternalVariants(result);
        List<VariantMappingMissing> missing = new ArrayList<>();
        for (VariantMappingMissingTable item : result) {
            missing.add(daoToDTO(item, externalVariants.get(new ExternalVariantId(item.getMerchantCode(), item.getIntegrationVariantId()))));
        }
        return missing;
    }

    private Map<ExternalVariantId, ExternalVariant> getExternalVariants(List<VariantMappingMissingTable> result) {
        List<ExternalVariantId> ids = result.stream().map(missing -> new ExternalVariantId(missing.getMerchantCode(), missing.getIntegrationVariantId())).toList();
        List<ExternalVariant> variants = externalVariantRepository.findAllById(ids::iterator);
        return variants.stream().collect(Collectors.toMap(ExternalVariant::getId, Function.identity()));
    }

    public VariantMappingMissing createVariantMappingMissing(CreateVariantMappingMissingRequest request) {
        try {
            String merchantID = request.getMerchantCode().toUpperCase();
            String integrationVariantId = request.getIntegrationVariantId();
            String footwayVariantId = request.getFootwayVariantId();

            Optional<VariantMappingMissingTable> existingMapping = variantMappingMissingRepository.findByMerchantCodeAndFootwayVariantIdAndIntegrationVariantId(merchantID, footwayVariantId, integrationVariantId);
            if (existingMapping.isPresent()) {
                return daoToDTO(existingMapping.get());
            } else {
                VariantMappingMissingTable mappingTable = new VariantMappingMissingTable(merchantID, integrationVariantId, footwayVariantId, request.getCause());
                VariantMappingMissingTable persisted = variantMappingMissingRepository.save(mappingTable);
                return daoToDTO(persisted);
            }
        } catch (Exception e) {
            return null;
        }
    }

    public String getSkuFromVariant(String variantId) { //FIXME: maybe persist locally to offload PRAT
        ResponseEntity<Variant> response = pratClient.getPratVariantByVariantId(variantId);
        if (response.getStatusCode().is2xxSuccessful()) {
            log.info("Successfully fetched variant details for variantId: {}", variantId);
            try {
                return Objects.requireNonNull(response.getBody()).getVariantNumber();
            } catch (RuntimeException e) {
                throw new RuntimeException("Error processing JSON response for variantId: " + variantId, e);
            }
        } else if (response.getStatusCode().isSameCodeAs(HttpStatus.NOT_FOUND)) {
            return null;
        } else {
            throw new RuntimeException("Failed to fetch variant details for variantId: " + variantId);
        }
    }

    public boolean notExistMappingForMerchantIdAndIntegrationId(String merchantCode, String integrationId) {
        List<VariantMappingTable> mappingList = variantMappingRepository.findByMerchantIdAndIntegrationVariantId(merchantCode, integrationId);
        return mappingList.isEmpty();
    }
}