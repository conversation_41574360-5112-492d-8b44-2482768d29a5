package com.footway.inventory.service.filter.dto;

import com.footway.inventory.service.filter.FilterType;

import java.util.*;

public class AllCounterContainer extends HashMap<FilterType, CountsPerTagForTypeMap> {
    public AllCounterContainer(List<CountPerGroup> filterCounters) {
        for (FilterType type : FilterType.values()) {
            this.put(type, new CountsPerTagForTypeMap(type));
        }

        filterCounters.forEach(item -> {
            Optional.ofNullable(item.getMerchantId())
                    .ifPresent(merchantId ->
                            this.get(FilterType.merchantId).access(merchantId).add(item)
                    );
            Optional.ofNullable(item.getVendor())
                    .ifPresent(vendor ->
                            this.get(FilterType.vendor).access(vendor).add(item)
                    );
            Optional.ofNullable(item.getDepartment())
                    .ifPresent(departments ->
                            departments.forEach(subItem ->
                                    this.get(FilterType.department).access(subItem).add(item)
                            )
                    );
            Optional.ofNullable(item.getProductGroup())
                    .ifPresent(productGroups ->
                            productGroups.forEach(subItem ->
                                    this.get(FilterType.productGroup).access(subItem).add(item)
                            )
                    );
            Optional.ofNullable(item.getProductType())
                    .ifPresent(productTypes ->
                            productTypes.forEach(subItem ->
                                    this.get(FilterType.productType).access(subItem).add(item)
                            )
                    );
        });
    }

    public Set<String> getPossibleValues(FilterType type) {
        return get(type).keySet();
    }

    public int totalCount() {
        return count(FilterMap.empty())
                .get(FilterType.merchantId)
                .values()
                .stream()
                .mapToInt(Integer::intValue)
                .sum();
    }

    public Map<FilterType, Map<String, Integer>> count(FilterMap filters) {
        Map<FilterType, Map<String, Integer>> counts = new HashMap<>();
        for( Map.Entry<FilterType, CountsPerTagForTypeMap> entry: this.entrySet()) {
            counts.put(entry.getKey(), entry.getValue().count(filters));
        }
        return counts;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("{");
        for (Map.Entry<FilterType, CountsPerTagForTypeMap> entry : this.entrySet()) {
            sb.append(entry.getKey().toString())
                    .append("=")
                    .append(entry.getValue().toString())
                    .append(", ");
        }
        if (sb.length() > 1) {
            sb.setLength(sb.length() - 2); // Remove the trailing ", "
        }
        sb.append("}");
        return sb.toString();
    }
}
