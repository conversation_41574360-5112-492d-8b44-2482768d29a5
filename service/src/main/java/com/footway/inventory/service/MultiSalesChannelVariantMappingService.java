package com.footway.inventory.service;

import com.footway.inventory.component.PratClient;
import com.footway.inventory.exception.VariantIdentifiersNullException;
import com.footway.inventory.model.dao.MultiSalesChannelVariantMapping;
import com.footway.inventory.model.dao.MultiSalesChannelVariantMappingSuggested;
import com.footway.inventory.model.dto.*;
import com.footway.inventory.model.dto.KeySetCursor;
import com.footway.inventory.repository.jpa.MscVariantMappingsRepository;
import com.footway.inventory.repository.jpa.MultiSalesChannelVariantMappingRepository;
import com.footway.inventory.repository.jpa.MultiSalesChannelVariantMappingSuggestedRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Slice;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Slf4j
@Service
public class MultiSalesChannelVariantMappingService {
    private static final int BATCH_SIZE = 100;
    private final MultiSalesChannelVariantMappingRepository multiSalesChannelVariantMappingRepository;
    private final MscVariantMappingsRepository mscVariantMappingsRepository;
    private final PratClient pratClient;
    private final MultiSalesChannelVariantMappingSuggestedRepository multiSalesChannelVariantMappingSuggestedRepository;

    @Autowired
    MultiSalesChannelVariantMappingService(MultiSalesChannelVariantMappingRepository variantMappingRepository, MscVariantMappingsRepository mscVariantMappingsRepository, PratClient pratClient, MultiSalesChannelVariantMappingSuggestedRepository multiSalesChannelVariantMappingSuggestedRepository) {
        this.multiSalesChannelVariantMappingRepository = variantMappingRepository;
        this.mscVariantMappingsRepository = mscVariantMappingsRepository;
        this.pratClient = pratClient;
        this.multiSalesChannelVariantMappingSuggestedRepository = multiSalesChannelVariantMappingSuggestedRepository;
    }

    public VariantMapping createVariantMapping(CreateVariantMappingRequest createVariantMappingRequest) {
        long count = multiSalesChannelVariantMappingRepository.countByMerchantCodeAndBothVariantIdsWithCase(createVariantMappingRequest.getMerchantCode(), createVariantMappingRequest.getIntegrationVariantId(),
                createVariantMappingRequest.getFootwayVariantId());
        if (count == 0) {
            try {
                MultiSalesChannelVariantMapping mappingTable = new MultiSalesChannelVariantMapping(createVariantMappingRequest.getMerchantCode(), createVariantMappingRequest.getIntegrationVariantId(),
                        createVariantMappingRequest.getFootwayVariantId()
                );

                multiSalesChannelVariantMappingRepository.save(mappingTable);

                try {
                    pratClient.sendVariantInventorySNS(createVariantMappingRequest.getFootwayVariantId());
                } catch (RuntimeException e) {
                    //if we fail, we fail!
                }

                // Find by unique combination
                Optional<MultiSalesChannelVariantMappingSuggested> mapping = multiSalesChannelVariantMappingSuggestedRepository.findByMerchantIdAndFootwayVariantIdAndIntegrationVariantId(
                        createVariantMappingRequest.getMerchantCode(), createVariantMappingRequest.getFootwayVariantId(), createVariantMappingRequest.getIntegrationVariantId());

                if (mapping.isPresent()) {
                    mapping.get().setStatus("mapped");
                    multiSalesChannelVariantMappingSuggestedRepository.save(mapping.get());
                }

                return mappingTable.toVariantMapping();

            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        } else {
            return null;
        }
    }

    public List<VariantMapping> upsertVariantMapping(String merchantCode, List<VariantMappingUpdateModel> variantMappingUpdateModels) {
        String normalizedMerchantCode = merchantCode.toUpperCase();
        List<VariantMapping> updatedMappings = new ArrayList<>();

        for (VariantMappingUpdateModel updateModel : variantMappingUpdateModels) {
            String normalizedFootwayVariantId = updateModel.getFootwayVariantId().toLowerCase();
            String normalizedintegrationVariantId = updateModel.getIntegrationVariantId().toLowerCase();

            List<MultiSalesChannelVariantMapping> existingMappings = multiSalesChannelVariantMappingRepository.findByMerchantCodeAndFootwayVariantIdWithCase(
                    normalizedMerchantCode, normalizedFootwayVariantId);

            if (existingMappings.isEmpty()) {
                MultiSalesChannelVariantMapping newMapping = new MultiSalesChannelVariantMapping(
                        normalizedMerchantCode, normalizedintegrationVariantId, normalizedFootwayVariantId);
                updatedMappings.add(multiSalesChannelVariantMappingRepository.save(newMapping).toVariantMapping());
            } else if (existingMappings.size() == 1) {
                MultiSalesChannelVariantMapping existingMapping = existingMappings.stream().findFirst().orElse(null);
                existingMapping.setIntegrationVariantId(normalizedintegrationVariantId);
                existingMapping = multiSalesChannelVariantMappingRepository.save(existingMapping);
                updatedMappings.add(existingMapping.toVariantMapping());
            } else {
                throw new RuntimeException("Cannot update because there are multiple integration variants for footway variant " + normalizedFootwayVariantId);
            }
        }

        return updatedMappings;
    }

    public boolean removeVariantMapping(Long mappingId) {
        log.info("Removing variantMapping {} from MultiSalesChannelVariantMappingRepository", mappingId);
        MultiSalesChannelVariantMapping mapping = multiSalesChannelVariantMappingRepository.findById(mappingId).orElse(null);
        log.info("Removing mapping " + mapping);
        if (mapping != null) {
            try {
                multiSalesChannelVariantMappingRepository.delete(mapping);
                Optional<MultiSalesChannelVariantMappingSuggested> suggested = multiSalesChannelVariantMappingSuggestedRepository.findByMerchantIdAndFootwayVariantIdAndIntegrationVariantId(mapping.getMerchantId(), mapping.getFootwayVariantId(), mapping.getIntegrationVariantId());
                if (suggested.isPresent()) {
                    suggested.get().setStatus("suggested");
                    multiSalesChannelVariantMappingSuggestedRepository.save(suggested.get());
                }
                return true;
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        } else {
            return false;
        }
    }

    //    @Cacheable("variantMappingByMappingId")
    public VariantMapping getVariantMappingById(Long mappingId) {
        return multiSalesChannelVariantMappingRepository.findById(mappingId).map(MultiSalesChannelVariantMapping::toVariantMapping).orElse(null);
    }

    //    @Cacheable("variantMappingByMerchantAndintegrationVariantSingle")
    public VariantMapping getVariantMappingByintegrationVariantIdSingle(String merchantCode, String integrationVariantId) {
        List<VariantMapping> mappings = getVariantMappingByintegrationVariantId(merchantCode, integrationVariantId);
        if (mappings.isEmpty()) {
            throw new RuntimeException("No mapping found for integrationVariantId:" + integrationVariantId);
        } else if (mappings.size() > 1) {
            throw new RuntimeException("Too many mappings found for integrationVariantId:" + integrationVariantId);
        } else {
            return mappings.stream().findFirst().orElse(null);
        }
    }


    //    @Cacheable("variantMappingByMerchantAndintegrationVariant")
    public List<VariantMapping> getVariantMappingByintegrationVariantId(String merchantCode, String integrationVariantId) {
        return multiSalesChannelVariantMappingRepository.findByMerchantCodeAndIntegrationVariantIdWithCase(merchantCode, integrationVariantId).stream()
                .map(MultiSalesChannelVariantMapping::toVariantMapping).toList();
    }

    //    @Cacheable("variantMappingByMerchantAndFootwayVariant")
    public VariantMapping getVariantMappingByFootwayVariantIdSingle(String merchantCode, String footwayVariantId) {
        List<VariantMapping> mappings = getVariantMappingByFootwayVariantId(merchantCode, footwayVariantId);
        if (mappings.isEmpty()) {
            throw new RuntimeException("No mapping found for footwayVariantId:" + footwayVariantId);
        } else if (mappings.size() > 1) {
            throw new RuntimeException("Too many mappings found for footwayVariantId:" + footwayVariantId);
        } else {
            return mappings.stream().findFirst().orElse(null);
        }
    }

    //    @Cacheable("variantMappingByFootwayVariant")
    public List<VariantMapping> getVariantMappingByFootwayVariantId(String footwayVariantId) {
        return multiSalesChannelVariantMappingRepository.findByFootwayVariantId(footwayVariantId)
                .stream()
                .map(MultiSalesChannelVariantMapping::toVariantMapping).toList();
    }

    //    @Cacheable("variantMappingByMerchantAndFootwayVariant")
    public List<VariantMapping> getVariantMappingByFootwayVariantId(String merchantCode, String footwayVariantId) {
        return multiSalesChannelVariantMappingRepository.findByMerchantCodeAndFootwayVariantIdWithCase(merchantCode, footwayVariantId).stream()
                .map(MultiSalesChannelVariantMapping::toVariantMapping).toList();
    }

    //    @Cacheable("variantMappingByMerchantAndVariants") //FIXME: need to handle that it can be lists
    public VariantMapping getVariantMappingBySomeVariantId(String merchantCode, String integrationVariantId, String footwayVariantId) { //handle null
        if (integrationVariantId == null && footwayVariantId == null) {
            throw new VariantIdentifiersNullException("Both integration variant id and footway variant id cannot be null");
        }
        if (integrationVariantId != null && footwayVariantId != null) {
            return Optional.ofNullable(multiSalesChannelVariantMappingRepository.findByMerchantCodeAndIntegrationVariantIdAndFootwayVariantIdWithCase(merchantCode, integrationVariantId, footwayVariantId))
                    .map(MultiSalesChannelVariantMapping::toVariantMapping)
                    .orElse(null);
        }

        if (integrationVariantId != null && footwayVariantId == null) {
            List<VariantMapping> result = multiSalesChannelVariantMappingRepository.findByMerchantCodeAndIntegrationVariantIdWithCase(merchantCode, integrationVariantId).stream()
                    .map(MultiSalesChannelVariantMapping::toVariantMapping).toList();
            return result.stream().findFirst().orElse(null);
        }

        if (integrationVariantId == null && footwayVariantId != null) {
            List<VariantMapping> result = multiSalesChannelVariantMappingRepository.findByMerchantCodeAndFootwayVariantIdWithCase(merchantCode, footwayVariantId).stream()
                    .map(MultiSalesChannelVariantMapping::toVariantMapping)
                    .toList();
            return result.stream().findFirst().orElse(null);
        }

        throw new RuntimeException("Impossible combination");
    }

    public List<VariantMapping> getVariantMappingsByFootwayVariantId(String footwayVariantId) {
        return multiSalesChannelVariantMappingRepository.findByFootwayVariantId(footwayVariantId).stream().map(MultiSalesChannelVariantMapping::toVariantMapping).toList();
    }

    public List<VariantMapping> getVariantMappingsByMerchantCode(String merchantCode) {
        return multiSalesChannelVariantMappingRepository.findByMerchantCodeWithCase(merchantCode).stream().map(MultiSalesChannelVariantMapping::toVariantMapping).toList();
    }

    public PaginatedMultiSalesChannelMappingsResponse getPaginatedMultiSalesChannelVariantMappingsByMerchantId(String merchantId, Integer page, Integer pageSize) {
        try {
            Page<MultiSalesChannelMappingsProjection> results = mscVariantMappingsRepository.findMultiSalesChannelVariantMappingsByMerchantId(merchantId, PageRequest.of(page - 1, pageSize));

            List<MultiSalesChannelMappingsResponse> mappingResponses = results.getContent().stream()
                    .map(projection -> {
                        MultiSalesChannelMappingsResponse response = new MultiSalesChannelMappingsResponse();
                        response.setMerchantId(projection.getMerchantId());
                        response.setFootwayVariantId(projection.getFootwayVariantId());
                        response.setIntegrationVariantId(projection.getIntegrationVariantId());
                        response.setMscMappingId(projection.getMscMappingId());
                        
                        // Handle the footwayProduct concatenation in code instead of SQL
                        if (projection.getSize() != null) {
                            response.setFootwayProduct(projection.getProductName() + " - " + projection.getSize());
                        } else {
                            response.setFootwayProduct(projection.getProductName());
                        }
                        
                        if (projection.getMscEan() != null) {
                            response.setMscEan(List.of(projection.getMscEan()));
                        }
                        response.setFootwayImageUrl(projection.getFootwayImageUrl());
                        response.setIntegrationProduct(projection.getIntegrationProduct());
                        response.setProductId(projection.getProductId());
                        response.setEan(projection.getEan());
                        response.setIntegrationImageUrl(projection.getIntegrationImageUrl());
                        return response;
                    })
                    .toList();

            PaginatedMultiSalesChannelMappingsResponse response = new PaginatedMultiSalesChannelMappingsResponse();
            response.setItems(mappingResponses);
            response.setTotalItems((int) results.getTotalElements());
            response.setCurrentPage(results.getNumber() + 1);
            response.setTotalPages(results.getTotalPages());

            return response;
        } catch (Exception e) {
            log.error("Error fetching multi sales channel variant mappings for merchant {}: ", merchantId, e);
            return null;
        }
    }

    public PaginatedMultiSalesChannelMappingSuggestedResponse getPaginatedMultiSalesChannelVariantMappingSuggestedByMerchantId(String merchantId, Integer page, Integer pageSize) {
        try {
            Page<MultiSalesChannelVariantMappingSuggested> results = multiSalesChannelVariantMappingSuggestedRepository.findByMerchantIdAndStatus(merchantId, "suggested", PageRequest.of(page - 1, pageSize));

            List<MultiSalesChannelMappingSuggestedResponse> mappingResponses = results.getContent().stream()
                    .map(suggested -> {
                        MultiSalesChannelMappingSuggestedResponse response = new MultiSalesChannelMappingSuggestedResponse();
                        response.setMerchantId(suggested.getMerchantId());
                        response.setFootwayVariantId(suggested.getFootwayVariantId());
                        response.setIntegrationVariantId(suggested.getIntegrationVariantId());

                        // Set footway variant details if available
                        if (suggested.getFootwayVariant() != null) {
                            if (suggested.getFootwayVariant().getSize() != null) {
                                response.setFootwayProductName(suggested.getFootwayVariant().getProductName() + " - " + suggested.getFootwayVariant().getSize());
                            } else {
                                response.setFootwayProductName(suggested.getFootwayVariant().getProductName());
                            }

                            // Try to get image URL from getImageUrl() first, then fall back to metadata
                            String footwayImageUrl = suggested.getFootwayVariant().getImageUrl();
                            if (footwayImageUrl == null && suggested.getFootwayVariant().getMetadata() != null) {
                                Map<String, Object> metadata = suggested.getFootwayVariant().getMetadata();
                                if (metadata.containsKey("images") && metadata.get("images") instanceof List) {
                                    Object imagesObj = metadata.get("images");
                                    if (imagesObj instanceof List<?> && !((List<?>) imagesObj).isEmpty()) {
                                        Object firstImage = ((List<?>) imagesObj).get(0);
                                        if (firstImage instanceof Map<?, ?> && ((Map<?, ?>) firstImage).containsKey("url")) {
                                            footwayImageUrl = (String) ((Map<?, ?>) firstImage).get("url");
                                        }
                                    }
                                }
                            }
                            response.setFootwayImageUrl(footwayImageUrl);

                            if (suggested.getFootwayVariant().getEan() != null) {
                                response.setFootwayEan(suggested.getFootwayVariant().getEan());
                            }
                        }

                        // Set integration variant details if available
                        if (suggested.getIntegrationVariant() != null) {
                            response.setIntegrationProductName(suggested.getIntegrationVariant().getDisplayName());
                            if (suggested.getIntegrationVariant().getMetadata() != null) {
                                Map<String, Object> metadata = suggested.getIntegrationVariant().getMetadata();
                                // Try to get image URL from root level "images" array first
                                if (metadata.containsKey("images") && metadata.get("images") instanceof List) {
                                    Object imagesObj = metadata.get("images");
                                    if (imagesObj instanceof List<?> && !((List<?>) imagesObj).isEmpty()) {
                                        Object firstImage = ((List<?>) imagesObj).get(0);
                                        if (firstImage instanceof Map<?, ?>) {
                                            Map<?, ?> imageMap = (Map<?, ?>) firstImage;
                                            if (imageMap.containsKey("url")) {
                                                response.setIntegrationImageUrl((String) imageMap.get("url"));
                                            } else if (imageMap.containsKey("src")) {
                                                response.setIntegrationImageUrl((String) imageMap.get("src"));
                                            }
                                        }
                                    }
                                }
                                // If no image found, try the legacy format with "image" object
                                else if (metadata.containsKey("image") && metadata.get("image") instanceof Map) {
                                    Object imageObj = metadata.get("image");
                                    if (imageObj instanceof Map<?, ?> && ((Map<?, ?>) imageObj).containsKey("src")) {
                                        response.setIntegrationImageUrl((String) ((Map<?, ?>) imageObj).get("src"));
                                    }
                                }
                            }
                            response.setProductId(suggested.getIntegrationVariant().getProductId());
                            response.setEan(suggested.getIntegrationVariant().getEan());
                        }

                        return response;
                    })
                    .toList();

            PaginatedMultiSalesChannelMappingSuggestedResponse response = new PaginatedMultiSalesChannelMappingSuggestedResponse();
            response.setItems(mappingResponses);
            response.setTotalItems((int) results.getTotalElements());
            response.setCurrentPage(results.getNumber() + 1);
            response.setTotalPages(results.getTotalPages());

            return response;
        } catch (Exception e) {
            log.error("Error fetching multi sales channel variant mapping suggested for merchant {}: ", merchantId, e);
            return null;
        }
    }

    /**
     * Creates all variant mappings for a merchant asynchronously using batch processing.
     *
     * @param merchantId The merchant ID
     */
    @Async
    public void createAllMappingsByMerchant(String merchantId) {
        log.info("Starting async batch processing of mappings for merchant {}.", merchantId);

        try {
            log.info("Querying for suggested mappings for merchant {}", merchantId);

            // Get total count first
            long totalCount = multiSalesChannelVariantMappingSuggestedRepository.countByMerchantIdAndStatus(merchantId, "suggested");
            log.info("Total suggested mappings to process for merchant {}: {}", merchantId, totalCount);

            // First fetch all IDs to process in cursor-based approach
            List<Long> allSuggestedIds = multiSalesChannelVariantMappingSuggestedRepository.findIdsByMerchantIdAndStatus(merchantId, "suggested");
            log.info("Retrieved {} IDs to process for merchant {}", allSuggestedIds.size(), merchantId);

            int totalProcessed = 0;
            int batchCount = 0;

            // Process in batches of BATCH_SIZE
            for (int i = 0; i < allSuggestedIds.size(); i += BATCH_SIZE) {
                int endIndex = Math.min(i + BATCH_SIZE, allSuggestedIds.size());
                List<Long> batchIds = allSuggestedIds.subList(i, endIndex);

                log.info("Fetching batch {} of suggested mappings for merchant {} (IDs: {}-{})",
                        batchCount, merchantId, i, endIndex - 1);

                // Fetch full records for this batch of IDs
                List<MultiSalesChannelVariantMappingSuggested> batch =
                        multiSalesChannelVariantMappingSuggestedRepository.findAllById(batchIds);

                log.info("Retrieved {} mappings in batch {} for merchant {}", batch.size(), batchCount, merchantId);

                if (!batch.isEmpty()) {
                    processBatch(batch);
                    totalProcessed += batch.size();
                    log.info("Processed {}/{} mappings for merchant {} ({}%)",
                            totalProcessed, totalCount, merchantId,
                            totalCount > 0 ? (totalProcessed * 100L / totalCount) : 0);
                }

                batchCount++;
            }

            log.info("Completed processing {} total mappings for merchant {}", totalProcessed, merchantId);
        } catch (Exception e) {
            log.error("Error while creating mappings for merchant {}: {}", merchantId, e.getMessage(), e);
            throw e;
        }
    }

    protected void processBatch(List<MultiSalesChannelVariantMappingSuggested> batch) {
        for (MultiSalesChannelVariantMappingSuggested mapping : batch) {
            try {
                long count = multiSalesChannelVariantMappingRepository.countByMerchantCodeAndBothVariantIdsWithCase(
                        mapping.getMerchantId(),
                        String.valueOf(mapping.getIntegrationVariantId()),
                        mapping.getFootwayVariantId());

                if (count == 0) {
                    // Create and save the mapping
                    MultiSalesChannelVariantMapping mappingTable = new MultiSalesChannelVariantMapping(
                            mapping.getMerchantId(),
                            String.valueOf(mapping.getIntegrationVariantId()),
                            mapping.getFootwayVariantId());
                    multiSalesChannelVariantMappingRepository.save(mappingTable);

                    // Update suggested mapping status
                    mapping.setStatus("mapped");
                    multiSalesChannelVariantMappingSuggestedRepository.save(mapping);

                    // Send SNS notification
                    try {
                        pratClient.sendVariantInventorySNS(mapping.getFootwayVariantId());
                    } catch (RuntimeException e) {
                        log.warn("Failed to send SNS notification for variant {}", mapping.getFootwayVariantId(), e);
                    }
                } else {
                    // If mapping already exists, we should still mark it as processed
                    log.info("Mapping already exists for merchantId={}, footwayVariantId={}, integrationVariantId={}. Marking as mapped.",
                            mapping.getMerchantId(), mapping.getFootwayVariantId(), mapping.getIntegrationVariantId());
                    mapping.setStatus("mapped");
                    multiSalesChannelVariantMappingSuggestedRepository.save(mapping);
                }
            } catch (Exception e) {
                log.error("Error processing mapping for merchantId={}, footwayVariantId={}, integrationVariantId={}: {}",
                        mapping.getMerchantId(), mapping.getFootwayVariantId(), mapping.getIntegrationVariantId(), e.getMessage(), e);
                // Don't update status on error, so it will be retried in next run
            }
        }
    }

    /**
     * Get multi sales channel variant mappings by merchant ID using key-set pagination
     * 
     * @param keySetPaginationRequest The keySet pagination request
     * @return Paginated mappings response with cursor information
     */
    public KeySetPaginatedMappingsResponse getMultiSalesChannelVariantMappingsWithKeySetPagination(KeySetPaginationRequest keySetPaginationRequest) {
        
        try {
            // Extract values from request
            String merchantId = keySetPaginationRequest.getMerchantId();
            int limit = keySetPaginationRequest.getLimit();
            String searchTerm = keySetPaginationRequest.getSearchTerm();
            String eanFilter = keySetPaginationRequest.getEanFilter();
            
            // Extract cursor values
            String afterProductName = null;
            String afterVariantId = null;
            if (keySetPaginationRequest.getAfterCursor() != null) {
                afterProductName = keySetPaginationRequest.getAfterCursor().getProductName();
                afterVariantId = keySetPaginationRequest.getAfterCursor().getVariantId();
            }
            
            String beforeProductName = null;
            String beforeVariantId = null;
            if (keySetPaginationRequest.getBeforeCursor() != null) {
                beforeProductName = keySetPaginationRequest.getBeforeCursor().getProductName();
                beforeVariantId = keySetPaginationRequest.getBeforeCursor().getVariantId();
            }
            
            boolean isDescending = keySetPaginationRequest.getDescending();
            
            // Get results as a Slice which contains pagination information
            Slice<MultiSalesChannelMappingsProjection> resultsSlice = mscVariantMappingsRepository.findWithKeySetPagination(
                    merchantId,
                    searchTerm,
                    eanFilter,
                    afterProductName, 
                    afterVariantId, 
                    beforeProductName, 
                    beforeVariantId, 
                    isDescending, 
                    limit);
            
            List<MultiSalesChannelMappingsProjection> results = resultsSlice.getContent();
            
            List<MultiSalesChannelMappingsResponse> mappingResponses = results.stream()
                    .map(projection -> {
                        MultiSalesChannelMappingsResponse response = new MultiSalesChannelMappingsResponse();
                        response.setMerchantId(projection.getMerchantId());
                        response.setFootwayVariantId(projection.getFootwayVariantId());
                        response.setIntegrationVariantId(projection.getIntegrationVariantId());
                        response.setMscMappingId(projection.getMscMappingId());
                        
                        // Handle the footwayProduct concatenation in code instead of SQL
                        if (projection.getSize() != null) {
                            response.setFootwayProduct(projection.getProductName() + " - " + projection.getSize());
                        } else {
                            response.setFootwayProduct(projection.getProductName());
                        }
                        
                        if (projection.getMscEan() != null) {
                            response.setMscEan(List.of(projection.getMscEan()));
                        }
                        response.setFootwayImageUrl(projection.getFootwayImageUrl());
                        response.setIntegrationProduct(projection.getIntegrationProduct());
                        response.setProductId(projection.getProductId());
                        response.setEan(projection.getEan());
                        response.setIntegrationImageUrl(projection.getIntegrationImageUrl());
                        return response;
                    })
                    .toList();

            KeySetPaginatedMappingsResponse response = new KeySetPaginatedMappingsResponse();
            response.setItems(mappingResponses);
            response.setHasMore(resultsSlice.hasNext());
            
            // Set next cursor based on the last result if there are any results
            if (!results.isEmpty()) {
                MultiSalesChannelMappingsProjection last = results.get(results.size() - 1);
                KeySetCursor nextCursor = new KeySetCursor();
                nextCursor.setProductName(last.getProductName());
                nextCursor.setVariantId(last.getFootwayVariantId());
                response.setNextCursor(nextCursor);
                
                // Set prev cursor based on the first result
                MultiSalesChannelMappingsProjection first = results.get(0);
                KeySetCursor prevCursor = new KeySetCursor();
                prevCursor.setProductName(first.getProductName());
                prevCursor.setVariantId(first.getFootwayVariantId());
                response.setPrevCursor(prevCursor);
            }
            
            return response;
        } catch (Exception e) {
            log.error("Error fetching multi sales channel variant mappings with keyset pagination: ", e);
            return null;
        }
    }
}