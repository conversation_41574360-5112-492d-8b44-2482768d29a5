package com.footway.inventory.service.filter;

import com.footway.inventory.model.dto.*;
import com.footway.inventory.service.filter.dto.AllCounterContainer;
import com.footway.inventory.service.filter.dto.FilterMap;
import jakarta.validation.Valid;

import java.util.ArrayList;
import java.util.Map;

public class AvailableFilterHelper {
    private final AllCounterContainer allCounters;
    private final AvailableFilters availableFilters = new AvailableFilters();

    public AvailableFilterHelper(AllCounterContainer allCounters) {
        this.allCounters = allCounters;
    }

    public AvailableFilters toAvailableFiltersComplete(FilterMap currentFilters) {
        Map<FilterType, FilterMap> permutations = currentFilters.createPermutations(allCounters);
        for (Map.Entry<FilterType, FilterMap> entry : permutations.entrySet()) {
            fillType(entry.getKey(), entry.getValue());
        }
        availableFilters.setTotalItems(allCounters.totalCount());
        return availableFilters;
    }

    private void fillType(FilterType type, FilterMap filters) {
        Map<FilterType, Map<String, Integer>> counts = allCounters.count(filters);
        Map<String, Integer> typeCounts = counts.get(type);
        for (Map.Entry<String, Integer> valueEntry : typeCounts.entrySet()) {
            if (valueEntry.getValue() != 0) {
                updateAvailableFilters(type, valueEntry.getKey(), valueEntry.getValue());
            }
        }
    }


    public AvailableFilters toAvailableFilters(FilterMap currentFilters) {
        Map<FilterType, Map<String, Integer>> counts = allCounters.count(currentFilters);
        for (Map.Entry<FilterType, Map<String, Integer>> typeEntry : counts.entrySet()) {
            for (Map.Entry<String, Integer> valueEntry : typeEntry.getValue().entrySet()) {
                if (valueEntry.getValue() != 0) {
                    updateAvailableFilters(typeEntry.getKey(), valueEntry.getKey(), valueEntry.getValue());
                }
            }
        }
        availableFilters.setTotalItems(allCounters.totalCount());

        return availableFilters;
    }

    private void updateAvailableFilters(FilterType type, String value, int count) {
        switch (type) {
            case merchantId -> {
                merchant().getValues().add(new AvailableFiltersMerchantsValuesInner().name(value).count(count).id(value));
                merchant().setTotal(merchant().getTotal() + count);
            }
            case vendor -> {
                vendor().getValues().add(new AvailableFiltersVendorsValuesInner().name(value).count(count));
                vendor().setTotal(vendor().getTotal() + count);
            }
            case department -> {
                department().getValues().add(new AvailableFiltersDepartmentsValuesInner().name(value).count(count));
                department().setTotal(department().getTotal() + count);
            }
            case productGroup -> {
                productGroup().getValues().add(new AvailableFiltersProductGroupsValuesInner().name(value).count(count));
                productGroup().setTotal(productGroup().getTotal() + count);
            }
            case productType -> {
                productType().getValues().add(new AvailableFiltersProductTypesValuesInner().name(value).count(count));
                productType().setTotal(productType().getTotal() + count);
            }
        }
    }

    private @Valid AvailableFiltersMerchants merchant() {
        if (availableFilters.getMerchants() == null)
            availableFilters.setMerchants(new AvailableFiltersMerchants().total(0));
        @Valid AvailableFiltersMerchants next = availableFilters.getMerchants();
        if (next.getValues() == null) {
            next.setValues(new ArrayList<>());
        }
        return next;
    }

    private @Valid AvailableFiltersVendors vendor() {
        if (availableFilters.getVendors() == null)
            availableFilters.setVendors(new AvailableFiltersVendors().total(0));
        @Valid AvailableFiltersVendors next = availableFilters.getVendors();
        if (next.getValues() == null) {
            next.setValues(new ArrayList<>());
        }
        return next;
    }

    private @Valid AvailableFiltersDepartments department() {
        if (availableFilters.getDepartments() == null)
            availableFilters.setDepartments(new AvailableFiltersDepartments().total(0));
        @Valid AvailableFiltersDepartments next = availableFilters.getDepartments();
        if (next.getValues() == null) {
            next.setValues(new ArrayList<>());
        }
        return next;
    }

    private @Valid AvailableFiltersProductGroups productGroup() {
        if (availableFilters.getProductGroups() == null)
            availableFilters.setProductGroups(new AvailableFiltersProductGroups().total(0));
        @Valid AvailableFiltersProductGroups next = availableFilters.getProductGroups();
        if (next.getValues() == null) {
            next.setValues(new ArrayList<>());
        }
        return next;
    }

    private @Valid AvailableFiltersProductTypes productType() {
        if (availableFilters.getProductTypes() == null)
            availableFilters.setProductTypes(new AvailableFiltersProductTypes().total(0));
        @Valid AvailableFiltersProductTypes next = availableFilters.getProductTypes();
        if (next.getValues() == null) {
            next.setValues(new ArrayList<>());
        }
        return next;
    }
}
