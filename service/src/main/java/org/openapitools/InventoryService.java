package org.openapitools;

import com.fasterxml.jackson.databind.Module;
import com.footway.fop.shared.webhook.configuration.CallbackConfig;
import com.footway.fop.shared.webhook.service.CallbackService;
import org.openapitools.jackson.nullable.JsonNullableModule;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.FilterType;
import org.springframework.context.annotation.FullyQualifiedAnnotationBeanNameGenerator;
import org.springframework.scheduling.annotation.EnableAsync;

@SpringBootApplication(
        nameGenerator = FullyQualifiedAnnotationBeanNameGenerator.class
)
@EnableAsync
@ComponentScan(
        basePackages = {"org.openapitools", "com.footway.inventory",
                "com.footway.util",
                "com.footway.fop.shared"},
        nameGenerator = FullyQualifiedAnnotationBeanNameGenerator.class,
        excludeFilters = @ComponentScan.Filter(type = FilterType.ASSIGNABLE_TYPE, classes = {CallbackConfig.class, CallbackService.class})
)
@EntityScan("com.footway.inventory.model")
public class InventoryService {

    public static void main(String[] args) {
        SpringApplication.run(InventoryService.class, args);
    }

    @Bean(name = "org.openapitools.OpenApiGeneratorApplication.jsonNullableModule")
    public Module jsonNullableModule() {
        return new JsonNullableModule();
    }
}