# Stage 1: Build Stage
FROM maven:3.8.4-amazoncorretto-17 as build-stage
ENV GOOGLE_APPLICATION_CREDENTIALS=/app/service-account-key.json

WORKDIR /app
COPY service-account-key.json service-account-key.json
COPY . /app
RUN mvn install -pl shared,service -am -DskipTests

# Stage 2: Runtime Stage
# Use a smaller JRE base image for running the application
FROM amazoncorretto:17

# Set the working directory in the runtime stage
WORKDIR /app

# Copy the built jar from the build stage to the runtime stage
COPY --from=build-stage /app/service/target/*.jar /app/service.jar

# Expose the port the application runs on
EXPOSE 8080

# Set the entry point to run the application
ENTRYPOINT ["java", "-jar", "/app/service.jar"]
